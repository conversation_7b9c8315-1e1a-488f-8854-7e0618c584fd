@echo off
chcp 65001 >nul
title 🤖 نظام سحب الكتب مع التليجرام

echo.
echo ========================================
echo 🚀 نظام سحب الكتب من FoulaBook
echo 🤖 مع دعم رفع التليجرام
echo ========================================
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

:: التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر! يرجى تثبيت pip أولاً
    pause
    exit /b 1
)

:: تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt --quiet --disable-pip-version-check

if errorlevel 1 (
    echo ⚠️ تعذر تثبيت بعض المتطلبات، لكن سنحاول المتابعة...
)

:: إنشاء المجلدات المطلوبة
if not exist "downloads" mkdir downloads
if not exist "scraped" mkdir scraped
if not exist "published" mkdir published
if not exist "templates" mkdir templates

:: التحقق من وجود ملفات النظام
if not exist "server.py" (
    echo ❌ ملف server.py غير موجود!
    pause
    exit /b 1
)

if not exist "telegram_bot.py" (
    echo ❌ ملف telegram_bot.py غير موجود!
    pause
    exit /b 1
)

if not exist "templates\index.html" (
    echo ❌ ملف templates\index.html غير موجود!
    pause
    exit /b 1
)

echo.
echo 🔧 خيارات التشغيل:
echo 1. تشغيل السيرفر مباشرة
echo 2. اختبار التليجرام أولاً
echo 3. إعداد التليجرام التفاعلي
echo 4. عرض دليل ربط التليجرام
echo.

set /p choice="اختر رقماً (1-4): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto test_telegram
if "%choice%"=="3" goto setup_telegram
if "%choice%"=="4" goto show_guide

:start_server
echo.
echo 🚀 تشغيل السيرفر...
echo 🌐 ستفتح الواجهة على: http://localhost:5000
echo 📱 للوصول للإعدادات: اضغط على أيقونة الإعدادات ⚙️
echo.
echo ⚠️ لإيقاف السيرفر: اضغط Ctrl+C
echo.
python server.py
goto end

:test_telegram
echo.
echo 🧪 اختبار التليجرام...
python test_telegram.py
pause
goto start_server

:setup_telegram
echo.
echo 🛠️ إعداد التليجرام التفاعلي...
python test_telegram.py --setup
pause
goto start_server

:show_guide
echo.
echo 📖 عرض دليل ربط التليجرام...
if exist "دليل-ربط-التليجرام.md" (
    type "دليل-ربط-التليجرام.md"
) else (
    echo ❌ ملف الدليل غير موجود!
)
echo.
pause
goto start_server

:end
echo.
echo 👋 شكراً لاستخدام نظام سحب الكتب!
pause
