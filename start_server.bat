@echo off
chcp 65001 > nul
title FoulaBook Scraper - تشغيل سريع

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ⚡ تشغيل سريع للسيرفر                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: إنشاء المجلدات بسرعة
if not exist "scraped_articles" mkdir scraped_articles > nul 2>&1
if not exist "published_articles" mkdir published_articles > nul 2>&1
if not exist "logs" mkdir logs > nul 2>&1

echo 🚀 تشغيل السيرفر...
echo 💻 http://localhost:5000
echo.

:: فتح المتصفح في الخلفية
start "" http://localhost:5000

:: تشغيل السيرفر
python server.py

pause
