@echo off
chcp 65001 > nul
title اختبار سيرفر FoulaBook Scraper

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧪 اختبار السيرفر                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص متطلبات الاختبار...

:: فحص Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص مكتبة requests
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 تثبيت مكتبة requests...
    pip install requests
)

echo ✅ مكتبات الاختبار متوفرة

echo.
echo 🧪 بدء اختبار السيرفر...
echo 💡 تأكد من تشغيل السيرفر في نافذة أخرى
echo.

python test_server.py

echo.
pause
