#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام سحب البيانات من موقع FoulaBook.com
FoulaBook.com Data Scraper System

هذا السكريبت يقوم بسحب بيانات الكتب من موقع foulabook.com
وتحويلها إلى تنسيق متوافق مع قالب اقرأ كتاب
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
import os
from urllib.parse import urljoin, urlparse
import logging

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('foulabook_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class FoulaBookScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def extract_book_data(self, url):
        """
        استخراج بيانات الكتاب من رابط foulabook.com
        """
        try:
            logging.info(f"جاري سحب البيانات من: {url}")
            
            # إرسال طلب GET للصفحة
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # تحليل HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # استخراج البيانات
            book_data = self._parse_book_page(soup, url)
            
            logging.info("تم استخراج البيانات بنجاح")
            return book_data
            
        except requests.RequestException as e:
            logging.error(f"خطأ في الشبكة: {e}")
            raise
        except Exception as e:
            logging.error(f"خطأ في استخراج البيانات: {e}")
            raise
    
    def _parse_book_page(self, soup, url):
        """
        تحليل صفحة الكتاب واستخراج البيانات
        """
        book_data = {}
        
        try:
            # استخراج العنوان
            title_element = soup.find('h1') or soup.find('title')
            if title_element:
                book_data['title'] = self._clean_text(title_element.get_text())
            
            # استخراج صورة الكتاب
            img_element = soup.find('img', {'alt': True})
            if img_element and img_element.get('src'):
                book_data['image'] = urljoin(url, img_element['src'])
            
            # استخراج المعلومات من النص
            page_text = soup.get_text()
            
            # استخراج المؤلف
            author_match = re.search(r'المؤلف\s*:?\s*([^\n]+)', page_text)
            if author_match:
                book_data['author'] = self._clean_text(author_match.group(1))
            
            # استخراج اللغة
            language_match = re.search(r'اللغة\s*:?\s*([^\n]+)', page_text)
            if language_match:
                book_data['language'] = self._clean_text(language_match.group(1))
            
            # استخراج التصنيف
            category_match = re.search(r'اﻟﺘﺼﻨﻴﻒ\s*:?\s*([^\n]+)', page_text)
            if category_match:
                book_data['category'] = self._clean_text(category_match.group(1))
            
            # استخراج الفئة
            subcategory_match = re.search(r'الفئة\s*:?\s*([^\n]+)', page_text)
            if subcategory_match:
                book_data['subcategory'] = self._clean_text(subcategory_match.group(1))
            
            # استخراج عدد الصفحات
            pages_match = re.search(r'عدد الصفحات\s*:?\s*(\d+)', page_text)
            if pages_match:
                book_data['pages'] = pages_match.group(1)
            
            # استخراج حجم الملف
            size_match = re.search(r'الحجم\s*:?\s*([^\n]+)', page_text)
            if size_match:
                book_data['size'] = self._clean_text(size_match.group(1))
            
            # استخراج نوع الملف
            format_match = re.search(r'نوع الملف\s*:?\s*([^\n]+)', page_text)
            if format_match:
                book_data['format'] = self._clean_text(format_match.group(1))
            
            # استخراج عدد التحميلات
            downloads_match = re.search(r'عدد التحميلات\s*:?\s*(\d+)', page_text)
            if downloads_match:
                book_data['downloads'] = downloads_match.group(1)
            
            # استخراج الوصف
            description = self._extract_description(soup)
            if description:
                book_data['description'] = description
            
            # استخراج رابط التحميل
            download_link = self._extract_download_link(soup, url)
            if download_link:
                book_data['download_link'] = download_link
            
            # إضافة معلومات إضافية
            book_data['source_url'] = url
            book_data['scraped_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
            
            return book_data
            
        except Exception as e:
            logging.error(f"خطأ في تحليل الصفحة: {e}")
            raise
    
    def _extract_description(self, soup):
        """
        استخراج وصف الكتاب
        """
        # البحث عن قسم "لمحة عن الكتاب"
        description_section = soup.find(text=re.compile(r'لمحة عن الكتاب'))
        if description_section:
            parent = description_section.parent
            if parent:
                # البحث عن النص التالي
                next_elements = parent.find_next_siblings()
                for element in next_elements:
                    text = self._clean_text(element.get_text())
                    if len(text) > 50:  # تأكد من أن النص طويل بما فيه الكفاية
                        return text
        
        # طريقة بديلة: البحث عن أطول فقرة
        paragraphs = soup.find_all('p')
        longest_paragraph = ""
        for p in paragraphs:
            text = self._clean_text(p.get_text())
            if len(text) > len(longest_paragraph):
                longest_paragraph = text
        
        return longest_paragraph if len(longest_paragraph) > 100 else None
    
    def _extract_download_link(self, soup, base_url):
        """
        استخراج رابط التحميل
        """
        # البحث عن روابط التحميل
        download_links = soup.find_all('a', href=True)
        for link in download_links:
            href = link['href']
            text = link.get_text().lower()
            
            # البحث عن كلمات مفتاحية للتحميل
            if any(keyword in text for keyword in ['تحميل', 'download', 'downloading']):
                return urljoin(base_url, href)
            
            # البحث في الرابط نفسه
            if any(keyword in href.lower() for keyword in ['download', 'file', 'pdf']):
                return urljoin(base_url, href)
        
        return None
    
    def _clean_text(self, text):
        """
        تنظيف النص من المسافات الزائدة والأحرف غير المرغوب فيها
        """
        if not text:
            return ""
        
        # إزالة المسافات الزائدة والأسطر الجديدة
        text = re.sub(r'\s+', ' ', text.strip())
        
        # إزالة الأحرف الخاصة غير المرغوب فيها
        text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF.,!?():-]', '', text)
        
        return text.strip()
    
    def generate_iqraa_article(self, book_data, copyright_protected=False):
        """
        توليد مقالة متوافقة مع قالب اقرأ كتاب
        """
        try:
            # تحضير البيانات مع دعم التحديثات اليدوية
            title = book_data.get('title', 'عنوان غير محدد')
            author = book_data.get('author', 'مؤلف غير محدد')
            series = book_data.get('series', 'لا توجد')
            image = book_data.get('image', 'https://via.placeholder.com/300x400/007bff/ffffff?text=كتاب')
            category = book_data.get('category', 'غير محدد')
            language = book_data.get('language', 'العربية')
            pages = book_data.get('pages', 'غير محدد')
            edition = book_data.get('edition', 'غير محدد')
            publisher = book_data.get('publisher', 'غير محدد')
            year = book_data.get('year', 'غير محدد')
            rating = book_data.get('rating', '5/5')
            size = book_data.get('size', 'غير محدد')
            format_type = book_data.get('format', 'PDF')
            description = book_data.get('description', 'وصف غير متوفر')
            download_link = book_data.get('downloadLink', book_data.get('download_link', 'https://ikitab.iqraatech.net'))
            
            # إضافة خاصية حماية حقوق الطبع والنشر
            copyright_attr = ' data-copyright="protected"' if copyright_protected else ''
            
            # بناء المقالة مع البيانات الديناميكية
            article = f'''<div class="ibookinfobox">
    <div class="iBICover"{copyright_attr}><img alt="{title}" border="0" data-original-height="800" data-original-width="512" height="100%" src="{image}" title="{title}" width="100%" /></div>
  <div class="iBIDetails">
    <div class="iBItable iBName"><span class="ititle"><b>الكتاب:</b></span> {title}<br /></div>
    <div class="iBItable iBAuthor"><p><span class="ititle"><b>المؤلف:</b> {author}</span></p></div>
    <div class="iBItable iBSeries"><span class="ititle"><b>السلسلة:</b></span> {series}</div>
    <div class="iBItable iBLang"><span class="ititle"><b>اللغة:</b></span> {language}</div>
    <div class="iBItable iBPages"><span class="ititle"><b>الصفحات:</b></span> {pages} صفحة<br /></div>
    <div class="iBItable iBEdition"><span class="ititle"><b>الطبعة:</b></span> {edition}</div>
    <div class="iBItable iBPublisher"><span class="ititle"><b>دار النشر:</b></span> {publisher}</div>
    <div class="iBItable iBYear"><span class="ititle"><b>سنة النشر:</b></span> {year}</div>
    <div class="iBItable iBRate"><p><span class="ititle"><b>تقييم جود ريدز:</b> {rating}</span></p></div>
    <div class="iBItable iBType"><span class="ititle"><b>الصيغة:</b></span> {format_type}</div>
    <div class="iBItable iBSize"><span class="ititle"><b>حجم الملف:</b></span> {size}</div>
  </div>
</div>
<br />
<span><!--more--></span>
<div class="iqraa-message info imtitle iAboutB">
  <h2 style="text-align: right;">نبذة عن الكتاب</h2>
  <p>{description}</p>
</div>
<br />
<button class="iqraacoll iBIndex" type="button"><svg class="iBIndexbefore" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13.744 8s1.522-8-3.335-8h-8.409v24h20v-13c0-3.419-5.247-3.745-8.256-3zm4.256 11h-12v-1h12v1zm0-3h-12v-1h12v1zm0-3h-12v-1h12v1zm-3.432-12.925c2.202 1.174 5.938 4.883 7.432 6.881-1.286-.9-4.044-1.657-6.091-1.179.222-1.468-.185-4.534-1.341-5.702z"></path></svg><h2>فهرس الكتاب</h2><svg class="iBIndexafter" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"></path></svg></button><div class="collcontent iBIndexdetail">
  <p>فهرس الكتاب:<br /></p>
  <ol style="text-align: right;"><li>المقدمة</li><li>الفصل الأول</li><li>الفصل الثاني</li><li>الفصل الثالث</li><li>الخاتمة</li></ol>
</div>
<br />
<div class="iqraa-message info imtitle bookauthor">
  <h2 style="text-align: right;">معلومات عن الكاتب</h2>
  <div class="iALAvatarImage"><img class="iALAvatar" alt="صورة مؤلف الكتاب" src="https://lh3.googleusercontent.com/--NHUHORGgKI/Y8sDAvqyVhI/AAAAAAAAAwg/NE5pqiucNjEtjYgWDVlI9rarSKL-XdFHACNcBGAsYHQ/s16000-rw-e360/iAuthorAvatar-1.webp" title="صورة مؤلف الكتاب" /></div>
  <p>معلومات عن الكاتب {author} ومسيرته العلمية والأدبية.</p>
  <a class="bookauthorBTN button" href="#">الكتب الأخرى للكاتب</a>
</div>
<br />
<h2 style="text-align: right;">أفكار واقتباسات من الكتاب</h2>
<p>هذه مجموعة من الأفكار والمعلومات التي تم اقتباسها من الكتاب:</p><p></p>
<blockquote>اقتباس تجريبي من الكتاب يعبر عن محتواه وأفكاره الرئيسية.</blockquote><p></p>
<blockquote>اقتباس آخر يوضح أسلوب الكاتب وطريقة عرضه للأفكار.</blockquote><p></p>'''

            # إضافة أزرار التحميل أو رسالة حقوق الطبع والنشر
            if copyright_protected:
                article += '''
<!-- رسالة حماية حقوق الطبع والنشر -->
<div class="iqraa-message warning copyright-notice" style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); border: 2px solid #dc3545; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
    <h3 style="color: #dc3545; margin-bottom: 15px; font-size: 22px; font-weight: bold;">⚠️ تنبيه حقوق الطبع والنشر</h3>
    <p style="color: #856404; margin-bottom: 15px; line-height: 1.8; font-size: 16px;">
        <strong>نعتذر، هذا الكتاب غير متاح حاليًا للتحميل أو القراءة لأن المؤلف أو الناشر لا يسمح بذلك في الوقت الحالي.</strong>
    </p>
    <p style="color: #856404; margin-bottom: 15px; line-height: 1.6; font-size: 14px;">
        نحن نحترم حقوق الملكية الفكرية ونلتزم بقوانين حقوق الطبع والنشر.
    </p>
    <div style="background: rgba(220, 53, 69, 0.1); padding: 15px; border-radius: 8px; margin-top: 15px;">
        <h4 style="color: #dc3545; margin-bottom: 10px; font-size: 16px;">📚 يمكنك الحصول على هذا الكتاب من خلال:</h4>
        <ul style="color: #856404; text-align: right; margin: 0; padding-right: 20px; line-height: 1.8;">
            <li>شراء نسخة ورقية من المكتبات</li>
            <li>المكتبات العامة والجامعية</li>
            <li>التواصل مع الناشر مباشرة</li>
            <li>المتاجر الإلكترونية المعتمدة</li>
        </ul>
    </div>
</div>'''
            else:
                article += f'''
<div class="isdbtn button download" id="isdbtn">تحميل الكتاب</div>
<div class="idownloadmodal" id="idownloadmodal">
  <div class="idownloadmodalcontainer">
    <div class="idownloadmodaltop"><div class="idownloadmodalCloseBTN" id="CloseBTN"></div><div class="idownloadmodaltitle">تحميل الكتاب</div></div>
    <div id="ipdAd"></div>
    <div class="inowdownloadinner">
      <a class="button download" href="{download_link}" id="inowdownload" target="_blank">أضغط لتحميل الكتاب</a>
    </div>
  </div>
</div>'''

            article += '''
<br />
<br />
<div class="iBLikeMessage"><svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="m12 5.72c-2.624-4.517-10-3.198-10 2.461 0 3.725 4.345 7.727 9.303 *************.446.283.697.283s.503-.094.697-.283c4.977-4.831 9.303-8.814 9.303-12.54 0-5.678-7.396-6.944-10-2.461z"></path></svg><p>إذا كان الكتاب قد نال إعجابكم فلا تترددوا في الضغط على زر القلب الموجود في الأسفل</p><svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="m12 5.72c-2.624-4.517-10-3.198-10 2.461 0 3.725 4.345 7.727 9.303 *************.446.283.697.283s.503-.094.697-.283c4.977-4.831 9.303-8.814 9.303-12.54 0-5.678-7.396-6.944-10-2.461z"></path></svg></div>'''

            # إضافة السكريبت إذا لم يكن محمي بحقوق الطبع والنشر
            if not copyright_protected:
                article += '''

<!--[ أكواد خاصة بأداة التحميل العائم يجب عدم التلاعب بها لضمان عمل الأداة بشكل سليم ]-->
<script>/*<![CDATA[*/var _0x53b5e1=_0x568f;function _0x568f(t,n){var r=_0x34a4();return _0x568f=function(n,e){var a=r[n-=422];if(void 0===_0x568f.pKfuWp){var f=function(t){for(var n,r,e="",a="",i=e+f,o=0,c=0;r=t.charAt(c++);~r&&(n=o%4?64*n+r:r,o++%4)?e+=i.charCodeAt(c+10)-10!=0?String.fromCharCode(255&n>>(-2*o&6)):o:0)r="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=".indexOf(r);for(var u=0,s=e.length;u<s;u++)a+="%"+("00"+e.charCodeAt(u).toString(16)).slice(-2);return decodeURIComponent(a)};_0x568f.NFBMpM=f,t=arguments,_0x568f.pKfuWp=!0}var i=r[0],o=n+i,c=t[o];if(c)a=c;else{var u=function(t){this.YoeVLP=t,this.IVrfrZ=[1,0,0],this.SFXOoi=function(){return"newState"},this.MOsbRF="\\w+ *\\(\\) *{\\w+ *",this.CWpKlI="['|\"].+['|\"];? *}"};u.prototype.pSVDLb=function(){var t=new RegExp(this.MOsbRF+this.CWpKlI).test(this.SFXOoi.toString())?--this.IVrfrZ[1]:--this.IVrfrZ[0];return this.gWqApy(t)},u.prototype.gWqApy=function(t){return Boolean(~t)?this.wvAjFF(this.YoeVLP):t},u.prototype.wvAjFF=function(t){for(var n=0,r=this.IVrfrZ.length;n<r;n++)this.IVrfrZ.push(Math.round(Math.random())),r=this.IVrfrZ.length;return t(this.IVrfrZ[0])},new u(_0x568f).pSVDLb(),a=_0x568f.NFBMpM(a),t[o]=a}return a},_0x568f(t,n)}!function(t,n){for(var r=_0x568f,e=_0x34a4();;)try{if(457417===-parseInt(r(427))/1+-parseInt(r(433))/2+-parseInt(r(443))/3+-parseInt(r(449))/4*(parseInt(r(441))/5)+-parseInt(r(425))/6+parseInt(r(445))/7+parseInt(r(438))/8)break;e.push(e.shift())}catch(t){e.push(e.shift())}}();var _0x2cf5c2=function(){var t=!0;return function(n,r){var e=t?function(){var t=_0x568f;if(r){var e=r[t(436)](n,arguments);return r=null,e}}:function(){};return t=!1,e}}(),_0x1fc1ec=_0x2cf5c2(this,(function(){var t=_0x568f;return _0x1fc1ec[t(446)]()[t(426)](t(437))[t(446)]()[t(431)](_0x1fc1ec).search("(((.+)+)+)+$")}));_0x1fc1ec();var lazyiPDBtn=!1;function _0x34a4(){var t=["B25JBgLJAW","mta2odK5nhH3BuzXAq","Aw5Uzxjive1m","yM9KEq","yxbWBhK","kcGOlISPkYKRksSK","mJiXnJK4mZjqyLLmAxy","zg9JDw1LBNrfBgvTzw50","mtzWEa","nwnoDgPcEa","AxnKyNrU","mtCYodKXmKLhshbpwa","icdyQ9IN2yBzITIPlI4UlIa","nZu2mtqWDKHfyKLe","Dg9tDhjPBMC","2lpzITIQ2yuG2kFzHnII2yyG2kxyUDIV2kFyRYdyP9Me2yxzHnMb","y3jLyxrLrwXLBwvUDa","mZaZmZe4oen4Bw1Asq","CgfYzw50tM9Kzq","ywrKrxzLBNrmAxn0zw5LCG","C3r5Bgu","C2nYB2XSvg9W","z2v0rwXLBwvUDej5swq","C2nYB2XS","C3bHBG","mJeWmde2ogTMve5PsW","C2vHCMnO","mJaYnZa2BK5tA0f1","2yxzHIdzGDI22ytzGYdyP9Mg2kRyUnIXica","CMvWBgfJzunOAwXK","zM9UDa","y29UC3rYDwn0B3i"];return(_0x34a4=function(){return t})()}window[_0x53b5e1(451)](_0x53b5e1(423),(function(){var t,n,r,e,a=_0x53b5e1;(0!=document[a(439)][a(453)]&&!1===lazyiPDBtn||0!=document[a(435)][a(453)]&&!1===lazyiPDBtn)&&(n=document[a(422)]("inowdownload"),r=ipdseconds,(e=document[a(448)](a(424)))[a(434)]=a(447),e[a(452)][a(430)]=a(440),n[a(450)][a(429)](e,n),document.getElementById(a(442))[a(432)]=function(){t=setInterval((function(){var a=_0x568f;--r<0?(e[a(450)][a(429)](n,e),clearInterval(t)):e[a(434)]=a(428)+r.toString()+a(444)}),1e3)},lazyiPDBtn=!0)}),!0);/*]]>*/</script>'''

            return article
            
        except Exception as e:
            logging.error(f"خطأ في توليد المقالة: {e}")
            raise
    
    def save_data(self, book_data, filename=None):
        """
        حفظ البيانات في ملف JSON
        """
        if not filename:
            # إنشاء اسم ملف من عنوان الكتاب
            title = book_data.get('title', 'unknown_book')
            # تنظيف اسم الملف
            filename = re.sub(r'[^\w\s-]', '', title)[:50] + '.json'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(book_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"تم حفظ البيانات في: {filename}")
            return filename
            
        except Exception as e:
            logging.error(f"خطأ في حفظ البيانات: {e}")
            raise

def main():
    """
    الدالة الرئيسية لتشغيل السكريبت
    """
    # مثال على الاستخدام
    scraper = FoulaBookScraper()
    
    # رابط الكتاب المراد سحب بياناته
    book_url = "https://foulabook.com/ar/book/%D8%A3%D9%84%D9%81-%D9%84%D9%8A%D9%84%D8%A9-%D9%88%D9%84%D9%8A%D9%84%D8%A9-%D9%86%D8%B3%D8%AE%D8%A9-%D8%A3%D8%B5%D9%84%D9%8A%D8%A9-%D9%86%D8%A7%D8%AF%D8%B1%D8%A9-pdf"
    
    try:
        # سحب البيانات
        book_data = scraper.extract_book_data(book_url)
        
        # طباعة البيانات
        print("البيانات المستخرجة:")
        print(json.dumps(book_data, ensure_ascii=False, indent=2))
        
        # حفظ البيانات
        scraper.save_data(book_data)
        
        # توليد مقالة القالب
        article = scraper.generate_iqraa_article(book_data, copyright_protected=False)
        
        # حفظ المقالة
        with open('generated_article.html', 'w', encoding='utf-8') as f:
            f.write(article)
        
        print("\nتم توليد المقالة وحفظها في: generated_article.html")
        
    except Exception as e:
        logging.error(f"خطأ في تشغيل السكريبت: {e}")

if __name__ == "__main__":
    main()
