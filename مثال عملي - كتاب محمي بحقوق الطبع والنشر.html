<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>مثال: كتاب محمي بحقوق الطبع والنشر</title>
</head>
<body>

<!-- مثال 1: كتاب محمي في قائمة الكتب -->
<div class="index-post">
    <div class="iPostThumbWrap" data-copyright="protected">
        <a class="iPostThumbLink" href="#" title="كتاب محمي">
            <img class="postthumb" src="https://via.placeholder.com/300x400/dc3545/ffffff?text=كتاب+محمي" alt="كتاب محمي"/>
        </a>
    </div>
    <div class="iPostContent">
        <h2 class="iPostTitle">
            <a href="#" title="كتاب محمي بحقوق الطبع والنشر">كتاب محمي بحقوق الطبع والنشر</a>
        </h2>
        <div class="iPostMeta">
            <span class="iPostAuthor">المؤلف: أحمد محمد</span>
            <span class="iPostDate">تاريخ النشر: 2024</span>
        </div>
        <div class="iPostSnippet">
            هذا مثال على كتاب محمي بحقوق الطبع والنشر. عند النقر على الصورة أو أزرار التحميل، ستظهر رسالة تنبيه.
        </div>
        <div class="iPostButtons">
            <button class="isdbtn">تحميل الكتاب</button>
            <button class="iqraarbbtn">قراءة الكتاب</button>
        </div>
    </div>
</div>

<!-- مثال 2: صفحة كتاب محمي -->
<article class="post">
    <div class="post-body">
        <div class="ibookinfobox">
            <div class="iBICover" data-copyright="protected">
                <img alt="كتاب الأدب العربي الحديث" src="https://via.placeholder.com/250x350/dc3545/ffffff?text=كتاب+محمي" title="كتاب الأدب العربي الحديث"/>
            </div>
            <div class="iBIDetails">
                <div class="iBItable iBName">
                    <span class="ititle"><b>الكتاب:</b></span> الأدب العربي الحديث
                </div>
                <div class="iBItable iBAuthor">
                    <span class="ititle"><b>المؤلف:</b></span> د. محمد عبدالله
                </div>
                <div class="iBItable iBPublisher">
                    <span class="ititle"><b>الناشر:</b></span> دار المعرفة
                </div>
                <div class="iBItable iBYear">
                    <span class="ititle"><b>سنة النشر:</b></span> 2023
                </div>
                <div class="iBItable iBPages">
                    <span class="ititle"><b>عدد الصفحات:</b></span> 450 صفحة
                </div>
                <div class="iBItable iBLanguage">
                    <span class="ititle"><b>اللغة:</b></span> العربية
                </div>
                <div class="iBItable iBFormat">
                    <span class="ititle"><b>الصيغة:</b></span> PDF
                </div>
                <div class="iBItable iBSize">
                    <span class="ititle"><b>حجم الملف:</b></span> غير متاح
                </div>
            </div>
        </div>

        <div class="post-content">
            <h3>وصف الكتاب</h3>
            <p>
                هذا الكتاب يتناول تطور الأدب العربي في العصر الحديث، ويستعرض أهم الأعمال والكتاب الذين أثروا في المشهد الأدبي العربي.
            </p>
            
            <div class="copyright-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="color: #856404; margin-bottom: 10px;">⚠️ تنبيه حقوق الطبع والنشر</h4>
                <p style="color: #856404; margin: 0;">
                    هذا الكتاب محمي بحقوق الطبع والنشر. لا يمكن تحميله أو قراءته إلكترونياً في الوقت الحالي احتراماً لحقوق المؤلف والناشر.
                </p>
            </div>

            <h3>معلومات إضافية</h3>
            <p>
                يمكنك الحصول على هذا الكتاب من خلال:
            </p>
            <ul>
                <li>المكتبات العامة</li>
                <li>المكتبات الجامعية</li>
                <li>شراء نسخة ورقية من المكتبات</li>
                <li>التواصل مع الناشر مباشرة</li>
            </ul>
        </div>

        <!-- أزرار معطلة للكتاب المحمي -->
        <div class="iPostButtons" style="text-align: center; margin: 20px 0;">
            <button class="isdbtn" style="opacity: 0.6; cursor: not-allowed;" title="هذا الكتاب محمي بحقوق الطبع والنشر">
                📥 تحميل الكتاب
            </button>
            <button class="iqraarbbtn" style="opacity: 0.6; cursor: not-allowed;" title="هذا الكتاب محمي بحقوق الطبع والنشر">
                📖 قراءة الكتاب
            </button>
        </div>
    </div>
</article>

<!-- مثال 3: كتاب محمي في القائمة الجانبية -->
<div class="widget">
    <h3 class="widget-title">كتب مميزة</h3>
    <div class="widget-content">
        <div class="related-post">
            <div class="iPostThumbWrap" data-copyright="protected" style="position: relative;">
                <a class="iPostThumbLink" href="#" title="كتاب محمي">
                    <img class="postthumb" src="https://via.placeholder.com/150x200/dc3545/ffffff?text=محمي" alt="كتاب محمي"/>
                </a>
            </div>
            <div class="iPostContent">
                <h4><a href="#" title="كتاب محمي">تاريخ الحضارة الإسلامية</a></h4>
                <p>كتاب محمي بحقوق الطبع والنشر</p>
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط إضافية للمثال */
.index-post, .post {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.iPostThumbWrap {
    position: relative;
    display: inline-block;
    margin-bottom: 15px;
}

.iPostThumbWrap img {
    max-width: 200px;
    height: auto;
    border-radius: 5px;
}

.iPostButtons {
    margin-top: 15px;
}

.isdbtn, .iqraarbbtn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 5px;
    cursor: pointer;
}

.ibookinfobox {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.iBICover {
    position: relative;
}

.iBIDetails {
    flex: 1;
}

.iBItable {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.ititle {
    font-weight: bold;
    color: #333;
}

.widget {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.related-post {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.related-post .iPostThumbWrap img {
    max-width: 80px;
}
</style>

<script>
// محاكاة عمل الميزة للمثال
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أيقونات حقوق الطبع والنشر
    const protectedElements = document.querySelectorAll('[data-copyright="protected"]');
    protectedElements.forEach(function(element) {
        const icon = document.createElement('div');
        icon.style.cssText = `
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 8px;
            border-radius: 50%;
            font-size: 12px;
            z-index: 10;
            cursor: pointer;
        `;
        icon.innerHTML = '⚠️';
        icon.title = 'كتاب محمي بحقوق الطبع والنشر';
        element.appendChild(icon);
        
        // إضافة حدث النقر
        icon.addEventListener('click', function(e) {
            e.preventDefault();
            alert('نعتذر، هذا الكتاب غير متاح حاليًا للتحميل أو القراءة لأن المؤلف أو الناشر لا يسمح بذلك في الوقت الحالي.');
        });
    });
    
    // تعطيل الأزرار
    const buttons = document.querySelectorAll('.isdbtn, .iqraarbbtn');
    buttons.forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            alert('نعتذر، هذا الكتاب غير متاح حاليًا للتحميل أو القراءة لأن المؤلف أو الناشر لا يسمح بذلك في الوقت الحالي.');
        });
    });
});
</script>

</body>
</html>
