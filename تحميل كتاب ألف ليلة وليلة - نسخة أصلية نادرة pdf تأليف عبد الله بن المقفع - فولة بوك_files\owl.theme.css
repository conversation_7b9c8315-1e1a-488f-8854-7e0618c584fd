/*
* 	Owl Carousel Owl Demo Theme 
*	v1.3.3
*/

.owl-theme .owl-controls{
	margin-top: 16px;
	text-align: center;
}

/* Styling Next and Prev buttons */

.owl-theme .owl-controls .owl-buttons div{
	color: #292929;
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin: 5px;
	padding: 3px 10px;
    padding-top:4px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
	background: #FCFCFC;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 0.7;
    border: 1px solid rgb(221, 221, 221);
}
/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.owl-theme .owl-controls.clickable .owl-buttons div:hover{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	text-decoration: none;
}

/* Styling Pagination*/

.owl-theme .owl-controls .owl-page{
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
}
.owl-theme .owl-controls .owl-page span{
	display: block;
	width: 11px;
	height: 11px;
	margin: 5px 4px;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 0.5;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
    border: 1px solid rgb(31, 31, 31);
}

.owl-theme .owl-controls .owl-page.active span,
.owl-theme .owl-controls.clickable .owl-page:hover span{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
}

/* If PaginationNumbers is true */

.owl-theme .owl-controls .owl-page span.owl-numbers{
	height: auto;
	width: auto;
	color: #FFF;
	padding: 2px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
}

/* preloading images */
.owl-item.loading{
	min-height: 150px;
	background: url(AjaxLoader.gif) no-repeat center center
}

.owl-carousel .owl-item {
    padding: 0px 12px;
}
.carousel-wrap.no-padding .owl-item {
    padding: 0px 1px;
}
.carousel-wrap.no-padding {
    margin-left: 0px; 
    margin-right: 0px; 
}
.owl-carousel.no-padding .owl-item {
    padding: 0px 1px; 
}

.owl-carousel .owl-wrapper-outer {
    /*margin-left: -5px;*/
}

 
.owl-theme .owl-controls .owl-page.active span {
    width: 11px;
    height: 11px;
    border: 2px solid #555;
    border-radius: 50%;
}