/* 
 * أنماط CSS إضافية لميزة حماية حقوق الطبع والنشر
 * يمكن إضافة هذه الأنماط لتحسين مظهر الميزة
 */

/* أيقونة حقوق الطبع والنشر */
.icopyright-icon {
    position: absolute;
    display: none;
    top: 5px;
    right: 5px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    z-index: 2;
    color: var(--iqraa-white-color);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.icopyright-icon.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: copyrightPulse 2s infinite;
}

.icopyright-icon svg {
    width: 20px;
    height: 20px;
    fill: var(--iqraa-white-color);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.icopyright-icon:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* تأثير النبض للأيقونة */
@keyframes copyrightPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* نافذة التنبيه المنبثقة */
.icopyright-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.icopyright-modal.show {
    display: flex;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* محتوى النافذة المنبثقة */
.icopyright-content {
    background: var(--iqraa-pbcolor);
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    position: relative;
    border: 2px solid var(--iqraa-main-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.icopyright-content h3 {
    color: #dc3545;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icopyright-content p {
    color: var(--iqraa-btcolor);
    line-height: 1.8;
    margin-bottom: 20px;
    font-size: 16px;
}

.icopyright-content p:last-of-type {
    margin-bottom: 0;
    font-size: 14px;
    opacity: 0.8;
}

/* زر الإغلاق */
.icopyright-close {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.icopyright-close:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* تأثيرات على الكتب المحمية */
[data-copyright="protected"] {
    position: relative;
}

[data-copyright="protected"]::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(220, 53, 69, 0.1) 50%, transparent 70%);
    pointer-events: none;
    z-index: 1;
    border-radius: inherit;
}

/* تأثيرات على الأزرار المعطلة */
.isdbtn[data-protected="true"],
.iqraarbbtn[data-protected="true"],
.button.download[data-protected="true"],
.button.read[data-protected="true"] {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    position: relative;
    overflow: hidden;
}

.isdbtn[data-protected="true"]::after,
.iqraarbbtn[data-protected="true"]::after,
.button.download[data-protected="true"]::after,
.button.read[data-protected="true"]::after {
    content: "🔒";
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    font-size: 12px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .icopyright-content {
        padding: 20px;
        margin: 20px;
        max-width: none;
        width: calc(100% - 40px);
    }
    
    .icopyright-content h3 {
        font-size: 20px;
    }
    
    .icopyright-content p {
        font-size: 14px;
    }
    
    .icopyright-icon {
        padding: 6px;
        top: 3px;
        right: 3px;
    }
    
    .icopyright-icon svg {
        width: 16px;
        height: 16px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .icopyright-content {
        padding: 15px;
        margin: 10px;
        width: calc(100% - 20px);
    }
    
    .icopyright-content h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }
    
    .icopyright-content p {
        font-size: 13px;
        line-height: 1.6;
        margin-bottom: 15px;
    }
}

/* تأثيرات إضافية للكتب المحمية في القوائم */
.FeaturedPost [data-copyright="protected"] .icopyright-icon {
    top: 15px;
    right: 15px;
    padding: 10px;
}

.FeaturedPost [data-copyright="protected"] .icopyright-icon svg {
    width: 22px;
    height: 22px;
}

/* تأثير التمرير على الكتب المحمية */
[data-copyright="protected"]:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

[data-copyright="protected"]:hover .icopyright-icon {
    animation: copyrightShake 0.5s ease;
}

@keyframes copyrightShake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

/* شارة "محمي" إضافية */
.copyright-badge {
    position: absolute;
    bottom: 5px;
    left: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    z-index: 2;
    text-transform: uppercase;
}

/* تحسينات للوضع الليلي */
@media (prefers-color-scheme: dark) {
    .icopyright-content {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .icopyright-content p {
        color: #cbd5e0;
    }
}

/* تأثيرات الانتقال السلس */
* {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* إخفاء عناصر معينة للكتب المحمية */
[data-copyright="protected"] .download-info,
[data-copyright="protected"] .file-size,
[data-copyright="protected"] .download-count {
    display: none;
}

/* رسالة بديلة للكتب المحمية */
.copyright-notice {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffeaa7;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.copyright-notice::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.copyright-notice h4 {
    color: #856404;
    margin-bottom: 10px;
    font-weight: bold;
}

.copyright-notice p {
    color: #856404;
    margin: 0;
    line-height: 1.6;
}
