# 🚀 دليل البدء السريع - سيرفر FoulaBook Scraper

## 📋 المتطلبات الأساسية
- Python 3.7 أو أحدث
- اتصال بالإنترنت
- متصفح ويب

## ⚡ التشغيل السريع (3 خطوات)

### 1️⃣ تشغيل السيرفر
```bash
# Windows - تشغيل سريع
start_server.bat

# أو تشغيل كامل
run_server.bat
```

### 2️⃣ فتح المتصفح
انتقل إلى: **http://localhost:5000**

### 3️⃣ سحب أول كتاب
1. أدخل رابط كتاب من foulabook.com
2. اضغط "سحب البيانات"
3. انتظر انتهاء شريط التقدم
4. عدّل البيانات إذا لزم الأمر
5. اضغط "توليد المقالة"

## 🎯 الميزات الرئيسية

### 📊 لوحة التحكم
- **إحصائيات مباشرة**: عدد المقالات والحجم ووقت التشغيل
- **شريط تقدم**: متابعة عملية السحب في الوقت الفعلي
- **إشعارات**: تنبيهات فورية لحالة العمليات

### 📚 إدارة المقالات
- **المقالات المسحوبة**: عرض وإدارة البيانات المسحوبة
- **المقالات المنشورة**: تتبع المقالات المولدة
- **معاينة**: عرض المقالات قبل النشر
- **حذف**: إزالة المقالات غير المرغوبة

### 🔒 حماية حقوق الطبع والنشر
- **تفعيل الحماية**: خيار لحماية الكتب المحمية
- **رسائل تنبيه**: إشعارات مخصصة للكتب المحمية
- **أيقونات مميزة**: علامات بصرية للكتب المحمية

## 🛠️ استكشاف الأخطاء

### ❌ السيرفر لا يعمل
```bash
# تحقق من Python
python --version

# تثبيت المكتبات
pip install -r requirements.txt

# تشغيل يدوي
python server.py
```

### ❌ لا يمكن الوصول للموقع
- تأكد من أن السيرفر يعمل
- جرب: http://127.0.0.1:5000
- تحقق من جدار الحماية

### ❌ فشل في السحب
- تحقق من صحة الرابط
- تأكد من اتصال الإنترنت
- جرب رابط كتاب آخر

## 📁 هيكل الملفات

```
📦 FoulaBook Scraper
├── 🐍 server.py              # السيرفر الرئيسي
├── ⚙️ config.py              # ملف الإعدادات
├── 📁 templates/
│   └── 🌐 index.html         # واجهة الويب
├── 📁 scraped_articles/      # المقالات المسحوبة
├── 📁 published_articles/    # المقالات المنشورة
├── 📁 logs/                  # ملفات السجلات
├── 🚀 start_server.bat       # تشغيل سريع
├── 🚀 run_server.bat         # تشغيل كامل
└── 🧪 test_server.py         # اختبار السيرفر
```

## 🎮 أمثلة عملية

### مثال 1: سحب كتاب عادي
1. رابط: `https://foulabook.com/ar/book/example-book-pdf`
2. سحب البيانات ← تعديل ← توليد المقالة
3. النتيجة: مقالة جاهزة للنشر

### مثال 2: كتاب محمي بحقوق الطبع والنشر
1. رابط: `https://foulabook.com/ar/book/protected-book-pdf`
2. سحب البيانات ← تفعيل "محمي بحقوق الطبع والنشر"
3. توليد المقالة ← النتيجة: مقالة مع رسالة حماية

## 🔧 إعدادات متقدمة

### تغيير المنفذ
```python
# في config.py
SERVER_CONFIG['port'] = 8080
```

### تخصيص المجلدات
```python
# في config.py
DIRECTORIES['scraped_articles'] = 'my_books'
```

## 📞 الدعم

### ملفات السجلات
- `logs/server.log` - سجل السيرفر
- `logs/scraper.log` - سجل عمليات السحب

### اختبار النظام
```bash
# تشغيل اختبار شامل
test_server.bat

# أو يدوياً
python test_server.py
```

## 🎉 نصائح للاستخدام الأمثل

1. **ابدأ بكتب بسيطة** لاختبار النظام
2. **راجع البيانات** قبل توليد المقالة
3. **استخدم الحماية** للكتب المحمية بحقوق الطبع والنشر
4. **احتفظ بنسخ احتياطية** من المقالات المهمة
5. **راقب الإحصائيات** لمتابعة الأداء

---

**🚀 مبروك! أنت الآن جاهز لاستخدام سيرفر FoulaBook Scraper**

للمزيد من التفاصيل، راجع ملف `README_SERVER.md`
