(function(sttc){'use strict';var aa=Object.defineProperty,ba=globalThis,ca=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",da={},ea={};function fa(a,b,c){if(!c||a!=null){c=ea[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ha(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in da?f=da:f=ba;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ca&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(da,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ea[d]===void 0&&(a=Math.random()*1E9>>>0,ea[d]=ca?ba.Symbol(d):"$jscp$"+a+"$"+d),aa(f,ea[d],{configurable:!0,writable:!0,value:b})))}}ha("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var q=this||self;function ja(a,b){var c=ka("CLOSURE_FLAGS");a=c&&c[a];return a!=null?a:b}function ka(a){a=a.split(".");for(var b=q,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function la(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function ma(a){return Object.prototype.hasOwnProperty.call(a,na)&&a[na]||(a[na]=++oa)}var na="closure_uid_"+(Math.random()*1E9>>>0),oa=0;function pa(a,b,c){return a.call.apply(a.bind,arguments)} 
function qa(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function ra(a,b,c){ra=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?pa:qa;return ra.apply(null,arguments)} 
function sa(a,b,c){a=a.split(".");c=c||q;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function ta(a){q.setTimeout(()=>{throw a;},0)};function ua(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]} 
function va(a,b){let c=0;a=ua(String(a)).split(".");b=ua(String(b)).split(".");const d=Math.max(a.length,b.length);for(let g=0;c==0&&g<d;g++){var e=a[g]||"",f=b[g]||"";do{e=/(\d*)(\D*)(.*)/.exec(e)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(e[0].length==0&&f[0].length==0)break;c=wa(e[1].length==0?0:parseInt(e[1],10),f[1].length==0?0:parseInt(f[1],10))||wa(e[2].length==0,f[2].length==0)||wa(e[2],f[2]);e=e[3];f=f[3]}while(c==0)}return c}function wa(a,b){return a<b?-1:a>b?1:0};var xa=ja(610401301,!1),ya=ja(748402147,ja(1,!0));function za(){var a=q.navigator;return a&&(a=a.userAgent)?a:""}var Aa;const Ba=q.navigator;Aa=Ba?Ba.userAgentData||null:null;function Ca(a){if(!xa||!Aa)return!1;for(let b=0;b<Aa.brands.length;b++){const {brand:c}=Aa.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function r(a){return za().indexOf(a)!=-1};function Da(){return xa?!!Aa&&Aa.brands.length>0:!1}function Ea(){return Da()?!1:r("Trident")||r("MSIE")}function Fa(){return Da()?Ca("Chromium"):(r("Chrome")||r("CriOS"))&&!(Da()?0:r("Edge"))||r("Silk")}function Ga(a){const b={};a.forEach(c=>{b[c[0]]=c[1]});return c=>b[c.find(d=>d in b)]||""} 
function Ha(){var a=za();if(Ea()){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])a=b[1];else{b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];a=b}return a}c=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");b=[];let d;for(;d=c.exec(a);)b.push([d[1],d[2],d[3]||void 0]);a=Ga(b);return(Da()?0:r("Opera"))?a(["Version", 
"Opera"]):(Da()?0:r("Edge"))?a(["Edge"]):(Da()?Ca("Microsoft Edge"):r("Edg/"))?a(["Edg"]):r("Silk")?a(["Silk"]):Fa()?a(["Chrome","CriOS","HeadlessChrome"]):(a=b[2])&&a[1]||""};function Ia(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ja(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Ka(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d} 
function La(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function Ma(a,b){a:{var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;c--)if(c in d&&b.call(void 0,d[c],c,a)){b=c;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]}function Na(a,b){return Ia(a,b)>=0}function Oa(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};function Pa(a){Pa[" "](a);return a}Pa[" "]=function(){};var Qa=null;function Ta(a){const b=[];Ua(a,function(c){b.push(c)});return b}function Ua(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=Qa[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}Va();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function Va(){if(!Qa){Qa={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));for(let e=0;e<d.length;e++){const f=d[e];Qa[f]===void 0&&(Qa[f]=e)}}}};function Wa(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Xa=void 0,Ya;function Za(a){if(Ya)throw Error("");Ya=b=>{q.setTimeout(()=>{a(b)},0)}}function $a(a){if(Ya)try{Ya(a)}catch(b){throw b.cause=a,b;}}function ab(a){a=Error(a);Wa(a,"warning");$a(a);return a};function cb(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var db=cb(),eb=cb(),fb=cb(),gb=cb("m_m",!0);const u=cb("jas",!0);var hb;const ib=[];ib[u]=7;hb=Object.freeze(ib);function jb(a){if(4&a)return 512&a?512:1024&a?1024:0}function kb(a){a[u]|=32;return a};var lb={};function mb(a,b){return b===void 0?a.i!==nb&&!!(2&(a.C[u]|0)):!!(2&b)&&a.i!==nb}const nb={};var ob=Object.freeze({}),pb=Object.freeze({});function qb(a){a.yc=!0;return a};var rb=qb(a=>typeof a==="number"),sb=qb(a=>typeof a==="string"),tb=qb(a=>Array.isArray(a));function ub(){return qb(a=>tb(a)?a.every(b=>rb(b)):!1)};function vb(a){if(sb(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(rb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var yb=qb(a=>a>=wb&&a<=xb);const wb=BigInt(Number.MIN_SAFE_INTEGER),xb=BigInt(Number.MAX_SAFE_INTEGER);let zb=0,Ab=0;function Bb(a){const b=a>>>0;zb=b;Ab=(a-b)/4294967296>>>0}function Cb(a){if(a<0){Bb(-a);a=zb;var b=Ab;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];zb=c>>>0;Ab=d>>>0}else Bb(a)}function Db(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function Eb(){var a=zb,b=Ab,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=Db(a,b);return c};function Fb(a,b=`unexpected value ${a}!`){throw Error(b);};const Gb=typeof BigInt==="function"?BigInt.asIntN:void 0,Hb=Number.isSafeInteger,Ib=Number.isFinite,Jb=Math.trunc;function Kb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Lb(a){if(a!=null&&typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a}function Mb(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a} 
const Nb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Ob(a){switch(typeof a){case "bigint":return!0;case "number":return Ib(a);case "string":return Nb.test(a);default:return!1}}function Pb(a){if(!Ib(a))throw ab("enum");return a|0}function Qb(a){return a==null?a:Ib(a)?a|0:void 0}function Rb(a){if(typeof a!=="number")throw ab("int32");if(!Ib(a))throw ab("int32");return a|0}function Sb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Ib(a)?a|0:void 0} 
function Tb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Ib(a)?a>>>0:void 0}function Ub(a){if(!Ob(a))throw ab("int64");switch(typeof a){case "string":return Vb(a);case "bigint":return vb(Gb(64,a));default:return Wb(a)}}function Wb(a){a=Jb(a);if(!Hb(a)){Cb(a);var b=zb,c=Ab;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:Db(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a} 
function Xb(a){a=Jb(a);Hb(a)?a=String(a):(Cb(a),a=Eb());return a}function Vb(a){var b=Jb(Number(a));if(Hb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));b=a.length;(a[0]==="-"?b<20||b===20&&a<="-9223372036854775808":b<19||b===19&&a<="9223372036854775807")||(a.length<16?Cb(Number(a)):(a=BigInt(a),zb=Number(a&BigInt(4294967295))>>>0,Ab=Number(a>>BigInt(32)&BigInt(4294967295))),a=Eb());return a}function Yb(a){if(typeof a!=="string")throw Error();return a} 
function Zb(a){if(a!=null&&typeof a!=="string")throw Error();return a}function w(a){return a==null||typeof a==="string"?a:void 0}function $b(a,b,c,d){if(a!=null&&a[gb]===lb)return a;if(!Array.isArray(a))return c?d&2?b[db]||(b[db]=ac(b)):new b:void 0;c=a[u]|0;d=c|d&32|d&2;d!==c&&(a[u]=d);return new b(a)}function ac(a){a=new a;var b=a.C;b[u]|=34;return a};function bc(a){return a};function cc(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let h,k=4294967295,n=!1;const l=!!(b&64),m=l?b&128?0:-1:void 0;b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,k=g):h=void 0,!l||b&128||e||(n=!0,k=(dc??bc)(k-m,m,a,h,void 0)+m));b=void 0;for(e=0;e<g;e++){let p=a[e];if(p!=null&&(p=c(p,d))!=null)if(l&&e>=k){const v=e-m;(b??(b={}))[v]=p}else f[e]=p}if(h)for(let p in h){if(!Object.prototype.hasOwnProperty.call(h,p))continue;a=h[p];if(a==null||(a=c(a,d))== 
null)continue;g=+p;let v;l&&!Number.isNaN(g)&&(v=g+m)<k?f[v]=a:(b??(b={}))[p]=a}b&&(n?f.push(b):f[k]=b);return f}function ec(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return yb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[u]|0;return a.length===0&&b&1?void 0:cc(a,b,ec)}if(a!=null&&a[gb]===lb)return x(a);return}return a}var fc=typeof structuredClone!="undefined"?structuredClone:a=>cc(a,0,ec);let dc; 
function x(a){a=a.C;return cc(a,a[u]|0,ec)};function hc(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[u]|0;if(ya&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&ic();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[u]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&&typeof d==="object"&&d.constructor===Object){const f=b&128?0:-1;e-=f;if(e>=1024)throw Error("pvtlmt");for(const g in d){if(!Object.prototype.hasOwnProperty.call(d,g))continue;const h=+g;if(h<e)c[h+f]=d[g],delete d[g];else break}b= 
b&-8380417|(e&1023)<<13}}}a[u]=b|2112;return a}function ic(){if(ya)throw Error("carr");if(fb!=null){var a=Xa??(Xa={});var b=a[fb]||0;b>=5||(a[fb]=b+1,a=Error(),Wa(a,"incident"),Ya?$a(a):ta(a))}};function jc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[u]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=kc(a,c,!1,b&&!(c&16)):(a[u]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[gb]===lb)return b=a.C,c=b[u]|0,mb(a,c)?a:lc(a,b,c)?mc(a,b):kc(b,c)}function mc(a,b,c){a=new a.constructor(b);c&&(a.i=nb);a.A=nb;return a}function kc(a,b,c,d){d??(d=!!(34&b));a=cc(a,b,jc,d);d=32;c&&(d|=2);b=b&8380609|d;a[u]=b;return a} 
function nc(a){const b=a.C,c=b[u]|0;return mb(a,c)?lc(a,b,c)?mc(a,b,!0):new a.constructor(kc(b,c,!1)):a}function oc(a){if(a.i!==nb)return!1;var b=a.C;b=kc(b,b[u]|0);b[u]|=2048;a.C=b;a.i=void 0;a.A=void 0;return!0}function pc(a){if(!oc(a)&&mb(a,a.C[u]|0))throw Error();}function qc(a,b){b===void 0&&(b=a[u]|0);b&32&&!(b&4096)&&(a[u]=b|4096)}function lc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(b[u]=c|2,a.i=nb,!0):!1};const rc=vb(0),sc={};function z(a,b,c,d,e){b=tc(a.C,b,c,e);if(b!==null||d&&a.A!==nb)return b}function tc(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function uc(a,b,c){pc(a);const d=a.C;A(d,d[u]|0,b,c);return a} 
function A(a,b,c,d){const e=c+-1;var f=a.length-1;if(f>=0&&e>=f){const g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&&(f=(b??(b=a[u]|0))>>13&1023||536870912,c>=f?d!=null&&(a[f+-1]={[c]:d}):a[e]=d);return b}function vc(a,b,c){a=a.C;return wc(a,a[u]|0,b,c)!==void 0}function B(a){return a===ob?2:4} 
function xc(a,b,c,d,e){let f=a.C,g=f[u]|0;d=mb(a,g)?1:d;e=!!e||d===3;d===2&&oc(a)&&(f=a.C,g=f[u]|0);a=yc(f,b);let h=a===hb?7:a[u]|0,k=zc(h,g);var n=4&k?!1:!0;if(n){4&k&&(a=[...a],h=0,k=Ac(k,g),g=A(f,g,b,a));let l=0,m=0;for(;l<a.length;l++){const p=c(a[l]);p!=null&&(a[m++]=p)}m<l&&(a.length=m);c=(k|4)&-513;k=c&=-1025;k&=-4097}k!==h&&(a[u]=k,2&k&&Object.freeze(a));return a=Bc(a,k,f,g,b,d,n,e)} 
function Bc(a,b,c,d,e,f,g,h){let k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Cc(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==k&&(a[u]=b),Object.freeze(a)):(f===2&&Cc(b)&&(a=[...a],k=0,b=Ac(b,d),d=A(c,d,e,a)),Cc(b)||(h||(b|=16),b!==k&&(a[u]=b)));2&b||!(4096&b||16&b)||qc(c,d);return a}function yc(a,b){a=tc(a,b);return Array.isArray(a)?a:hb}function zc(a,b){2&b&&(a|=2);return a|1}function Cc(a){return!!(2&a)&&!!(4&a)||!!(256&a)} 
function Dc(a,b,c,d){pc(a);const e=a.C;let f=e[u]|0;if(c==null)return A(e,f,b),a;let g=c===hb?7:c[u]|0,h=g;var k=Cc(g);let n=k||Object.isFrozen(c);k||(g=0);n||(c=[...c],h=0,g=Ac(g,f),n=!1);g|=5;k=jb(g)??0;for(let l=0;l<c.length;l++){const m=c[l],p=d(m,k);Object.is(m,p)||(n&&(c=[...c],h=0,g=Ac(g,f),n=!1),c[l]=p)}g!==h&&(n&&(c=[...c],g=Ac(g,f)),c[u]=g);A(e,f,b,c);return a}function Ec(a,b,c,d){pc(a);const e=a.C;A(e,e[u]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a} 
function Fc(a,b,c,d){pc(a);const e=a.C;var f=e[u]|0;if(d==null){var g=Gc(e);if(Hc(g,e,f,c)===b)g.set(c,0);else return a}else{g=Gc(e);const h=Hc(g,e,f,c);h!==b&&(h&&(f=A(e,f,h)),g.set(c,b))}A(e,f,b,d);return a}function Ic(a,b,c){return Jc(a,b)===c?c:-1}function Jc(a,b){a=a.C;return Hc(Gc(a),a,void 0,b)}function Gc(a){return a[eb]??(a[eb]=new Map)} 
function Hc(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];tc(b,g)!=null&&(e!==0&&(c=A(b,c,e)),e=g)}a.set(d,e);return e}function Kc(a,b,c){pc(a);a=a.C;let d=a[u]|0;const e=tc(a,c),f=void 0===pb;b=$b(e,b,!f,d);if(!f||b)return b=nc(b),e!==b&&(d=A(a,d,c,b),qc(a,d)),b}function wc(a,b,c,d){let e=!1;d=tc(a,d,void 0,f=>{const g=$b(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!mb(d)&&qc(a,b),d} 
function Lc(a){var b=Mc;a=a.C;return wc(a,a[u]|0,b,4)||b[db]||(b[db]=ac(b))}function C(a,b,c){let d=a.C,e=d[u]|0;b=wc(d,e,b,c);if(b==null)return b;e=d[u]|0;if(!mb(a,e)){const f=nc(b);f!==b&&(oc(a)&&(d=a.C,e=d[u]|0),b=f,e=A(d,e,c,b),qc(d,e))}return b} 
function D(a,b,c,d){var e=a.C,f=e;e=e[u]|0;var g=mb(a,e);const h=g?1:d;d=h===3;var k=!g;(h===2||k)&&oc(a)&&(f=a.C,e=f[u]|0);a=yc(f,c);var n=a===hb?7:a[u]|0,l=zc(n,e);if(g=!(4&l)){var m=a,p=e;const v=!!(2&l);v&&(p|=2);let t=!v,y=!0,G=0,ia=0;for(;G<m.length;G++){const Ra=$b(m[G],b,!1,p);if(Ra instanceof b){if(!v){const Sa=mb(Ra);t&&(t=!Sa);y&&(y=Sa)}m[ia++]=Ra}}ia<G&&(m.length=ia);l|=4;l=y?l&-4097:l|4096;l=t?l|8:l&-9}l!==n&&(a[u]=l,2&l&&Object.freeze(a));if(k&&!(8&l||!a.length&&(h===1||(h!==4?0:2&l|| 
!(16&l)&&32&e)))){Cc(l)&&(a=[...a],l=Ac(l,e),e=A(f,e,c,a));b=a;k=l;for(n=0;n<b.length;n++)m=b[n],l=nc(m),m!==l&&(b[n]=l);k|=8;l=k=b.length?k|4096:k&-4097;a[u]=l}return a=Bc(a,l,f,e,c,h,g,d)}function Nc(a){a==null&&(a=void 0);return a}function Oc(a,b,c){c=Nc(c);uc(a,b,c);c&&!mb(c)&&qc(a.C);return a}function E(a,b,c,d){d=Nc(d);Fc(a,b,c,d);d&&!mb(d)&&qc(a.C);return a} 
function Pc(a,b,c){pc(a);const d=a.C;let e=d[u]|0;if(c==null)return A(d,e,b),a;let f=c===hb?7:c[u]|0,g=f;const h=Cc(f),k=h||Object.isFrozen(c);let n=!0,l=!0;for(let p=0;p<c.length;p++){var m=c[p];h||(m=mb(m),n&&(n=!m),l&&(l=m))}h||(f=n?13:5,f=l?f&-4097:f|4096);k&&f===g||(c=[...c],g=0,f=Ac(f,e));f!==g&&(c[u]=f);e=A(d,e,b,c);2&f||!(4096&f||16&f)||qc(d,e);return a}function Ac(a,b){return a=(2&b?a|2:a&-3)&-273} 
function Qc(a,b){pc(a);a=xc(a,4,w,2,!0);const c=jb(a===hb?7:a[u]|0)??0;if(Array.isArray(b)){var d=b.length;for(let e=0;e<d;e++)a.push(Yb(b[e],c))}else for(d of b)a.push(Yb(d,c))}function Rc(a,b){a=z(a,b);b=typeof a;a!=null&&(b==="bigint"?a=vb(Gb(64,a)):Ob(a)?b==="string"?(b=Jb(Number(a)),Hb(b)?a=vb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=vb(Gb(64,BigInt(a))))):a=Hb(a)?vb(Wb(a)):vb(Xb(a)):a=void 0);return a}function Sc(a,b,c){return Sb(z(a,b,void 0,c))} 
function F(a,b){return Mb(z(a,b))??!1}function H(a,b){return Sc(a,b)??0}function Tc(a,b){return z(a,b,void 0,void 0,Kb)??0}function I(a,b){return w(z(a,b))??""}function J(a,b){return Qb(z(a,b))??0}function Uc(a,b,c){return J(a,Ic(a,c,b))}function Vc(a,b,c,d){return C(a,b,Ic(a,d,c))}function Wc(a,b){return w(z(a,b,void 0,sc))}function Xc(a,b){return Qb(z(a,b,void 0,sc))}function Yc(a,b,c){return uc(a,b,c==null?c:Rb(c))}function Zc(a,b,c){return Ec(a,b,c==null?c:Rb(c),0)} 
function $c(a,b,c){return Ec(a,b,c==null?c:Ub(c),"0")}function ad(a,b){var c=performance.now();if(c!=null&&typeof c!=="number")throw Error(`Value of float/double field must be a number, found ${typeof c}: ${c}`);Ec(a,b,c,0)}function bd(a,b,c){return uc(a,b,Zb(c))}function cd(a,b,c){return Ec(a,b,Zb(c),"")}function ed(a,b,c){return uc(a,b,c==null?c:Pb(c))}function fd(a,b,c){return Ec(a,b,c==null?c:Pb(c),0)}function gd(a,b,c,d){return Fc(a,b,c,d==null?d:Pb(d))};function hd(a){const b=a.C,c=b[u]|0;return mb(a,c)?a:lc(a,b,c)?mc(a,b):new a.constructor(kc(b,c,!0))}var K=class{constructor(a){this.C=hc(a)}toJSON(){return x(this)}B(){return JSON.stringify(x(this))}};K.prototype[gb]=lb;function id(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(kb(b))};function jd(a){return()=>a[db]||(a[db]=ac(a))}function kd(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(kb(b))}return b}};var ld=class extends K{};var md=class extends K{};function nd(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};function od(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Pa(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function pd(a){return od(a.top)?a.top:null};function qd(a){return function(){return!a.apply(this,arguments)}}function rd(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function sd(a){let b=a;return function(){if(b){const c=b;b=null;c()}}};function td(){return xa&&Aa?Aa.mobile:!ud()&&(r("iPod")||r("iPhone")||r("Android")||r("IEMobile"))}function ud(){return xa&&Aa?!Aa.mobile&&(r("iPad")||r("Android")||r("Silk")):r("iPad")||r("Android")&&!r("Mobile")||r("Silk")};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let vd=globalThis.trustedTypes,wd;function xd(){let a=null;if(!vd)return a;try{const b=c=>c;a=vd.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var yd=class{constructor(a){this.g=a}toString(){return this.g+""}};function zd(a){var b;wd===void 0&&(wd=xd());a=(b=wd)?b.createScriptURL(a):a;return new yd(a)}function Ad(a){if(a instanceof yd)return a.g;throw Error("");};var Bd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Cd(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};const Dd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Ed(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};var Fd=rd(()=>td()?2:ud()?1:0);let Gd,Hd=64;function Id(){try{return Gd??(Gd=new Uint32Array(64)),Hd>=64&&(crypto.getRandomValues(Gd),Hd=0),Gd[Hd++]}catch(a){return Math.floor(Math.random()*2**32)}};function Jd(a,b){if(!rb(a.goog_pvsid))try{const c=Id()+(Id()&2**21-1)*2**32;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(c){b.na({methodName:784,ua:c})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.na({methodName:784,ua:Error(`Invalid correlator, ${a}`)});return a||-1};function Kd(a,...b){if(b.length===0)return zd(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return zd(c)}function Ld(a,b){a=Ad(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Md(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function Md(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return zd(a+b+c)};function Nd(a,b){const c=Od("SCRIPT",a);c.src=Ad(b);(b=Cd(c.ownerDocument))&&c.setAttribute("nonce",b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function Pd(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle}function Qd(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}var Rd=/^([0-9.]+)px$/,Sd=/^(-?[0-9.]{1,30})$/; 
function Td(a){if(!Sd.test(a))return null;a=Number(a);return isNaN(a)?null:a}function Ud(a){return(a=Rd.exec(a))?+a[1]:null}var Vd=a=>{nd({display:"none"},(b,c)=>{a.style.setProperty(c,b,"important")})};function Wd(){var a=L(Xd).A(Yd.g,Yd.defaultValue),b=M.document;if(a.length&&b.head)for(const c of a)c&&b.head&&(a=Od("META"),b.head.appendChild(a),a.httpEquiv="origin-trial",a.content=c)}var Zd=a=>Jd(a,{na:()=>{}});function Od(a,b=document){return b.createElement(String(a).toLowerCase())};let $d=[];function ae(){const a=$d;$d=[];for(const b of a)try{b()}catch{}};function be(a,b){this.width=a;this.height=b}be.prototype.aspectRatio=function(){return this.width/this.height};be.prototype.isEmpty=function(){return!(this.width*this.height)};be.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};be.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};be.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
be.prototype.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function ce(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function de(a,b){for(const c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function ee(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function fe(a){const b={};for(const c in a)b[c]=a[c];return b};function ge(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function he(a){this.g=a||q.document||document}he.prototype.contains=function(a,b){return a&&b?a==b||a.contains(b):!1};function ie(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function je(a,b,c){return typeof a.removeEventListener==="function"?(a.removeEventListener(b,c,!1),!0):!1}function ke(a){var b=le;b.readyState==="complete"||b.readyState==="interactive"?($d.push(a),$d.length===1&&(window.Promise?Promise.resolve().then(ae):(a=window.setImmediate,typeof a==="function"?a(ae):setTimeout(ae,0)))):b.addEventListener("DOMContentLoaded",a)};function me(a,b,c=null,d=!1,e=!1){ne(a,b,c,d,e)}function ne(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Od("IMG",a.document);if(c||d){const g=h=>{c&&c(h);if(d){h=a.google_image_requests;const k=Ia(h,f);k>=0&&Array.prototype.splice.call(h,k,1)}je(f,"load",g);je(f,"error",g)};ie(f,"load",g);ie(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function oe(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;nd(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});pe(c)}function pe(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):me(b,a,void 0,!1,!1)};var le=document,M=window;let qe=null;var re=(a,b=[])=>{let c=!1;q.google_logging_queue||(c=!0,q.google_logging_queue=[]);q.google_logging_queue.push([a,b]);if(a=c){if(qe==null){qe=!1;try{const d=pd(q);d&&d.location.hash.indexOf("google_logging")!==-1&&(qe=!0)}catch(d){}}a=qe}a&&Nd(q.document,Kd`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function se(a){return!!(a.error&&a.meta&&a.id)}var te=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function ue(a){return new te(a,{message:ve(a)})}function ve(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const we=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var xe=class{constructor(a,b){this.g=a;this.i=b}},ye=class{constructor(a,b,c){this.url=a;this.l=b;this.g=!!c;this.depth=null}};let ze=null;function Ae(){var a=window;if(ze===null){ze="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);ze=c?c[1]:""}}catch(b){}}return ze};function Be(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Ce(){const a=q.performance;return a&&a.now?a.now():null};var De=class{constructor(a,b){var c=Ce()||Be();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Ee=q.performance,Fe=!!(Ee&&Ee.mark&&Ee.measure&&Ee.clearMarks),Ge=rd(()=>{var a;if(a=Fe)a=Ae(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function He(a){a&&Ee&&Ge()&&(Ee.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Ee.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function Ie(a){a.g=!1;if(a.i!==a.j.google_js_reporting_queue){if(Ge()){var b=a.i;const c=b.length;b=typeof b==="string"?b.split(""):b;for(let d=0;d<c;d++)d in b&&He.call(void 0,b[d])}a.i.length=0}} 
var Je=class{constructor(a){this.i=[];this.j=a||q;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Ge()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new De(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Ee&&Ge()&&Ee.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(Ce()||Be())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Ee&&Ge()&&Ee.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Le(a,b){const c={};c[a]=b;return[c]}function Me(a,b,c,d,e){const f=[];nd(a,(g,h)=>{(g=Ne(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Ne(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Ne(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Me(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Oe(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function Pe(a,b,c){b="https://"+b+c;let d=Oe(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}let n=Me(h[k],a.j,",$");if(n){n=e+n;if(d>=n.length){d-=n.length;b+=n;e=a.j;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var Qe=class{constructor(){this.j="&";this.i={};this.u=0;this.g=[]}};var Re=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Se=/#|$/;function Te(a,b){const c=a.search(Se);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};var We=class{constructor(a=null){this.F=Ue;this.j=a;this.i=null;this.B=!1;this.D=this.J}H(a){this.D=a}A(a){this.i=a}T(a){this.B=a}g(a,b,c){let d,e;try{this.j&&this.j.g?(e=this.j.start(a.toString(),3),d=b(),this.j.end(e)):d=b()}catch(f){b=!0;try{He(e),b=this.D(a,ue(f),void 0,c)}catch(g){this.J(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}J(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const bb=new Qe;var g=bb;g.g.push(1);g.i[1]=Le("context", 
a);se(b)||(b=ue(b));g=b;if(g.msg){b=bb;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Le("msg",h)}var k=g.meta||{};h=k;if(this.i)try{this.i(h)}catch(V){}if(d)try{d(h)}catch(V){}d=bb;k=[k];d.g.push(3);d.i[3]=k;var n;if(!(n=p)){d=q;k=[];h=null;do{var l=d;if(od(l)){var m=l.location.href;h=l.document&&l.document.referrer||null}else m=h,h=null;k.push(new ye(m||"",l));try{d=l.parent}catch(V){d=null}}while(d&&l!==d);for(let V=0,Tg=k.length-1;V<=Tg;++V)k[V].depth=Tg-V;l=q;if(l.location&&l.location.ancestorOrigins&& 
l.location.ancestorOrigins.length===k.length-1)for(m=1;m<k.length;++m){const V=k[m];V.url||(V.url=l.location.ancestorOrigins[m-1]||"",V.g=!0)}n=k}var p=n;let dd=new ye(q.location.href,q,!1);n=null;const Ke=p.length-1;for(l=Ke;l>=0;--l){var v=p[l];!n&&we.test(v.url)&&(n=v);if(v.url&&!v.g){dd=v;break}}v=null;const Rk=p.length&&p[Ke].url;dd.depth!==0&&Rk&&(v=p[Ke]);f=new xe(dd,v);if(f.i){p=bb;var t=f.i.url||"";p.g.push(4);p.i[4]=Le("top",t)}var y={url:f.g.url||""};if(f.g.url){const V=f.g.url.match(Re); 
var G=V[1],ia=V[3],Ra=V[4];t="";G&&(t+=G+":");ia&&(t+="//",t+=ia,Ra&&(t+=":"+Ra));var Sa=t}else Sa="";G=bb;y=[y,{url:Sa}];G.g.push(5);G.i[5]=y;Ve(this.F,e,bb,this.B,c)}catch(bb){try{Ve(this.F,e,{context:"ecmserr",rctx:a,msg:ve(bb),url:f?.g.url??""},this.B,c)}catch(dd){}}return!0}qa(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.J(a,c instanceof Error?c:Error(c),void 0,this.i||void 0)})}};var Xe=class extends K{},Ye=[2,3,4];var Ze=class extends K{},$e=[3,4,5],af=[6,7];var bf=class extends K{},cf=[4,5];function df(a,b){var c=D(a,Ze,2,B());if(!c.length)return ef(a,b);a=J(a,1);if(a===1)return c=df(c[0],b),c.success?{success:!0,value:!c.value}:c;c=Ka(c,d=>df(d,b));switch(a){case 2:return c.find(d=>d.success&&!d.value)??c.find(d=>!d.success)??{success:!0,value:!0};case 3:return c.find(d=>d.success&&d.value)??c.find(d=>!d.success)??{success:!0,value:!1};default:return{success:!1,O:3}}} 
function ef(a,b){var c=Jc(a,$e);a:{switch(c){case 3:var d=Uc(a,3,$e);break a;case 4:d=Uc(a,4,$e);break a;case 5:d=Uc(a,5,$e);break a}d=void 0}if(!d)return{success:!1,O:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,property:d,ga:c,O:1};let e;try{var f=xc(a,8,w,B());e=b(...f)}catch(g){return{success:!1,property:d,ga:c,O:2}}f=J(a,1);if(f===4)return{success:!0,value:!!e};if(f===5)return{success:!0,value:e!=null};if(f===12)a=I(a,Ic(a,af,7));else a:{switch(c){case 4:a=Tc(a,Ic(a,af,6));break a;case 5:a=I(a, 
Ic(a,af,7));break a}a=void 0}if(a==null)return{success:!1,property:d,ga:c,O:3};if(f===6)return{success:!0,value:e===a};if(f===9)return{success:!0,value:e!=null&&va(String(e),a)===0};if(e==null)return{success:!1,property:d,ga:c,O:4};switch(f){case 7:c=e<a;break;case 8:c=e>a;break;case 12:c=sb(a)&&sb(e)&&(new RegExp(a)).test(e);break;case 10:c=e!=null&&va(String(e),a)===-1;break;case 11:c=e!=null&&va(String(e),a)===1;break;default:return{success:!1,O:3}}return{success:!0,value:c}} 
function ff(a,b){return a?b?df(a,b):{success:!1,O:1}:{success:!0,value:!0}};function gf(a){return xc(a,4,w,B())}var Mc=class extends K{};var hf=class extends K{getValue(){return C(this,Mc,2)}};var jf=class extends K{},kf=kd(jf),lf=[1,2,3,6,7,8];var mf=class extends K{};function nf(a,b){try{const c=d=>[{[d.Ba]:d.za}];return JSON.stringify([a.filter(d=>d.oa).map(c),x(b),a.filter(d=>!d.oa).map(c)])}catch(c){return of(c,b),""}}function of(a,b){try{oe({m:ve(a instanceof Error?a:Error(String(a))),b:J(b,1)||null,v:I(b,2)||null},"rcs_internal")}catch(c){}}var pf=class{constructor(a,b){var c=new mf;a=fd(c,1,a);b=cd(a,2,b);this.j=hd(b)}};var qf=class extends K{getWidth(){return H(this,3)}getHeight(){return H(this,4)}};var rf=class extends K{};function sf(a,b){return uc(a,1,b==null?b:Ub(b))}function tf(a,b){return uc(a,2,b==null?b:Ub(b))}var uf=class extends K{getWidth(){return Rc(this,1)??rc}getHeight(){return Rc(this,2)??rc}};var vf=class extends K{};function wf(a){var b=new xf;return fd(b,1,a)}var xf=class extends K{};var yf=class extends K{};var zf=class extends K{getValue(){return J(this,1)}};var Af=class extends K{getContentUrl(){return I(this,4)}};var Bf=class extends K{};function Cf(a){return Kc(a,Bf,3)}var Df=class extends K{};var Ef=class extends K{getContentUrl(){return I(this,1)}};var Ff=class extends K{};var Gf=class extends K{};var Hf=class extends K{};var If=class extends K{},Jf=[4,5,6,8,9,10,11,12,13,14,15,16,17,18,19,20,21];var Kf=class extends K{};function Lf(a,b){return fd(a,1,b)}function Mf(a,b){return fd(a,2,b)}var Nf=class extends K{};var Of=class extends K{},Pf=[1,2];function Qf(a,b){return Oc(a,1,b)}function Rf(a,b){return Pc(a,2,b)}function Sf(a,b){return Dc(a,4,b,Rb)}function Tf(a,b){return Pc(a,5,b)}function Uf(a,b){return fd(a,6,b)}var Vf=class extends K{};var Wf=class extends K{},Xf=[1,2,3,4,6];var Yf=class extends K{};function Zf(a){var b=new $f;return E(b,4,ag,a)}var $f=class extends K{getTagSessionCorrelator(){return Rc(this,2)??rc}},ag=[4,5,7,8,9];var bg=class extends K{};function cg(){var a=dg();a=nc(a);return cd(a,1,eg())}var fg=class extends K{};var gg=class extends K{};var hg=class extends K{getTagSessionCorrelator(){return Rc(this,1)??rc}};var ig=class extends K{},jg=[1,7],kg=[4,6,8];class lg extends pf{constructor(){super(...arguments)}}function mg(a,...b){ng(a,...b.map(c=>({oa:!0,Ba:3,za:x(c)})))}function og(a,...b){ng(a,...b.map(c=>({oa:!0,Ba:4,za:x(c)})))}function pg(a,...b){ng(a,...b.map(c=>({oa:!0,Ba:7,za:x(c)})))}var qg=class extends lg{};function rg(a,b){globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function ng(a,...b){try{a.D&&nf(a.g.concat(b),a.j).length>=65536&&sg(a),a.u&&!a.A&&(a.A=!0,tg(a.u,()=>{sg(a)})),a.g.push(...b),a.g.length>=a.B&&sg(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{sg(a)},a.H))}catch(c){of(c,a.j)}}function sg(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=nf(a.g,a.j);a.F("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var ug=class extends qg{constructor(a,b,c,d,e,f){super(a,b);this.F=rg;this.H=c;this.B=d;this.D=e;this.u=f;this.g=[];this.i=null;this.A=!1}},vg=class extends ug{constructor(a,b,c=1E3,d=100,e=!1,f){super(a,b,c,d,e&&!0,f)}};function wg(a,b){var c=Date.now();c=Number.isFinite(c)?Math.round(c):0;b=$c(b,1,c);c=Zd(window);b=$c(b,2,c);return $c(b,6,a.A)}function xg(a,b,c,d,e,f){if(a.j){var g=Mf(Lf(new Nf,b),c);b=Uf(Rf(Qf(Tf(Sf(new Vf,d),e),g),a.g.slice()),f);b=Zf(b);og(a.i,wg(a,b));if(f===1||f===3||f===4&&!a.g.some(h=>J(h,1)===J(g,1)&&J(h,2)===c))a.g.push(g),a.g.length>100&&a.g.shift()}}function yg(a,b,c,d){if(a.j){var e=new Kf;b=Yc(e,1,b);c=Yc(b,2,c);d=ed(c,3,d);c=new $f;d=E(c,8,ag,d);og(a.i,wg(a,d))}} 
function zg(a,b,c,d,e){if(a.j){var f=new bf;b=Oc(f,1,b);c=ed(b,2,c);d=Yc(c,3,d);if(e.ga===void 0)gd(d,4,cf,e.O);else switch(e.ga){case 3:c=new Xe;c=gd(c,2,Ye,e.property);e=ed(c,1,e.O);E(d,5,cf,e);break;case 4:c=new Xe;c=gd(c,3,Ye,e.property);e=ed(c,1,e.O);E(d,5,cf,e);break;case 5:c=new Xe,c=gd(c,4,Ye,e.property),e=ed(c,1,e.O),E(d,5,cf,e)}e=new $f;e=E(e,9,ag,d);og(a.i,wg(a,e))}}var Ag=class{constructor(a,b,c,d=new vg(6,"unknown",b)){this.A=a;this.u=c;this.i=d;this.g=[];this.j=a>0&&Qd()<1/a}};var L=a=>{var b="ya";if(a.ya&&a.hasOwnProperty(b))return a.ya;b=new a;return a.ya=b};var Bg=class{constructor(){this.N={[3]:{},[4]:{},[5]:{}}}};var Cg=/^true$/.test("false");function Dg(a,b){switch(b){case 1:return Uc(a,1,lf);case 2:return Uc(a,2,lf);case 3:return Uc(a,3,lf);case 6:return Uc(a,6,lf);case 8:return Uc(a,8,lf);default:return null}}function Eg(a,b){if(!a)return null;switch(b){case 1:return F(a,1);case 7:return I(a,3);case 2:return Tc(a,2);case 3:return I(a,3);case 6:return gf(a);case 8:return gf(a);default:return null}}const Fg=rd(()=>{if(!Cg)return{};try{var a=window;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch{b=null}if(b)return JSON.parse(b)}catch{}return{}}); 
function Gg(a,b,c,d=0){L(Hg).j[d]=L(Hg).j[d]?.add(b)??(new Set).add(b);const e=Fg();if(e[b]!=null)return e[b];b=Ig(d)[b];if(!b)return c;b=kf(JSON.stringify(b));b=Jg(b);a=Eg(b,a);return a!=null?a:c}function Jg(a){const b=L(Bg).N;if(b&&Jc(a,lf)!==8){const c=Ma(D(a,hf,5,B()),d=>{d=ff(C(d,Ze,1),b);return d.success&&d.value});if(c)return c.getValue()??null}return C(a,Mc,4)??null}class Hg{constructor(){this.i={};this.u=[];this.j={};this.g=new Map}}function Kg(a,b=!1,c){return!!Gg(1,a,b,c)} 
function Lg(a,b=0,c){a=Number(Gg(2,a,b,c));return isNaN(a)?b:a}function Mg(a,b="",c){a=Gg(3,a,b,c);return typeof a==="string"?a:b}function Ng(a,b=[],c){a=Gg(6,a,b,c);return Array.isArray(a)?a:b}function Og(a,b=[],c){a=Gg(8,a,b,c);return Array.isArray(a)?a:b}function Ig(a){return L(Hg).i[a]||(L(Hg).i[a]={})} 
function Pg(a,b){const c=Ig(b);nd(a,(d,e)=>{if(c[e]){var f=d=kf(JSON.stringify(d)),g=Ic(d,lf,8);Qb(z(f,g))!=null&&(g=kf(JSON.stringify(c[e])),f=Kc(d,Mc,4),g=gf(Lc(g)),Qc(f,g));c[e]=x(d)}else c[e]=d})} 
function Qg(a,b,c,d,e=!1){var f=[],g=[];for(const m of b){b=Ig(m);for(const p of a){var h=Jc(p,lf);const v=Dg(p,h);if(v){a:{var k=v;var n=h,l=L(Hg).g.get(m)?.get(v)?.slice(0)??[];const t=new Wf;switch(n){case 1:gd(t,1,Xf,k);break;case 2:gd(t,2,Xf,k);break;case 3:gd(t,3,Xf,k);break;case 6:gd(t,4,Xf,k);break;case 8:gd(t,6,Xf,k);break;default:k=void 0;break a}Dc(t,5,l,Rb);k=t}k&&L(Hg).j[m]?.has(v)&&f.push(k);h===8&&b[v]?(k=kf(JSON.stringify(b[v])),h=Kc(p,Mc,4),k=gf(Lc(k)),Qc(h,k)):k&&L(Hg).g.get(m)?.has(v)&& 
g.push(k);e||(h=v,k=m,n=d,l=L(Hg),l.g.has(k)||l.g.set(k,new Map),l.g.get(k).has(h)||l.g.get(k).set(h,[]),n&&l.g.get(k).get(h).push(n));b[v]=x(p)}}}if(f.length||g.length)a=d??void 0,c.j&&c.u&&(d=new Yf,f=Pc(d,2,f),g=Pc(f,3,g),a&&Zc(g,1,a),f=new $f,g=E(f,7,ag,g),og(c.i,wg(c,g)))}function Rg(a,b){b=Ig(b);for(const c of a){a=kf(JSON.stringify(c));const d=Jc(a,lf);(a=Dg(a,d))&&(b[a]||(b[a]=c))}}function Sg(){return Object.keys(L(Hg).i).map(a=>Number(a))} 
function Ug(a){L(Hg).u.includes(a)||Pg(Ig(4),a)};function N(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Vg(a,b,c){return b[a]||c}function Wg(a){N(5,Kg,a);N(6,Lg,a);N(7,Mg,a);N(8,Ng,a);N(17,Og,a);N(13,Rg,a);N(15,Ug,a)}function Xg(a){N(4,b=>{L(Bg).N=b},a);N(9,(b,c)=>{var d=L(Bg);d.N[3][b]==null&&(d.N[3][b]=c)},a);N(10,(b,c)=>{var d=L(Bg);d.N[4][b]==null&&(d.N[4][b]=c)},a);N(11,(b,c)=>{var d=L(Bg);d.N[5][b]==null&&(d.N[5][b]=c)},a);N(14,b=>{var c=L(Bg);for(const d of[3,4,5])Object.assign(c.N[d],b[d])},a)} 
function Yg(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};function Zg(a,b,c){a.i=Vg(1,b,()=>{});a.j=(d,e)=>Vg(2,b,()=>[])(d,c,e);a.u=d=>Vg(3,b,()=>[])(d??c);a.g=d=>{Vg(16,b,()=>{})(d,c)}}class $g{i(){}g(){}j(){return[]}u(){return[]}}function ah(a){return L($g).u(a)};function Ve(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof Qe?f=c:(f=new Qe,nd(c,(h,k)=>{var n=f;const l=n.u++;h=Le(k,h);n.g.push(l);n.i[l]=h}));const g=Pe(f,a.domain,a.path+b+"&");g&&me(q,g)}catch(f){}}function bh(a,b){b>=0&&b<=1&&(a.g=b)}var ch=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let Ue,dh;const eh=new Je(window);(function(a){Ue=a??new ch;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());bh(Ue,window.google_srt);dh=new We(eh);dh.A(()=>{});dh.T(!0);window.document.readyState==="complete"?window.google_measure_js_timing||Ie(eh):eh.g&&ie(window,"load",()=>{window.google_measure_js_timing||Ie(eh)})})();function fh(a=q){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function gh(a=fh()){return a?od(a.master)?a.master:null:null};var hh=a=>{a=gh(fh(a))||a;a.google_unique_id=(a.google_unique_id||0)+1;return a.google_unique_id},ih=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},jh=a=>{if(!a)return"";a=a.toLowerCase();a.substring(0,3)!="ca-"&&(a="ca-"+a);return a};let kh=(new Date).getTime();var lh={bc:0,ac:1,Xb:2,Sb:3,Yb:4,Tb:5,Zb:6,Vb:7,Wb:8,Rb:9,Ub:10,dc:11};var mh={fc:0,hc:1,ec:2};function nh(a){if(a.g!=0)throw Error("Already resolved/rejected.");}var qh=class{constructor(){this.i=new oh(this);this.g=0}resolve(a){nh(this);this.g=1;this.u=a;ph(this.i)}reject(a){nh(this);this.g=2;this.j=a;ph(this.i)}};function ph(a){switch(a.g.g){case 0:break;case 1:a.i&&a.i(a.g.u);break;case 2:a.j&&a.j(a.g.j);break;default:throw Error("Unhandled deferred state.");}}var oh=class{constructor(a){this.g=a}then(a,b){if(this.i)throw Error("Then functions already set.");this.i=a;this.j=b;ph(this)}};var rh=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new rh(Ja(this.g,a))}apply(a){return new rh(a(this.g.slice(0)))}sort(a){return new rh(this.g.slice(0).sort(a))}get(a){return this.g[a]}add(a){const b=this.g.slice(0);b.push(a);return new rh(b)}};function sh(a,b){const c=[],d=a.length;for(let e=0;e<d;e++)c.push(a[e]);c.forEach(b,void 0)};var uh=class{constructor(){this.g={};this.i={}}set(a,b){const c=th(a);this.g[c]=b;this.i[c]=a}get(a,b){a=th(a);return this.g[a]!==void 0?this.g[a]:b}clear(){this.g={};this.i={}}};function th(a){return a instanceof Object?String(ma(a)):a+""};function vh(a){return new wh({value:a},null)}function xh(a){return new wh(null,a)}function yh(a){try{return vh(a())}catch(b){return xh(b)}}function zh(a){return a.g!=null?a.getValue():null}function Ah(a,b){a.g!=null&&b(a.getValue());return a}function Bh(a,b){a.g!=null||b(a.i);return a}var wh=class{constructor(a,b){this.g=a;this.i=b}getValue(){return this.g.value}map(a){return this.g!=null?(a=a(this.getValue()),a instanceof wh?a:vh(a)):this}};var Ch=class{constructor(a){this.g=new uh;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return this.g.g[th(a)]!==void 0}};var Dh=class{constructor(){this.g=new uh}set(a,b){let c=this.g.get(a);c||(c=new Ch,this.g.set(a,c));c.add(b)}};var Eh=class extends K{getId(){return Wc(this,3)}};var Fh=class{constructor({ob:a,kc:b,xc:c,Ib:d}){this.g=b;this.u=new rh(a||[]);this.j=d;this.i=c}};function Gh(a){const b=a.length;if(b===0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c};const Ih=a=>{const b=[],c=a.u;c&&c.g.length&&b.push({ca:"a",fa:Hh(c)});a.g!=null&&b.push({ca:"as",fa:a.g});a.i!=null&&b.push({ca:"i",fa:String(a.i)});a.j!=null&&b.push({ca:"rp",fa:String(a.j)});b.sort(function(d,e){return d.ca.localeCompare(e.ca)});b.unshift({ca:"t",fa:"aa"});return b},Hh=a=>{a=a.g.slice(0).map(Jh);a=JSON.stringify(a);return Gh(a)},Jh=a=>{const b={};w(z(a,7))!=null&&(b.q=Wc(a,7));Sc(a,2)!=null&&(b.o=Sc(a,2,sc));Sc(a,5)!=null&&(b.p=Sc(a,5,sc));return b};function Kh(a){return Xc(a,2)}var Lh=class extends K{setLocation(a){return ed(this,1,a)}};function Mh(a){const b=[].slice.call(arguments).filter(qd(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.Ua||[]);d=Object.assign(d,e.fb)});return new Nh(c,d)}function Oh(a){switch(a){case 1:return new Nh(null,{google_ad_semantic_area:"mc"});case 2:return new Nh(null,{google_ad_semantic_area:"h"});case 3:return new Nh(null,{google_ad_semantic_area:"f"});case 4:return new Nh(null,{google_ad_semantic_area:"s"});default:return null}} 
function Ph(a){if(a==null)var b=null;else{b=Nh;var c=Ih(a);a=[];for(let d of c)c=String(d.fa),a.push(d.ca+"."+(c.length<=20?c:c.slice(0,19)+"_"));b=new b(null,{google_placement_id:a.join("~")})}return b}var Nh=class{constructor(a,b){this.Ua=a;this.fb=b}};var Qh=new Nh(["google-auto-placed"],{google_reactive_ad_format:40,google_tag_origin:"qs"});var Rh=kd(class extends K{});function Sh(a){return C(a,Eh,1)}function Th(a){return Xc(a,2)}var Uh=class extends K{};var Vh=class extends K{};var Wh=class extends K{};function Xh(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b};function Yh(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){let d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}Xh(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")};var O=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},P=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},Zh=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var $h=new P(1359),ai=new P(1358),bi=new O(1360),ci=new P(1357),di=new O(1345),ei=new O(1392),fi=new P(1130,100),gi=new P(1340,.2),hi=new P(1338,.3),ii=new P(1339,.3),ji=new O(1337),ki=new class{constructor(a,b=""){this.g=a;this.defaultValue=b}}(14),li=new O(797780086,!0),mi=new O(782575400),ni=new O(1342),oi=new O(1344),pi=new P(1343,300),qi=new O(316),ri=new O(313),si=new O(369),ti=new O(45709472),ui=new O(626390500),vi=new Zh(635821288,["29_18","30_19"]),wi=new Zh(683929765),xi=new O(506914611), 
yi=new P(9604,.7),zi=new P(717888910,.5423),Ai=new P(9601,.25178),Bi=new P(9602,1),Ci=new P(643258049,.16),Di=new P(643258048,.1542),Ei=new P(9605,.5799),Fi=new P(717888911,.7),Gi=new P(9606,.65),Hi=new P(717888912,.5849),Ii=new P(9603,4),Ji=new P(748662193,4),Ki=new O(711741274),Li=new O(788556408,!0),Mi=new O(788558947),Ni=new O(805023856),Oi=new O(662101537),Pi=new P(1079,5),Qi=new O(10013),Ri=new O(772097522),Yd=new class{constructor(a,b=[]){this.g=a;this.defaultValue=b}}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==", 
"Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9nrunKdU5m96PSN1XsSGr3qOP0lvPFUB2AiAylCDlN5DTl17uDFkpQuHj1AFtgWLxpLaiBZuhrtb2WOu7ofHwEAAACKeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9", 
"A93bovR+QVXNx2/38qDbmeYYf1wdte9EO37K9eMq3r+541qo0byhYU899BhPB7Cv9QqD7wIbR1B6OAc9kEfYCA4AAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","A1S5fojrAunSDrFbD8OfGmFHdRFZymSM/1ss3G+NEttCLfHkXvlcF6LGLH8Mo5PakLO1sCASXU1/gQf6XGuTBgwAAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXRhZ3NlcnZpY2VzLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]), 
Si=new O(84);var Xd=class{constructor(){const a={};this.j=(b,c)=>a[b]!=null?a[b]:c;this.u=(b,c)=>a[b]!=null?a[b]:c;this.i=(b,c)=>a[b]!=null?a[b]:c;this.A=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.B=()=>{}}};function Q(a){return L(Xd).j(a.g,a.defaultValue)}function R(a){return L(Xd).u(a.g,a.defaultValue)};function Ti(a,b){const c=e=>{e=Ui(e);return e==null?!1:0<e},d=e=>{e=Ui(e);return e==null?!1:0>e};switch(b){case 0:return{init:Vi(a.previousSibling,c),ja:e=>Vi(e.previousSibling,c),pa:0};case 2:return{init:Vi(a.lastChild,c),ja:e=>Vi(e.previousSibling,c),pa:0};case 3:return{init:Vi(a.nextSibling,d),ja:e=>Vi(e.nextSibling,d),pa:3};case 1:return{init:Vi(a.firstChild,d),ja:e=>Vi(e.nextSibling,d),pa:3}}throw Error("Un-handled RelativePosition: "+b);} 
function Ui(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function Vi(a,b){return a&&b(a)?a:null};var Wi={rectangle:1,horizontal:2,vertical:4};var Xi={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7};function Yi(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}}function S(a){return Yi(a).clientWidth??void 0};function Zi(a,b){do{const c=Pd(a,b);if(c&&c.position==="fixed")return!1}while(a=a.parentElement);return!0};function $i(a,b){var c=["width","height"];for(let e=0;e<c.length;e++){const f="google_ad_"+c[e];if(!b.hasOwnProperty(f)){var d=Ud(a[c[e]]);d=d===null?null:Math.round(d);d!=null&&(b[f]=d)}}}function aj(a,b){return!((Sd.test(b.google_ad_width)||Rd.test(a.style.width))&&(Sd.test(b.google_ad_height)||Rd.test(a.style.height)))}function bj(a,b){return(a=cj(a,b))?a.y:0} 
function cj(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}} 
function dj(a,b,c,d,e){if(a!==a.top)return pd(a)?3:16;if(!(S(a)<488))return 4;if(!(a.innerHeight>=a.innerWidth))return 5;const f=S(a);if(!f||(f-c)/f>d)a=6;else{if(c=e.google_full_width_responsive!=="true")a:{c=b.parentElement;for(b=S(a);c;c=c.parentElement)if((d=Pd(c,a))&&(e=Ud(d.width))&&!(e>=b)&&d.overflow!=="visible"){c=!0;break a}c=!1}a=c?7:!0}return a} 
function ej(a,b,c,d){const e=dj(b,c,a,R(ii),d);e!==!0?a=e:d.google_full_width_responsive==="true"||Zi(c,b)?(b=S(b),a=b-a,a=b&&a>=0?!0:b?a<-10?11:a<0?14:12:10):a=9;return a}function fj(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function gj(a,b){if(b.nodeType===3)return/\S/.test(b.data);if(b.nodeType===1){if(/^(script|style)$/i.test(b.nodeName))return!1;let c;try{c=Pd(b,a)}catch(d){}return!c||c.display!=="none"&&!(c.position==="absolute"&&(c.visibility==="hidden"||c.visibility==="collapse"))}return!1}function hj(a,b,c){a=cj(b,a);return c==="rtl"?-a.x:a.x} 
function ij(a,b){var c;c=(c=b.parentElement)?(c=Pd(c,a))?c.direction:"":"";if(c){var d=b.style;d.border=d.borderStyle=d.outline=d.outlineStyle=d.transition="none";d.borderSpacing=d.padding="0";fj(b,c,"0px");d.width=`${S(a)}px`;if(hj(a,b,c)!==0){fj(b,c,"0px");var e=hj(a,b,c);fj(b,c,`${-1*e}px`);a=hj(a,b,c);a!==0&&a!==e&&fj(b,c,`${e/(a-e)*e}px`)}d.zIndex="30"}};function jj(a,b,c){let d;return a.style&&!!a.style[c]&&Ud(a.style[c])||(d=Pd(a,b))&&!!d[c]&&Ud(d[c])||null}function kj(a,b){const c=ih(a)===0;return b&&c?Math.max(250,2*Yi(a).clientHeight/3):250}function lj(a,b){let c;return a.style&&a.style.zIndex||(c=Pd(a,b))&&c.zIndex||null}function mj(a){return b=>b.g<=a}function nj(a,b,c,d){const e=a&&oj(c,b),f=kj(b,d);return g=>!(e&&g.height()>=f)}function pj(a){return b=>b.height()<=a}function oj(a,b){return bj(a,b)<Yi(b).clientHeight-100} 
function qj(a,b){var c=jj(b,a,"height");if(c)return c;var d=b.style.height;b.style.height="inherit";c=jj(b,a,"height");b.style.height=d;if(c)return c;c=Infinity;do(d=b.style&&Ud(b.style.height))&&(c=Math.min(c,d)),(d=jj(b,a,"maxHeight"))&&(c=Math.min(c,d));while(b.parentElement&&(b=b.parentElement)&&b.tagName!=="HTML");return c};var rj={google_ad_channel:!0,google_ad_client:!0,google_ad_host:!0,google_ad_host_channel:!0,google_adtest:!0,google_tag_for_child_directed_treatment:!0,google_tag_for_under_age_of_consent:!0,google_tag_partner:!0,google_restrict_data_processing:!0,google_page_url:!0,google_debug_params:!0,google_adbreak_test:!0,google_ad_frequency_hint:!0,google_admob_interstitial_slot:!0,google_admob_rewarded_slot:!0,google_admob_ads_only:!0,google_ad_start_delay_hint:!0,google_max_ad_content_rating:!0,google_traffic_source:!0, 
google_overlays:!0,google_privacy_treatments:!0,google_special_category_data:!0,google_ad_intent_query:!0,google_ad_intent_qetid:!0,google_ad_intent_rs_token:!0,google_ad_intents_format:!0};const sj=RegExp("(^| )adsbygoogle($| )");function tj(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=Ed(d.property);a[e]=d.value}};var uj=class extends K{g(){return Mb(z(this,23,void 0,sc))}};var vj=class extends K{g(){return Rc(this,1)}};var wj=class extends K{};var xj=class extends K{};var yj=class extends K{};var zj=class extends K{};var Aj=class extends K{getName(){return Wc(this,4)}},Bj=[1,2,3];var Cj=class extends K{};var Dj=class extends K{};var Ej=class extends K{};var Gj=class extends K{g(){return Vc(this,Ej,2,Fj)}},Fj=[1,2];var Hj=class extends K{g(){return C(this,Gj,3)}};var Ij=class extends K{},Jj=kd(Ij);function Kj(a){const b=[];sh(a.getElementsByTagName("p"),function(c){Lj(c)>=100&&b.push(c)});return b}function Lj(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;sh(a.childNodes,function(c){b+=Lj(c)});return b}function Mj(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function Nj(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function Oj(a,b){var c=[];try{c=b.querySelectorAll(a.u)}catch(d){}if(!c.length)return[];b=Oa(c);b=Nj(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=Kj(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var Pj=class{constructor(a,b,c,d){this.u=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.u,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var Qj=class{constructor(){this.i=Kd`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}J(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;se(b)||(b=new te(b,{context:a,id:d}));q.google_js_errors=q.google_js_errors||[];q.google_js_errors.push(b);q.error_rep_loaded||(Nd(q.document,this.i),q.error_rep_loaded=!0);return!1}g(a,b){try{return b()}catch(c){if(!this.J(a,c,.01,"jserror"))throw c;}}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}qa(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.J(a,c instanceof Error?c:Error(c),void 0)})}};function Rj(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Sj(a,b,c,d,e=!1){const f=d||window,g=typeof queueMicrotask!=="undefined";return function(...h){e&&g&&queueMicrotask(()=>{f.google_rum_task_id_counter=f.google_rum_task_id_counter||1;f.google_rum_task_id_counter+=1});const k=Ce();let n,l=3;try{n=b.apply(this,h)}catch(m){l=13;if(!c)throw m;c(a,m)}finally{f.google_measure_js_timing&&k&&Rj({label:a.toString(),value:k,duration:(Ce()||0)-k,type:l,...(e&&g&&{taskId:f.google_rum_task_id_counter=f.google_rum_task_id_counter||1})},f)}return n}} 
function Tj(a,b){return Sj(a,b,(c,d)=>{(new Qj).J(c,d)},void 0,!1)};function Uj(a,b,c){return Sj(a,b,void 0,c,!0).apply()}function Vj(a){if(!a)return null;var b=Wc(a,7);if(Wc(a,1)||a.getId()||xc(a,4,w,B()).length>0){var c=a.getId(),d=Wc(a,1),e=xc(a,4,w,B());b=Sc(a,2,sc);var f=Sc(a,5,sc);a=Wj(Xc(a,6));let g="";d&&(g+=d);c&&(g+="#"+Mj(c));if(e)for(c=0;c<e.length;c++)g+="."+Mj(e[c]);b=(e=g)?new Pj(e,b,f,a):null}else b=b?new Pj(b,Sc(a,2,sc),Sc(a,5,sc),Wj(Xc(a,6))):null;return b}const Xj={1:1,2:2,3:3,0:0};function Wj(a){return a==null?a:Xj[a]}const Yj={1:0,2:1,3:2,4:3}; 
function Zj(a){return a.google_ama_state=a.google_ama_state||{}}function ak(a){a=Zj(a);return a.optimization=a.optimization||{}};var bk=a=>{switch(Xc(a,8)){case 1:case 2:if(a==null)var b=null;else b=C(a,Eh,1),b==null?b=null:(a=Xc(a,2),b=a==null?null:new Fh({ob:[b],Ib:a}));return b!=null?vh(b):xh(Error("Missing dimension when creating placement id"));case 3:return xh(Error("Missing dimension when creating placement id"));default:return b="Invalid type: "+Xc(a,8),xh(Error(b))}};var ck=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function dk(a,b){const c=new Dh,d=new Ch;b.forEach(e=>{if(Vc(e,yj,1,Bj)){e=Vc(e,yj,1,Bj);if(C(e,Uh,1)&&Sh(C(e,Uh,1))&&C(e,Uh,2)&&Sh(C(e,Uh,2))){const g=ek(a,Sh(C(e,Uh,1))),h=ek(a,Sh(C(e,Uh,2)));if(g&&h)for(var f of ck({anchor:g,position:Th(C(e,Uh,1))},{anchor:h,position:Th(C(e,Uh,2))}))c.set(ma(f.anchor),f.position)}C(e,Uh,3)&&Sh(C(e,Uh,3))&&(f=ek(a,Sh(C(e,Uh,3))))&&c.set(ma(f),Th(C(e,Uh,3)))}else Vc(e,zj,2,Bj)?fk(a,Vc(e,zj,2,Bj),c):Vc(e,xj,3,Bj)&&gk(a,Vc(e,xj,3,Bj),d)});return new hk(c,d)} 
var hk=class{constructor(a,b){this.i=a;this.g=b}};const fk=(a,b,c)=>{C(b,Uh,2)?(b=C(b,Uh,2),(a=ek(a,Sh(b)))&&c.set(ma(a),Th(b))):C(b,Eh,1)&&(a=ik(a,C(b,Eh,1)))&&a.forEach(d=>{d=ma(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},gk=(a,b,c)=>{C(b,Eh,1)&&(a=ik(a,C(b,Eh,1)))&&a.forEach(d=>{c.add(ma(d))})},ek=(a,b)=>(a=ik(a,b))&&a.length>0?a[0]:null,ik=(a,b)=>(b=Vj(b))?Oj(b,a):null;function eg(){return"m202509160101"};var jk=jd(bg);var dg=jd(fg);function kk(a,b){return b(a)?a:void 0} 
function lk(a,b,c,d,e){c=c instanceof te?c.error:c;var f=new ig;const g=new hg;try{var h=Zd(window);$c(g,1,h)}catch(p){}try{var k=ah();Dc(g,2,k,Rb)}catch(p){}try{cd(g,3,window.document.URL)}catch(p){}h=Oc(f,2,g);k=new gg;b=fd(k,1,b);try{var n=sb(c?.name)?c.name:"Unknown error";cd(b,2,n)}catch(p){}try{var l=sb(c?.message)?c.message:`Caught ${c}`;cd(b,3,l)}catch(p){}try{var m=sb(c?.stack)?c.stack:Error().stack;m&&Dc(b,4,m.split(/\n\s*/),Yb)}catch(p){}n=E(h,1,jg,b);if(e){l=0;switch(e.errSrc){case "LCC":l= 
1;break;case "PVC":l=2}m=cg();b=kk(e.shv,sb);m=cd(m,2,b);l=fd(m,6,l);m=jk();m=nc(m);b=kk(e.es,ub());m=Dc(m,1,b,Rb);m=hd(m);l=Oc(l,4,m);m=kk(e.client,sb);l=bd(l,3,m);m=kk(e.slotname,sb);l=cd(l,7,m);e=kk(e.tag_origin,sb);e=cd(l,8,e);e=hd(e)}else e=hd(cg());e=E(n,6,kg,e);d=$c(e,5,d??1);mg(a,d)};var nk=class{constructor(){this.g=mk}};function mk(){return{Fb:Id()+(Id()&2**21-1)*2**32,tb:Number.MAX_SAFE_INTEGER}};var qk=class{constructor(a=!1){var b=ok;this.D=pk;this.B=a;this.F=b;this.i=null;this.j=this.J}H(a){this.j=a}A(a){this.i=a}T(){}g(a,b,c){let d;try{d=b()}catch(e){b=this.B;try{b=this.j(a,ue(e),void 0,c)}catch(f){this.J(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}qa(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.J(a,c instanceof Error?c:Error(c),void 0,void 0)})}J(a,b,c,d){try{const g=c===void 0?1/this.F:c===0?0:1/c;var e=(new nk).g(); 
if(g>0&&e.Fb*g<=e.tb){var f=this.D;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}lk(f,a,b,g,c)}}catch(g){}return this.B}};var T=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,T):this.stack=Error().stack||""}};let pk,rk,sk,tk,ok;const uk=new Je(q);function vk(a){a!=null&&(q.google_measure_js_timing=a);q.google_measure_js_timing||Ie(uk)}(function(a,b,c=!0){({Hb:ok,xb:sk}=wk());rk=a||new ch;bh(rk,sk);pk=b||new vg(2,eg(),1E3);tk=new qk(c);q.document.readyState==="complete"?vk():uk.g&&ie(q,"load",()=>{vk()})})();function xk(a,b,c){return tk.g(a,b,c)}function yk(a,b){return tk.u(a,b)}function zk(a,b){tk.qa(a,b)}function Ak(a,b,c=.01){const d=ah();!b.eid&&d.length&&(b.eid=d.toString());Ve(rk,a,b,!0,c)} 
function Bk(a,b,c=ok,d,e){return tk.J(a,b,c,d,e)}function Ck(a,b,c=ok,d,e){return(se(b)?b.msg||ve(b.error):ve(b)).indexOf("TagError")===0?((se(b)?b.error:b).pbr=!0,!1):Bk(a,b,c,d,e)}function wk(){let a,b;typeof q.google_srt==="number"?(b=q.google_srt,a=q.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Hb:a,xb:b}};var Dk=class{constructor(){var a=Math.random;this.g=Math.floor(a()*2**52);this.i=0}};function Ek(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(Fk(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function Gk(a){a=Hk(a);return a.has("all")||a.has("after")}function Ik(a){a=Hk(a);return a.has("all")||a.has("before")}function Hk(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function Fk(a){const b=Hk(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var Jk=class{constructor(){this.g=new Set;this.i=new Dk}};function Kk(a,b){if(!a)return!1;a=Pd(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function Lk(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null}function Mk(a){return!!a.nextSibling||!!a.parentNode&&Mk(a.parentNode)};function Nk(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function Ok(a){return{jc:Pk(a),lc:U(a,"body ins.adsbygoogle"),lb:Qk(a),mb:U(a,".google-auto-placed"),nb:Sk(a),ub:Tk(a),qc:Uk(a),zc:Vk(a),Eb:Wk(a),oc:U(a,"div.googlepublisherpluginad"),Pb:U(a,"html > ins.adsbygoogle")}}function Uk(a){return Xk(a)||U(a,"div[id^=div-gpt-ad]")}function Xk(a){const b=Nk(a);return b?Ja(Ka(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function U(a,b){return Oa(a.document.querySelectorAll(b))} 
function Sk(a){return U(a,"ins.adsbygoogle[data-anchor-status]")}function Qk(a){return U(a,"iframe[id^=aswift_],iframe[id^=google_ads_frame]")}function Vk(a){return U(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")}function Tk(a){return Uk(a).concat(U(a,"iframe[id^=google_ads_iframe]"))} 
function Wk(a){return U(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function Pk(a){return U(a,"ins.adsbygoogle-ablated-ad-slot")}function Yk(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};function Zk(a,b){if(a.u)return!0;a.u=!0;const c=D(a.j,Wh,1,B());a.i=0;const d=$k(a.F);var e=a.g;var f;try{var g=(f=e.localStorage.getItem("google_ama_settings"))?Rh(f):null}catch(v){g=null}f=g!==null&&F(g,2);g=Zj(e);f&&(g.eatf=!0,re(7,[!0,0,!1]));b:{var h={zb:!1,Ab:!1},k=U(e,".google-auto-placed"),n=Sk(e),l=Vk(e),m=Tk(e);const v=Wk(e),t=Pk(e),y=U(e,"div.googlepublisherpluginad"),G=U(e,"html > ins.adsbygoogle");let ia=[].concat(...Qk(e),...U(e,"body ins.adsbygoogle"));f=[];for(const [Ra,Sa]of[[h.sc, 
k],[h.zb,n],[h.vc,l],[h.tc,m],[h.wc,v],[h.rc,t],[h.uc,y],[h.Ab,G]])Ra===!1?f=f.concat(Sa):ia=ia.concat(Sa);h=Yk(ia);f=Yk(f);h=h.slice(0);for(p of f)for(f=0;f<h.length;f++)(p.contains(h[f])||h[f].contains(p))&&h.splice(f,1);var p=h;e=Yi(e).clientHeight;for(f=0;f<p.length;f++)if(!(p[f].getBoundingClientRect().top>e)){e=!0;break b}e=!1}e=e?g.eatfAbg=!0:!1;if(e)return!0;e=new Ch([2]);for(g=0;g<c.length;g++){p=a;h=c[g];f=g;k=b;(n=!C(h,Lh,4))||(n=e,l=n.contains,m=C(h,Lh,4),m=Xc(m,1),n=!l.call(n,m));if(n|| 
Xc(h,8)!==1||!al(h,d))p=null;else{p.i++;if(k=bl(p,h,k,d))n=Zj(p.g),n.numAutoAdsPlaced||(n.numAutoAdsPlaced=0),(l=!C(h,Eh,1))||(h=C(h,Eh,1),l=Sc(h,5)==null),l||(n.numPostPlacementsPlaced?n.numPostPlacementsPlaced++:n.numPostPlacementsPlaced=1),n.placed==null&&(n.placed=[]),n.numAutoAdsPlaced++,n.placed.push({index:f,element:k.ha}),re(7,[!1,p.i,!0]);p=k}if(p)return!0}re(7,[!1,a.i,!1]);return!1} 
function bl(a,b,c,d){if(!al(b,d)||Qb(z(b,8))!=1)return null;d=C(b,Eh,1);if(!d)return null;d=Vj(d);if(!d)return null;d=Oj(d,a.g.document);if(d.length==0)return null;d=d[0];var e=Xc(b,2);e=Yj[e];e=e===void 0?null:e;var f;if(!(f=e==null)){a:{f=a.g;switch(e){case 0:f=Kk(Lk(d),f);break a;case 3:f=Kk(d,f);break a;case 2:var g=d.lastChild;f=Kk(g?g.nodeType==1?g:Lk(g):null,f);break a}f=!1}if(c=!f&&!(!c&&e==2&&!Mk(d)))c=e==1||e==2?d:d.parentNode,c=!(c&&!Xh(c)&&c.offsetWidth<=0);f=!c}if(!(c=f)){c=a.B;f=Xc(b, 
2);g=c.i;var h=ma(d);g=g.g.get(h);if(!(g=g?g.contains(f):!1))a:{if(c.g.contains(ma(d)))switch(f){case 2:case 3:g=!0;break a;default:g=!1;break a}for(f=d.parentElement;f;){if(c.g.contains(ma(f))){g=!0;break a}f=f.parentElement}g=!1}c=g}if(!c){c=a.D;g=Xc(b,2);a:switch(g){case 1:f=Gk(d.previousElementSibling)||Ik(d);break a;case 4:f=Gk(d)||Ik(d.nextElementSibling);break a;case 2:f=Ik(d.firstElementChild);break a;case 3:f=Gk(d.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+ 
g);}g=Ek(c,d,g);c=c.i;Ak("ama_exclusion_zone",{typ:f?g?"siuex":"siex":g?"suex":"noex",cor:c.g,num:c.i++,dvc:Fd()},.1);c=f||g}if(c)return null;f=C(b,Vh,3);c={};f&&(c.jb=Wc(f,1),c.Sa=Wc(f,2),c.sb=!!Mb(z(f,3,void 0,sc)));f=C(b,Lh,4)&&Kh(C(b,Lh,4))?Kh(C(b,Lh,4)):null;f=Oh(f);g=Sc(b,12)!=null?Sc(b,12,sc):null;g=g==null?null:new Nh(null,{google_ml_rank:g});b=cl(a,b);b=Mh(a.A,f,g,b);f=a.g;a=a.H;h=f.document;var k=c.sb||!1;g=ge((new he(h)).g,"DIV");const n=g.style;n.width="100%";n.height="auto";n.clear=k? 
"both":"none";k=g.style;k.textAlign="center";c.Gb&&tj(k,c.Gb);h=ge((new he(h)).g,"INS");k=h.style;k.display="block";k.margin="auto";k.backgroundColor="transparent";c.jb&&(k.marginTop=c.jb);c.Sa&&(k.marginBottom=c.Sa);c.kb&&tj(k,c.kb);g.appendChild(h);c={wa:g,ha:h};c.ha.setAttribute("data-ad-format","auto");g=[];if(h=b&&b.Ua)c.wa.className=h.join(" ");h=c.ha;h.className="adsbygoogle";h.setAttribute("data-ad-client",a);g.length&&h.setAttribute("data-ad-channel",g.join("+"));a:{try{var l=c.wa;if(Q(ri)){{const y= 
Ti(d,e);if(y.init){var m=y.init;for(d=m;d=y.ja(d);)m=d;var p={anchor:m,position:y.pa}}else p={anchor:d,position:e}}l["google-ama-order-assurance"]=0;Yh(l,p.anchor,p.position)}else Yh(l,d,e);b:{var v=c.ha;v.dataset.adsbygoogleStatus="reserved";v.className+=" adsbygoogle-noablate";l={element:v};var t=b&&b.fb;if(v.hasAttribute("data-pub-vars")){try{t=JSON.parse(v.getAttribute("data-pub-vars"))}catch(y){break b}v.removeAttribute("data-pub-vars")}t&&(l.params=t);(f.adsbygoogle=f.adsbygoogle||[]).push(l)}}catch(y){(v= 
c.wa)&&v.parentNode&&(t=v.parentNode,t.removeChild(v),Xh(t)&&(t.style.display=t.getAttribute("data-init-display")||"none"));v=!1;break a}v=!0}return v?c:null}function cl(a,b){return zh(Bh(bk(b).map(Ph),c=>{Zj(a.g).exception=c}))}var dl=class{constructor(a,b,c,d,e){this.g=a;this.H=b;this.j=c;this.A=e||null;(this.F=d)?(a=a.document,d=D(d,Aj,5,B()),d=dk(a,d)):d=dk(a.document,[]);this.B=d;this.D=new Jk;this.i=0;this.u=!1}};function $k(a){const b={};a&&xc(a,6,Qb,B()).forEach(c=>{b[c]=!0});return b} 
function al(a,b){return a&&vc(a,Lh,4)&&b[Kh(C(a,Lh,4))]?!1:!0};var el=kd(class extends K{});function fl(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?yh(()=>el(c)):vh(null)};function gl(){if(hl)return hl;var a=gh()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?hl=b:a.google_persistent_state_async=hl=new il}function jl(a){return kl[a]||`google_ps_${a}`}function ll(a,b,c){b=jl(b);a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function ml(a,b,c){return ll(a,b,()=>c)}var il=class{constructor(){this.S={}}},hl=null;const kl={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function nl(a){this.g=a||{cookie:""}} 
nl.prototype.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.Ac,g=c.Bc||!1,f=c.domain||void 0,e=c.path||void 0,d=c.Cb);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
nl.prototype.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=ua(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};nl.prototype.isEmpty=function(){return!this.g.cookie}; 
nl.prototype.clear=function(){var a=(this.g.cookie||"").split(";");const b=[];var c=[];let d,e;for(let f=0;f<a.length;f++)e=ua(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(c=b.length-1;c>=0;c--)a=b[c],this.get(a),this.set(a,"",{Cb:0,path:void 0,domain:void 0})};function ol(a,b=window){if(F(a,5))try{return b.localStorage}catch{}return null};function pl(a){var b=new ql;return uc(b,5,Lb(a))}var ql=class extends K{};function rl(){this.A=this.A;this.i=this.i}rl.prototype.A=!1;rl.prototype.dispose=function(){this.A||(this.A=!0,this.D())};rl.prototype[fa(Symbol,"dispose")]=function(){this.dispose()};function sl(a,b){a.A?b():(a.i||(a.i=[]),a.i.push(b))}rl.prototype.D=function(){if(this.i)for(;this.i.length;)this.i.shift()()};function tl(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function ul(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=tl(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(oe({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function vl(a){if(a.g)return a.g;a:{let d=a.j;for(let e=0;e<50;++e){try{var b=!(!d.frames||!d.frames.__tcfapiLocator)}catch{b=!1}if(b){b=d;break a}b:{try{const f=d.parent;if(f&&f!=d){var c=f;break b}}catch{}c=null}if(!(d=c))break}b=null}a.g=b;return a.g}function wl(a,b,c,d){c||(c=()=>{});var e=a.j;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):vl(a)?(xl(a),e=++a.T,a.B[e]=c,a.g&&a.g.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function xl(a){if(!a.u){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.B[d.callId](d.returnValue,d.success)}catch(e){}};a.u=b;ie(a.j,"message",b)}} 
var yl=class extends rl{constructor(a){var b={};super();this.g=null;this.B={};this.T=0;this.u=null;this.j=a;this.H=b.ib??500;this.F=b.mc??!1}D(){this.B={};this.u&&(je(this.j,"message",this.u),delete this.u);delete this.B;delete this.j;delete this.g;super.D()}addEventListener(a){let b={internalBlockOnErrors:this.F};const c=sd(()=>a(b));let d=0;this.H!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.H));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
tl(b),b.internalBlockOnErrors=this.F,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{wl(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&wl(this,"removeEventListener",null,a.listenerId)}};var Dl=({l:a,ba:b,ib:c,rb:d,ka:e=!1,la:f=!1})=>{b=zl({l:a,ba:b,ka:e,la:f});b.g!=null||b.i.message!="tcunav"?d(b):Al(a,c).then(g=>g.map(Bl)).then(g=>g.map(h=>Cl(a,h))).then(d)},zl=({l:a,ba:b,ka:c=!1,la:d=!1})=>{if(!El({l:a,ba:b,ka:c,la:d}))return Cl(a,pl(!0));b=gl();return(b=ml(b,24))?Cl(a,Bl(b)):xh(Error("tcunav"))}; 
function El({l:a,ba:b,ka:c,la:d}){if(d=!d)d=new yl(a),d=typeof d.j.__tcfapi==="function"||vl(d)!=null;if(!d){if(c=!c){if(b){a=fl(a);if(a.g!=null)if((a=a.getValue())&&Qb(z(a,1))!=null)b:switch(a=J(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else Bk(806,a.i),a=!1;b=!a}c=b}d=c}return d?!0:!1}function Al(a,b){return Promise.race([Fl(),Gl(a,b)])} 
function Fl(){return(new Promise(a=>{var b=gl();a={resolve:a};const c=ml(b,25,[]);c.push(a);b.S[jl(25)]=c})).then(Hl)}function Gl(a,b){return new Promise(c=>{a.setTimeout(c,b,xh(Error("tcto")))})}function Hl(a){return a?vh(a):xh(Error("tcnull"))} 
function Bl(a){var b={};if(ul(a))if(a.gdprApplies===!1)a=!0;else if(a.tcString==="tcunavailable")a=!b.Xa;else if((b.Xa||a.gdprApplies!==void 0||b.nc)&&(b.Xa||typeof a.tcString==="string"&&a.tcString.length)){b:{if(a.publisher&&a.publisher.restrictions&&(b=a.publisher.restrictions["1"],b!==void 0)){b=b["755"];break b}b=void 0}b===0?a=!1:a.purpose&&a.vendor?(b=a.vendor.consents,(b=!(!b||!b["755"]))&&a.purposeOneTreatment&&a.publisherCC==="CH"?a=!0:(b&&(a=a.purpose.consents,b=!(!a||!a["1"])),a=b)):a= 
!0}else a=!0;else a=!1;return pl(a)}function Cl(a,b){return(a=ol(b,a))?vh(a):xh(Error("unav"))};var Il=class extends K{};var Jl=class extends K{};var Kl=class{constructor(a){this.exception=a}};function Ll(a,b){try{var c=a.i,d=c.resolve,e=a.g;Zj(e.g);D(e.j,Wh,1,B());d.call(c,new Kl(b))}catch(f){a.i.reject(f)}}var Ml=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}start(){this.u()}u(){try{switch(this.j.document.readyState){case "complete":case "interactive":Zk(this.g,!0);Ll(this);break;default:Zk(this.g,!1)?Ll(this):this.j.setTimeout(ra(this.u,this),100)}}catch(a){Ll(this,a)}}};var Nl=class extends K{getVersion(){return H(this,2)}};function Ol(a){return Ta(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function Pl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function Ql(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function Rl(a){var b=Ol(a),c=Pl(b.slice(0,6));a=Pl(b.slice(6,12));var d=new Nl;c=Zc(d,1,c);a=Zc(c,2,a);b=b.slice(12);c=Pl(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let k=0;k<c;k++){if(e.length===0)throw Error(`Found ${k} of ${c} sections [${d}] but reached end of input [${b}]`);var f=Pl(e[0])===0;e=e.slice(1);var g=Sl(e,b),h=d.length===0?0:d[d.length-1];h=Ql(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=Sl(e,b);g=Ql(f);for(let n=0;n<=g;n++)d.push(h+n);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return Dc(a,3,d,Rb)}function Sl(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var Tl="a".charCodeAt(),Ul=ee(lh),Vl=ee(mh);function Wl(){var a=new Xl;return $c(a,1,0)}function Yl(a){var b=Number;{var c=z(a,1);const d=typeof c;c=c==null?c:d==="bigint"?String(Gb(64,c)):Ob(c)?d==="string"?Vb(c):Xb(c):void 0}b=b(c??"0");a=H(a,2);return new Date(b*1E3+a/1E6)}var Xl=class extends K{};function W(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function Zl(a){let b=W(a,12);const c=[];for(;b--;){var d=!!W(a,1)===!0,e=W(a,16);if(d)for(d=W(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c}function $l(a,b,c){const d=[];for(let e=0;e<b;e++)if(W(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d} 
function am(a){const b=W(a,16);return!!W(a,1)===!0?(a=Zl(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):$l(a,b)}var bm=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var dm=(a,b)=>{try{var c=Ta(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new bm(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=W(d,12);c.cmpVersion=W(d,12);d.skip(30);c.tcfPolicyVersion=W(d,6);c.isServiceSpecific=!!W(d,1);c.useNonStandardStacks=!!W(d,1);c.specialFeatureOptins=cm($l(d,12,Vl),Vl);c.purpose={consents:cm($l(d,24,Ul),Ul),legitimateInterests:cm($l(d,24,Ul),Ul)};c.purposeOneTreatment=!!W(d,1);c.publisherCC=String.fromCharCode(Tl+W(d,6))+String.fromCharCode(Tl+ 
W(d,6));c.vendor={consents:cm(am(d),null),legitimateInterests:cm(am(d),null)};return c}catch(d){return null}};const cm=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var em=class extends K{g(){return w(z(this,2))!=null}};var fm=class extends K{g(){return w(z(this,2))!=null}};var gm=class extends K{};var hm=kd(class extends K{});function im(a){a=jm(a);try{var b=a?hm(a):null}catch(c){b=null}return b?C(b,gm,4)||null:null}function jm(a){a=(new nl(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};ee(lh).map(a=>Number(a));ee(mh).map(a=>Number(a));function km(a){a.__tcfapiPostMessageReady||lm(new mm(a))} 
function lm(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.l.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.l.addEventListener("message",a.g);a.l.__tcfapiPostMessageReady=!0}var mm=class{constructor(a){this.l=a}};function nm(a){a.__uspapiPostMessageReady||om(new pm(a))} 
function om(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.l.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.l.addEventListener("message",a.g);a.l.__uspapiPostMessageReady=!0} 
var pm=class{constructor(a){this.l=a;this.g=null}};var qm=class extends K{};var rm=kd(class extends K{g(){return w(z(this,1))!=null}});function sm(a,b){function c(m){if(m.length<10)return null;var p=h(m.slice(0,4));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function d(m){if(m.length<10)return null;var p=h(m.slice(0,6));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function e(m){if(m.length<12)return null;var p=h(m.slice(0,6));p=k(p);m=h(m.slice(8,12));m=n(m);return"1"+p+m+"N"}function f(m){if(m.length<18)return null;var p=h(m.slice(0,8));p=k(p);m=h(m.slice(12,18));m=n(m);return"1"+p+m+"N"}function g(m){if(m.length<10)return null; 
var p=h(m.slice(0,6));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function h(m){const p=[];let v=0;for(let t=0;t<m.length/2;t++)p.push(Pl(m.slice(v,v+2))),v+=2;return p}function k(m){return m.every(p=>p===1)?"Y":"N"}function n(m){return m.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=Ol(a[0]);const l=Pl(a.slice(0,6));a=a.slice(6);if(l!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function tm(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=Od("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function um(a){if(a!=null)return vm(a)}function vm(a){return yb(a)?Number(a):String(a)};function wm(a){M===M.top&&(a=new xm(a),ym(a),zm(a))}function ym(a){!a.j||a.l.__uspapi||a.l.frames.__uspapiLocator||(a.l.__uspapiManager="fc",tm(a.l,"__uspapiLocator"),sa("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i.j(),d({version:1,uspString:b?a.j:"1---"},!0))},a.l),nm(a.l))} 
function zm(a){!a.tcString||a.l.__tcfapi||a.l.frames.__tcfapiLocator||(a.l.__tcfapiManager="fc",tm(a.l,"__tcfapiLocator"),a.l.__tcfapiEventListeners=a.l.__tcfapiEventListeners||[],sa("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.l.__tcfapiEventListeners;c=a.i.g();switch(b){case "ping":d({gdprApplies:c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":b=f.push(d)-1;a.tcString? 
(e=dm(a.tcString,c),e.addtlConsent=a.g!=null?a.g:void 0,e.cmpStatus="loaded",e.eventStatus="tcloaded",b!=null&&(e.listenerId=b),b=e):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.l),km(a.l))} 
function Am(a){if(!a?.g()||I(a,1).length===0||D(a,qm,2,B()).length===0)return null;const b=I(a,1);let c;try{var d=Rl(b.split("~")[0]);c=b.includes("~")?b.split("~").slice(1):[]}catch(e){return null}a=D(a,qm,2,B()).reduce((e,f)=>{var g=Bm(e);g=Rc(g,1)??rc;g=vm(g);var h=Bm(f);h=Rc(h,1)??rc;return g>vm(h)?e:f});d=xc(d,3,Sb,B()).indexOf(H(a,1));return d===-1||d>=c.length?null:{uspString:sm(c[d],H(a,1)),va:Yl(Bm(a))}} 
function Cm(a){a=a.find(b=>b&&J(b,1)===13);if(a?.g())try{return rm(I(a,2))}catch(b){}return null}function Bm(a){return vc(a,Xl,2)?C(a,Xl,2):Wl()} 
var xm=class{constructor(a){var b=M;this.l=b;this.i=a;a=jm(this.l.document);try{var c=a?hm(a):null}catch(e){c=null}(a=c)?(c=C(a,fm,5)||null,a=D(a,em,7,B()),a=Cm(a??[]),c={Ta:c,Wa:a}):c={Ta:null,Wa:null};a=c;c=Am(a.Wa);a=a.Ta;if(a?.g()&&I(a,2).length!==0){var d=vc(a,Xl,1)?C(a,Xl,1):Wl();a={uspString:I(a,2),va:Yl(d)}}else a=null;this.j=a&&c?c.va>a.va?c.uspString:a.uspString:a?a.uspString:c?c.uspString:null;this.tcString=(c=im(b.document))&&w(z(c,1))!=null?I(c,1):null;this.g=(b=im(b.document))&&w(z(b, 
2))!=null?I(b,2):null}};const Dm={google_ad_channel:!0,google_ad_host:!0};function Em(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));Ak("ama",b,.01)}function Fm(a){const b={};nd(Dm,(c,d)=>{a.hasOwnProperty(d)&&(b[d]=a[d])});return b};function Gm(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function Hm(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function Im(a){a=xc(a,2,Qb,B());if(!a)return!1;for(let b=0;b<a.length;b++)if(a[b]==1)return!0;return!1}function Jm(a,b){a=Hm(Gm(a.location.pathname)).replace(/(^\/)|(\/$)/g,"");const c=Gh(a),d=Km(a);return b.find(e=>{if(vc(e,wj,7)){var f=C(e,wj,7);f=Tb(z(f,1,void 0,sc))}else f=Tb(z(e,1,void 0,sc));vc(e,wj,7)?(e=C(e,wj,7),e=Xc(e,2)):e=2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null} 
function Km(a){const b={};for(;;){b[Gh(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};function X(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function Lm(a){a=X(a);const b=a.space_collapsing||"none";return a.remove_ads_by_default?{Pa:!0,Nb:b,Qa:a.ablation_viewport_offset}:null}function Mm(a){a=X(a);a.had_ads_ablation=!0;a.remove_ads_by_default=!0;a.space_collapsing="slot";a.ablation_viewport_offset=1}function Nm(a){X(M).allow_second_reactive_tag=a}function Om(){const a=X(window);a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function Pm(a){return X(a)?.head_tag_slot_vars?.google_ad_host??Qm(a)}function Qm(a){return a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")??null};const Rm=[2,7,1];function Sm(a,b,c,d=""){return b===1&&c&&(Tm(a,d,c)?.F()??!1)?!0:Um(a,d,e=>La(D(e,ld,2,B()),f=>Xc(f,1)===b),!!C(c,Vm,26)?.g())}function Wm(a,b){const c=pd(M)||M;return Xm(c,a)?!0:Um(M,"",d=>La(xc(d,3,Qb,B()),e=>e===a),b)}function Xm(a,b){a=(a=(a=a.location&&a.location.hash)&&a.match(/forced_clientside_labs=([\d,]+)/))&&a[1];return!!a&&Na(a.split(","),b.toString())} 
function Um(a,b,c,d){a=pd(a)||a;const e=Ym(a,d);b&&(b=jh(String(b)));return de(e,(f,g)=>Object.prototype.hasOwnProperty.call(e,g)&&(!b||b===g)&&c(f))}function Ym(a,b){a=Zm(a,b);const c={};nd(a,(d,e)=>{try{const f=id(md,fc(d));c[e]=f}catch(f){}});return c}function Zm(a,b){a=zl({l:a,ba:b});return a.g!=null?$m(a.getValue()):{}} 
function $m(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:ce(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}}function an(a,b){const c=[];a=Pm(q)?Rm:(a=Tm(q,a,b)?.T())?[...xc(a,3,Qb,B())]:Rm;a.includes(1)||c.push(1);a.includes(2)||c.push(2);a.includes(7)||c.push(7);return c} 
function Tm(a,b,c){if(!b)return null;var d=bn(c)?.j(),e=bn(c)?.g()?.g();b=b??"";d=d??"";e=e??"";var f=I(c,17)||"";return d===b||e===b&&a.location.host&&f===a.location.host?bn(c):null};function cn(a,b,c,d){dn(new en(a,b,c,d))}function dn(a){const b=!!C(a.V,Vm,26)?.g();Bh(Ah(zl({l:a.l,ba:b}),c=>{fn(a,c,!0)}),()=>{gn(a)})}function fn(a,b,c){Bh(Ah(hn(b),d=>{jn("ok");a.g(d,{fromLocalStorage:!0})}),()=>{var d=a.l;try{b.removeItem("google_ama_config")}catch(e){Em(d,{lserr:1})}c?gn(a):a.g(null,null)})}function gn(a){Bh(Ah(kn(a),b=>{a.g(b,{fromPABGSettings:!0})}),()=>{ln(a)})} 
function hn(a){if(Q(qi))var b=null;else try{b=a.getItem("google_ama_config")}catch(d){b=null}try{var c=b?Jj(b):null}catch(d){c=null}return(a=(a=c)?(um(C(a,vj,3)?.g())??0)>Date.now()?a:null:null)?vh(a):xh(Error("invlocst"))}function kn(a){if(Pm(a.l)&&!F(a.V,22))return xh(Error("invtag"));if(a=(a=Tm(a.l,a.i,a.V)?.H())&&D(a,Wh,1,B()).length>0?a:null){var b=new Ij;var c=D(a,Wh,1,B());b=Pc(b,1,c);a=D(a,Cj,2,B());a=Pc(b,7,a);a=vh(a)}else a=xh(Error("invtag"));return a} 
function ln(a){const b=!!C(a.V,Vm,26)?.g();Dl({l:a.l,ba:b,ib:50,rb:c=>{mn(a,c)}})}function mn(a,b){Bh(Ah(b,c=>{fn(a,c,!1)}),c=>{jn(c.message);a.g(null,null)})}function jn(a){Ak("abg::amalserr",{status:a,guarding:"true",timeout:50,rate:.01},.01)}class en{constructor(a,b,c,d){this.l=a;this.V=b;this.i=c;this.g=d}};function nn(a,b,c,d){var e=on;try{const f=Jm(a,D(c,Cj,7,B()));if(f&&Im(f)){if(w(z(f,4))){const h=new Nh(null,{google_package:w(z(f,4))});d=Mh(d,h)}const g=e(a,b,c,f,d);Uj(1E3,()=>{const h=new qh;(new Ml(a,g,h)).start();return h.i},a).then(()=>{Em(a,{atf:1})},h=>{(a.google_ama_state=a.google_ama_state||{}).exception=h;Em(a,{atf:0})})}}catch(f){Em(a,{atf:-1})}}function on(a,b,c,d,e){return new dl(a,b,c,d,e)};function pn(a){return a.length?a.join("~"):void 0};function qn(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;let c="";for(const d of b.split("_"))c+=d.substring(0,2);b=c;return b!="go"&&a.indexOf(b)!=-1?!0:!1};function rn(){const a={};L(Xd).i(ki.g,ki.defaultValue)&&(a.bust=L(Xd).i(ki.g,ki.defaultValue));return a};class sn{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function tn(){const {promise:a,resolve:b}=new sn;return{promise:a,resolve:b}};function un(a=()=>{}){q.google_llp||(q.google_llp={});const b=q.google_llp;let c=b[7];if(c)return c;c=tn();b[7]=c;a();return c}function vn(a){return un(()=>{Nd(q.document,a)}).promise};Array.from({length:11},(a,b)=>b/10);var wn=class{constructor(){this.g=this.i=1}takeNextPageEventIndex(){return this.i++}takeNextAnnotationEntryId(){return this.g++}};function xn(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[]),a.google_reactive_ads_global_state.adIntentsPageState==null&&(a.google_reactive_ads_global_state.adIntentsPageState=new wn)):a.google_reactive_ads_global_state=new yn;return a.google_reactive_ads_global_state} 
var yn=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new zn;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1;this.adIntentsPageState=new wn}},zn=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};function An(a){if(q.google_apltlad||a.google_ad_intent_query)return null;var b=a.google_loader_used!=="sd"&&(q.top==q?0:od(q.top)?1:2)===1;if(q!==q.top&&!b||!a.google_ad_client)return null;q.google_apltlad=!0;b={enable_page_level_ads:{pltais:!0},google_ad_client:a.google_ad_client};const c=b.enable_page_level_ads;nd(a,(d,e)=>{rj[e]&&e!=="google_ad_client"&&(c[e]=d)});c.google_pgb_reactive=7;c.asro=Q(xi);c.aihb=Q(ui);c.aifxl=pn(L(Xd).g(vi.g,vi.defaultValue));R(Di)&&(c.aiapm=R(Di));R(Ci)&&(c.aiapmi= 
R(Ci));R(zi)&&(c.aiact=R(zi));R(Fi)&&(c.aicct=R(Fi));R(Hi)&&(c.ailct=R(Hi));R(Ji)&&(c.aimart=R(Ji));c.aiapmd=R(Ai);c.aiapmid=R(Bi);c.aiactd=R(yi);c.aicctd=R(Ei);c.ailctd=R(Gi);c.aimartd=R(Ii);c.aiof=pn(L(Xd).g(wi.g,wi.defaultValue));if("google_ad_section"in a||"google_ad_region"in a)c.google_ad_section=a.google_ad_section||a.google_ad_region;return b};function Bn(a,b){X(M).ama_ran_on_page||Uj(1001,()=>{Cn(new Dn(a,b))},q)}function Cn(a){cn(a.l,a.V,a.g.google_ad_client||"",(b,c)=>{var d=a.l,e=a.g;X(M).ama_ran_on_page||b&&En(d,e,b,c)})}class Dn{constructor(a,b){this.l=q;this.g=a;this.V=b}} 
function En(a,b,c,d){d&&(Zj(a).configSourceInAbg=d);vc(c,Hj,24)&&(d=ak(a),d.availableAbg=!0,d.ablationFromStorage=!!C(c,Hj,24)?.g()?.g());if(la(b.enable_page_level_ads)&&b.enable_page_level_ads.google_pgb_reactive===7){if(!Jm(a,D(c,Cj,7,B()))){Ak("amaait",{value:"true"});return}Ak("amaait",{value:"false"})}X(M).ama_ran_on_page=!0;C(c,uj,15)?.g()&&(X(a).enable_overlap_observer=!0);C(c,Hj,24)?.g()?.g()&&(ak(a).ablatingThisPageview=!0,Mm(a));re(3,[x(c)]);const e=b.google_ad_client||"";b=Fm(la(b.enable_page_level_ads)? 
b.enable_page_level_ads:{});const f=Mh(Qh,new Nh(null,b));xk(782,()=>{nn(a,e,c,f)})};function Fn(a,b){a=a.document;for(var c=void 0,d=0;!c||a.getElementById(c+"_host");)c="aswift_"+d++;a=c;c=Number(b.google_ad_width||0);b=Number(b.google_ad_height||0);d=document.createElement("div");d.id=a+"_host";const e=d.style;e.border="none";e.height=`${b}px`;e.width=`${c}px`;e.margin="0px";e.padding="0px";e.position="relative";e.visibility="visible";e.backgroundColor="transparent";e.display="inline-block";return{yb:a,Qb:d}};function Gn(a){return a.google_ad_client?String(a.google_ad_client):X(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??""};var Hn={"120x90":!0,"160x90":!0,"180x90":!0,"200x90":!0,"468x15":!0,"728x15":!0};function In(a,b){if(b==15){if(a>=728)return 728;if(a>=468)return 468}else if(b==90){if(a>=200)return 200;if(a>=180)return 180;if(a>=160)return 160;if(a>=120)return 120}return null};function Jn(a,b){for(var c=[],d=0;b&&d<25;++d){var e=void 0;e=(e=b.nodeType!==9&&b.id)?"/"+e:"";a:{if(b&&b.nodeName&&b.parentElement){var f=b.nodeName.toString().toLowerCase();const g=b.parentElement.childNodes;let h=0;for(let k=0;k<g.length;++k){const n=g[k];if(n.nodeName&&n.nodeName.toString().toLowerCase()===f){if(b===n){f="."+h;break a}++h}}}f=""}c.push((b.nodeName&&b.nodeName.toString().toLowerCase())+e+f);b=b.parentElement}b=c.join();c=[];if(a)try{let g=a.parent;for(d=0;g&&g!==a&&d<25;++d){const h= 
g.frames;for(e=0;e<h.length;++e)if(a===h[e]){c.push(e);break}a=g;g=a.parent}}catch(g){}return Gh(`${b}:${c.join()}`).toString()};var Kn=class extends K{getVersion(){return I(this,2)}};function Ln(a,b){return bd(a,2,b)}function Mn(a,b){return bd(a,3,b)}function Nn(a,b){return bd(a,4,b)}function On(a,b){return bd(a,5,b)}function Pn(a,b){return bd(a,9,b)}function Qn(a,b){return Pc(a,10,b)}function Rn(a,b){return uc(a,11,Lb(b))}function Sn(a,b){return bd(a,1,b)}function Tn(a,b){return uc(a,7,Lb(b))}var Un=class extends K{};const Vn="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Wn(){var a=M;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Vn).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} 
function Xn(a){return Rn(Qn(On(Ln(Sn(Nn(Tn(Pn(Mn(new Un,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new Kn;c=bd(c,1,b.brand);return bd(c,2,b.version)})||[]),a.wow64||!1)}function Yn(){return Wn()?.then(a=>Xn(a))??null};function Zn(a,b){b.google_ad_host||(a=Qm(a))&&(b.google_ad_host=a)}function $n(a,b,c=""){M.google_sa_queue||(M.google_sa_queue=[],M.google_process_slots=yk(215,()=>{ao(M.google_sa_queue)}),a=bo(c,a,b),Nd(M.document,a))}function ao(a){const b=a.shift();typeof b==="function"&&xk(216,b);a.length&&q.setTimeout(yk(215,()=>{ao(a)}),0)}function co(a,b){a.google_sa_queue=a.google_sa_queue||[];a.google_sa_impl?b():a.google_sa_queue.push(b)} 
function bo(a,b,c){var d=M;if(F(c,4))if(Q(Ri)){var e=a??"";const g=bn(c)?.g()?.g()||"",h=bn(c)?.j()||"";c=!(e&&(g===e||h===e)&&d.location.host&&I(c,17)===d.location.host)}else c=!0;else c=!1;b=c?b.Jb:b.Kb;if(c)if(a=a||Gn(d)){b:{try{for(;d;){if(d.location?.hostname){var f=d.location.hostname;break b}d=d.parent}}catch(g){}f=""}f={client:jh(a),plah:f}}else throw Error("PublisherCodeNotFoundForAma");else f={};f={...f,...rn()};return Ld(b,new Map(Object.entries(f)))} 
function eo(){var a=pd(q);a&&(a=xn(a),a.tagSpecificState[1]||(a.tagSpecificState[1]={debugCard:null,debugCardRequested:!1}))}function fo(){const a=Yn();a!=null&&a.then(b=>{M.google_user_agent_client_hint=b.B()});Wd()} 
function go(a){var b=a.google_ad_output;let c=a.google_ad_format;const d=a.google_ad_width||0,e=a.google_ad_height||0;c||b!=="html"&&b!=null||(c=`${d}x${e}`);Q(Qi)&&(a.google_reactive_ad_format===10?c="interstitial":a.google_reactive_ad_format===11&&(c="rewarded"));b=!a.google_ad_slot||a.google_override_format||!Hn[a.google_ad_width+"x"+a.google_ad_height]&&a.google_loader_used==="aa";c=c&&b?c.toLowerCase():"";a.google_ad_format=c} 
function ho(a,b){b=[b.google_ad_slot,b.google_orig_ad_format||b.google_ad_format,b.google_ad_type,Q(Mi)?b.google_ad_width:b.google_orig_ad_width||b.google_ad_width,Q(Li)?b.google_ad_height:b.google_orig_ad_height||b.google_ad_height];const c=[];let d=0;for(;a&&d<25;a=a.parentNode,++d)a.nodeType===9?c.push(""):c.push(a.id);(a=c.join())&&b.push(a);return Gh(b.join(":")).toString()};var io=class{constructor(a,b){this.g=a;this.u=b}height(){return this.u}i(a){return a>R(pi)&&this.u>300?this.g:Math.min(1200,Math.round(a))}j(){}};function jo(a){return b=>!!(b.aa()&a)}var Y=class extends io{constructor(a,b,c,d=!1){super(a,b);this.B=c;this.A=d}aa(){return this.B}j(a,b,c){c.style.height=`${this.height()}px`;b.rpe=!0}};const ko={image_stacked:1/1.91,image_sidebyside:1/3.82,mobile_banner_image_sidebyside:1/3.82,pub_control_image_stacked:1/1.91,pub_control_image_sidebyside:1/3.82,pub_control_image_card_stacked:1/1.91,pub_control_image_card_sidebyside:1/3.74,pub_control_text:0,pub_control_text_card:0},lo={image_stacked:80,image_sidebyside:0,mobile_banner_image_sidebyside:0,pub_control_image_stacked:80,pub_control_image_sidebyside:0,pub_control_image_card_stacked:85,pub_control_image_card_sidebyside:0,pub_control_text:80, 
pub_control_text_card:80},mo={pub_control_image_stacked:100,pub_control_image_sidebyside:200,pub_control_image_card_stacked:150,pub_control_image_card_sidebyside:250,pub_control_text:100,pub_control_text_card:150}; 
function no(a){var b=0;a.R&&b++;a.K&&b++;a.L&&b++;if(b<3)return{Z:"Tags data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num should be set together."};b=a.R.split(",");const c=a.L.split(",");a=a.K.split(",");if(b.length!==c.length||b.length!==a.length)return{Z:'Lengths of parameters data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num must match. Example: \n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}; 
if(b.length>2)return{Z:"The parameter length of attribute data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num is too long. At most 2 parameters for each attribute are needed: one for mobile and one for desktop, while "+`you are providing ${b.length} parameters. Example: ${'\n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}.`};const d=[],e=[];for(let g=0;g< 
b.length;g++){var f=Number(c[g]);if(Number.isNaN(f)||f===0)return{Z:`Wrong value '${c[g]}' for ${"data-matched-content-rows-num"}.`};d.push(f);f=Number(a[g]);if(Number.isNaN(f)||f===0)return{Z:`Wrong value '${a[g]}' for ${"data-matched-content-columns-num"}.`};e.push(f)}return{L:d,K:e,Za:b}} 
function oo(a){return a>=1200?{width:1200,height:600}:a>=850?{width:a,height:Math.floor(a*.5)}:a>=550?{width:a,height:Math.floor(a*.6)}:a>=468?{width:a,height:Math.floor(a*.7)}:{width:a,height:Math.floor(a*3.44)}}function po(a,b,c,d){b=Math.floor(((a-8*b-8)/b*ko[d]+lo[d])*c+8*c+8);return a>1500?{width:0,height:0,Lb:`Calculated slot width is too large: ${a}`}:b>1500?{width:0,height:0,Lb:`Calculated slot height is too large: ${b}`}:{width:a,height:b}} 
function qo(a,b){const c=a-8-8;--b;return{width:a,height:Math.floor(c/1.91+70)+Math.floor((c*ko.mobile_banner_image_sidebyside+lo.mobile_banner_image_sidebyside)*b+8*b+8)}};const ro=Pa("script");var so=class{constructor(a,b,c=null,d=null,e=null,f=null,g=null,h=null,k=null,n=null,l=null,m=null){this.D=a;this.X=b;this.aa=c;this.g=d;this.F=e;this.G=f;this.P=g;this.u=h;this.A=k;this.i=n;this.j=l;this.B=m}size(){return this.X}};const to=["google_content_recommendation_ui_type","google_content_recommendation_columns_num","google_content_recommendation_rows_num"];var uo=class extends io{i(a){return Math.min(1200,Math.max(this.g,Math.round(a)))}}; 
function vo(a,b){wo(a,b);if(b.google_content_recommendation_ui_type==="pedestal")return new so(9,new uo(a,Math.floor(a*2.189)));if(Q(bi)){var c=td();var d=R(ci);var e=R(ai),f=R($h);a<468?c?(a=qo(a,d),d={Y:a.width,W:a.height,K:1,L:d,R:"mobile_banner_image_sidebyside"}):(a=po(a,1,d,"image_sidebyside"),d={Y:a.width,W:a.height,K:1,L:d,R:"image_sidebyside"}):(d=oo(a),e===1&&(d.height=Math.floor(d.height*.5)),d={Y:d.width,W:d.height,K:f,L:e,R:"image_stacked"})}else d=td(),a<468?d?(d=qo(a,12),d={Y:d.width, 
W:d.height,K:1,L:12,R:"mobile_banner_image_sidebyside"}):(d=oo(a),d={Y:d.width,W:d.height,K:1,L:13,R:"image_sidebyside"}):(d=oo(a),d={Y:d.width,W:d.height,K:4,L:2,R:"image_stacked"});xo(b,d);return new so(9,new uo(d.Y,d.W))} 
function yo(a,b){wo(a,b);{const f=no({L:b.google_content_recommendation_rows_num,K:b.google_content_recommendation_columns_num,R:b.google_content_recommendation_ui_type});if(f.Z)a={Y:0,W:0,K:0,L:0,R:"image_stacked",Z:f.Z};else{var c=f.Za.length===2&&a>=468?1:0;var d=f.Za[c];d=d.indexOf("pub_control_")===0?d:"pub_control_"+d;var e=mo[d];let g=f.K[c];for(;a/g<e&&g>1;)g--;e=g;c=f.L[c];a=po(a,e,c,d);a={Y:a.width,W:a.height,K:e,L:c,R:d}}}if(a.Z)throw new T(a.Z);xo(b,a);return new so(9,new uo(a.Y,a.W))} 
function wo(a,b){if(a<=0)throw new T(`Invalid responsive width from Matched Content slot ${b.google_ad_slot}: ${a}. Please ensure to put this Matched Content slot into a non-zero width div container.`);}function xo(a,b){a.google_content_recommendation_ui_type=b.R;a.google_content_recommendation_columns_num=b.K;a.google_content_recommendation_rows_num=b.L};var zo=class extends io{i(){return this.g}j(a,b,c){ij(a,c);c.style.height=`${this.height()}px`;b.rpe=!0}};const Ao={"image-top":a=>a<=600?284+(a-250)*.414:429,"image-middle":a=>a<=500?196-(a-250)*.13:164+(a-500)*.2,"image-side":a=>a<=500?205-(a-250)*.28:134+(a-500)*.21,"text-only":a=>a<=500?187-.228*(a-250):130,"in-article":a=>a<=420?a/1.2:a<=460?a/1.91+130:a<=800?a/4:200};var Bo=class extends io{i(){return Math.min(1200,this.g)}}; 
function Co(a,b,c,d,e){var f=e.google_ad_layout||"image-top";if(f==="in-article"){var g=a;if(e.google_full_width_responsive==="false")a=g;else if(a=dj(b,c,g,R(gi),e),a!==!0)e.gfwrnwer=a,a=g;else if(a=S(b))if(e.google_full_width_responsive_allowed=!0,c.parentElement){b:{g=c;for(let h=0;h<100&&g.parentElement;++h){const k=g.parentElement.childNodes;for(let n=0;n<k.length;++n){const l=k[n];if(l!==g&&gj(b,l))break b}g=g.parentElement;g.style.width="100%";g.style.height="auto"}}ij(b,c)}else a=g;else a= 
g}if(a<250)throw new T("Fluid responsive ads must be at least 250px wide: "+`availableWidth=${a}`);a=Math.min(1200,Math.floor(a));if(d&&f!=="in-article"){f=Math.ceil(d);if(f<50)throw new T("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);return new so(11,new io(a,f))}if(f!=="in-article"&&(d=e.google_ad_layout_key)){f=`${d}`;if(d=(c=f.match(/([+-][0-9a-z]+)/g))&&c.length)for(b=[],e=0;e<d;e++)b.push(parseInt(c[e],36)/1E3);else b=null;if(!b)throw new T(`Invalid data-ad-layout-key value: ${f}`); 
f=(a+-725)/1E3;c=0;d=1;e=b.length;for(g=0;g<e;g++)c+=b[g]*d,d*=f;f=Math.ceil(c*1E3- -725+10);if(isNaN(f))throw new T(`Invalid height: height=${f}`);if(f<50)throw new T("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);if(f>1200)throw new T("Fluid responsive ads must be at most 1200px tall: "+`height=${f}`);return new so(11,new io(a,f))}d=Ao[f];if(!d)throw new T("Invalid data-ad-layout value: "+f);c=oj(c,b);b=S(b);b=f!=="in-article"||c||a!==b?Math.ceil(d(a)):Math.ceil(d(a)*1.25);return new so(11, 
f==="in-article"?new Bo(a,b):new io(a,b))};function Do(a){return b=>{for(let c=a.length-1;c>=0;--c)if(!a[c](b))return!1;return!0}}function Eo(a,b){var c=Fo.slice(0);const d=c.length;let e=null;for(let f=0;f<d;++f){const g=c[f];if(a(g)){if(b==null||b(g))return g;e===null&&(e=g)}}return e};var Z=[new Y(970,90,2),new Y(728,90,2),new Y(468,60,2),new Y(336,280,1),new Y(320,100,2),new Y(320,50,2),new Y(300,600,4),new Y(300,250,1),new Y(250,250,1),new Y(234,60,2),new Y(200,200,1),new Y(180,150,1),new Y(160,600,4),new Y(125,125,1),new Y(120,600,4),new Y(120,240,4),new Y(120,120,1,!0)],Fo=[Z[6],Z[12],Z[3],Z[0],Z[7],Z[14],Z[1],Z[8],Z[10],Z[4],Z[15],Z[2],Z[11],Z[5],Z[13],Z[9],Z[16]];function Go(a,b,c,d,e){e.google_full_width_responsive==="false"?c={I:a,G:1}:b==="autorelaxed"&&e.google_full_width_responsive||Ho(b)||e.google_ad_resize?(b=ej(a,c,d,e),c=b!==!0?{I:a,G:b}:{I:S(c)||a,G:!0}):c={I:a,G:2};const {I:f,G:g}=c;return g!==!0?{I:a,G:g}:d.parentElement?{I:f,G:g}:{I:a,G:g}} 
function Io(a,b,c,d,e){const {I:f,G:g}=xk(247,()=>Go(a,b,c,d,e));var h=g===!0;const k=Ud(d.style.width),n=Ud(d.style.height),{X:l,P:m,aa:p,Ya:v}=Jo(f,b,c,d,e,h);h=Ko(b,p);var t;const y=(t=jj(d,c,"marginLeft"))?`${t}px`:"",G=(t=jj(d,c,"marginRight"))?`${t}px`:"";t=lj(d,c)||"";return new so(h,l,p,null,v,g,m,y,G,n,k,t)}function Ho(a){return a==="auto"||/^((^|,) *(horizontal|vertical|rectangle) *)+$/.test(a)} 
function Jo(a,b,c,d,e,f){b=Lo(c,a,b);let g;var h=!1;let k=!1;var n=S(c)<488;if(n){g=Zi(d,c);var l=oj(d,c);h=!l&&g;k=l&&g}l=[mj(a),jo(b)];Q(ni)||l.push(nj(n,c,d,k));e.google_max_responsive_height!=null&&l.push(pj(e.google_max_responsive_height));n=[t=>!t.A];if(h||k)h=qj(c,d),n.push(pj(h));const m=Eo(Do(l),Do(n));if(!m)throw new T(`No slot size for availableWidth=${a}`);const {X:p,P:v}=xk(248,()=>{var t;a:if(f){if(e.gfwrnh&&(t=Ud(e.gfwrnh))){t={X:new zo(a,t),P:!0};break a}if(e.google_resizing_allowed|| 
e.google_full_width_responsive==="true")t=Infinity;else{t=d;let G=Infinity;do{var y=jj(t,c,"height");y&&(G=Math.min(G,y));(y=jj(t,c,"maxHeight"))&&(G=Math.min(G,y))}while(t.parentElement&&(t=t.parentElement)&&t.tagName!=="HTML");t=G}!(Q(ji)&&t<=a*2)&&(t=Math.min(a,t),t<a*.5||t<100)&&(t=a);t={X:new zo(a,Math.floor(t)),P:t<a?102:!0}}else t={X:m,P:100};return t});return e.google_ad_layout==="in-article"?{X:Mo(a,c,d,p,e),P:!1,aa:b,Ya:g}:{X:p,P:v,aa:b,Ya:g}} 
function Ko(a,b){if(a==="auto")return 1;switch(b){case 2:return 2;case 1:return 3;case 4:return 4;case 3:return 5;case 6:return 6;case 5:return 7;case 7:return 8;default:throw Error("bad mask");}}function Lo(a,b,c){if(c==="auto")c=Math.min(1200,S(a)),b=b/c<=.25?4:3;else{b=0;for(const d in Wi)c.indexOf(d)!==-1&&(b|=Wi[d])}return b}function Mo(a,b,c,d,e){const f=e.google_ad_height||jj(c,b,"height");b=Co(a,b,c,f,e).size();return b.g*b.height()>a*d.height()?new Y(b.g,b.height(),1):d};function No(a,b,c,d,e){var f;(f=S(b))?S(b)<488?b.innerHeight>=b.innerWidth?(e.google_full_width_responsive_allowed=!0,ij(b,c),f={I:f,G:!0}):f={I:a,G:5}:f={I:a,G:4}:f={I:a,G:10};const {I:g,G:h}=f;if(h!==!0||a===g)return new so(12,new io(a,d),null,null,!0,h,100);const {X:k,P:n,aa:l}=Jo(g,"auto",b,c,e,!0);return new so(1,k,l,2,!0,h,n)};function Oo(a){const b=a.google_ad_format;if(b==="autorelaxed"){a:{if(a.google_content_recommendation_ui_type!=="pedestal")for(const c of to)if(a[c]!=null){a=!0;break a}a=!1}return a?9:5}if(Ho(b))return 1;if(b==="link")return 4;if(b==="fluid")return a.google_ad_layout==="in-article"?(Po(a),1):8;if(a.google_reactive_ad_format===27)return Po(a),1} 
function Qo(a,b,c,d,e=!1){var f=b.offsetWidth||(c.google_ad_resize||e)&&jj(b,d,"width")||c.google_ad_width||0;a===4&&(c.google_ad_format="auto",a=1);e=(e=Ro(a,f,b,c,d))?e:Io(f,c.google_ad_format,d,b,c);e.size().j(d,c,b);e.aa!=null&&(c.google_responsive_formats=e.aa);e.F!=null&&(c.google_safe_for_responsive_override=e.F);e.G!=null&&(e.G===!0?c.google_full_width_responsive_allowed=!0:(c.google_full_width_responsive_allowed=!1,c.gfwrnwer=e.G));e.P!=null&&e.P!==!0&&(c.gfwrnher=e.P);d=e.j||c.google_ad_width; 
d!=null&&(c.google_resizing_width=d);d=e.i||c.google_ad_height;d!=null&&(c.google_resizing_height=d);d=e.size().i(f);const g=e.size().height();c.google_ad_width=d;c.google_ad_height=g;var h=e.size();f=`${h.i(f)}x${h.height()}`;c.google_ad_format=f;c.google_responsive_auto_format=e.D;e.g!=null&&(c.armr=e.g);c.google_ad_resizable=!0;c.google_override_format=1;c.google_loader_features_used=128;e.G===!0&&(c.gfwrnh=`${e.size().height()}px`);e.u!=null&&(c.gfwroml=e.u);e.A!=null&&(c.gfwromr=e.A);e.i!=null&& 
(c.gfwroh=e.i);e.j!=null&&(c.gfwrow=e.j);e.B!=null&&(c.gfwroz=e.B);f=pd(window)||window;qn(f.location,"google_responsive_dummy_ad")&&(Na([1,2,3,4,5,6,7,8],e.D)||e.g===1)&&e.g!==2&&(f=JSON.stringify({googMsgType:"adpnt",key_value:[{key:"qid",value:"DUMMY_AD"}]}),c.dash=`<${ro}>window.top.postMessage('${f}', '*'); 
          </${ro}> 
          <div id="dummyAd" style="width:${d}px;height:${g}px; 
            background:#ddd;border:3px solid #f00;box-sizing:border-box; 
            color:#000;"> 
            <p>Requested size:${d}x${g}</p> 
            <p>Rendered size:${d}x${g}</p> 
          </div>`);a!==1&&(a=e.size().height(),b.style.height=`${a}px`)}function Ro(a,b,c,d,e){const f=d.google_ad_height||jj(c,e,"height")||0;switch(a){case 5:const {I:g,G:h}=xk(247,()=>Go(b,d.google_ad_format,e,c,d));h===!0&&b!==g&&ij(e,c);h===!0?d.google_full_width_responsive_allowed=!0:(d.google_full_width_responsive_allowed=!1,d.gfwrnwer=h);return vo(g,d);case 9:return yo(b,d);case 8:return Co(b,e,c,f,d);case 10:return No(b,e,c,f,d)}}function Po(a){a.google_ad_format="auto";a.armr=3};function So(a,b){a.google_resizing_allowed=!0;a.ovlp=!0;a.google_ad_format="auto";a.iaaso=!0;a.armr=b};function To(a,b){var c=pd(b);if(c){c=S(c);const d=Pd(a,b)||{},e=d.direction;if(d.width==="0px"&&d.cssFloat!=="none")return-1;if(e==="ltr"&&c)return Math.floor(Math.min(1200,c-a.getBoundingClientRect().left));if(e==="rtl"&&c)return a=b.document.body.getBoundingClientRect().right-a.getBoundingClientRect().right,Math.floor(Math.min(1200,c-a-Math.floor((c-b.document.body.clientWidth)/2)))}return-1};function Uo(a,b){switch(a){case "google_reactive_ad_format":return a=parseInt(b,10),isNaN(a)?0:a;default:return b}} 
function Vo(a,b){if(a.getAttribute("src")){var c=a.getAttribute("src")||"";const d=Te(c,"client");d&&(b.google_ad_client=Uo("google_ad_client",d));(c=Te(c,"host"))&&(b.google_ad_host=Uo("google_ad_host",c))}for(const d of a.attributes)/data-/.test(d.name)&&(a=ua(d.name.replace("data-matched-content","google_content_recommendation").replace("data","google").replace(/-/g,"_")),b.hasOwnProperty(a)||(c=Uo(a,d.value),c!==null&&(b[a]=c)))} 
function Wo(a){return Number(a.google_ad_intents_format)===4?18:17}function Xo(a,b){if(a=fh(a))switch(a.data&&a.data.autoFormat){case "rspv":return 13;case "mcrspv":return 15;default:return 14}else return b.google_ad_intent_query?Wo(b):12} 
function Yo(a,b,c,d){Vo(a,b);if(c.document&&c.document.body&&!Oo(b)&&!b.google_reactive_ad_format&&!b.google_ad_intent_query){var e=parseInt(a.style.width,10),f=To(a,c);if(f>0&&e>f){var g=parseInt(a.style.height,10);e=!!Hn[e+"x"+g];let h=f;if(e){const k=In(f,g);if(k)h=k,b.google_ad_format=k+"x"+g+"_0ads_al";else throw new T("No slot size for availableWidth="+f);}b.google_ad_resize=!0;b.google_ad_width=h;e||(b.google_ad_format=null,b.google_override_format=!0);f=h;a.style.width=`${f}px`;So(b,4)}}if(Q(di)|| 
S(c)<488){f=pd(c)||c;g=a.offsetWidth||jj(a,c,"width")||b.google_ad_width||0;e=b.google_ad_client;if(d=qn(f.location,"google_responsive_slot_preview")||Sm(f,1,d,e))b:if(b.google_reactive_ad_format||b.google_ad_resize||Oo(b)||aj(a,b))d=!1;else{for(d=a;d;d=d.parentElement){f=Pd(d,c);if(!f){b.gfwrnwer=18;d=!1;break b}if(!Na(["static","relative"],f.position)){b.gfwrnwer=17;d=!1;break b}}if(!Q(oi)&&(d=R(hi),d=dj(c,a,g,d,b),d!==!0)){b.gfwrnwer=d;d=!1;break b}d=c===c.top?!0:!1}d?(So(b,1),d=!0):d=!1}else d= 
!1;if(g=Oo(b))Qo(g,a,b,c,d);else{if(aj(a,b)){if(d=Pd(a,c))a.style.width=d.width,a.style.height=d.height,$i(d,b);b.google_ad_width||(b.google_ad_width=a.offsetWidth);b.google_ad_height||(b.google_ad_height=a.offsetHeight);b.google_loader_features_used=256;b.google_responsive_auto_format=Xo(c,b)}else $i(a.style,b);c.location&&c.location.hash==="#gfwmrp"||b.google_responsive_auto_format===12&&b.google_full_width_responsive==="true"?Qo(10,a,b,c,!1):Math.random()<.01&&b.google_responsive_auto_format=== 
12&&(a=ej(a.offsetWidth||parseInt(a.style.width,10)||b.google_ad_width,c,a,b),a!==!0?(b.efwr=!1,b.gfwrnwer=a):b.efwr=!0)}};var Zo=["google_pause_ad_requests","google_user_agent_client_hint"];const $o={kxiJd:{},ZGVTR:void 0}; 
function ap(a){const b=a.ab,c=a.Va,d=a.gb,e=a.l;var f=a.V;const g=a.Ra;c.dataset.adsbygoogleStatus="done";const h=d.google_reactive_ads_config,k=Q(Ni)?b===2:!!h;k||Yo(c,d,e,f);Zn(e,d);const n=Q(Ni)&&k;if(!n&&bp(c,d,e))cp(c,d,e,b===1?a.Aa:$o);else if(n||Pd(c,e)?.display!=="none"||d.google_adtest==="on"||d.google_reactive_ad_format>0||d.google_reactive_ads_config)if(dp(d))q.console&&q.console.warn("Adsbygoogle tag with data-reactive-ad-format="+String(d.google_reactive_ad_format)+" is deprecated. Check out page-level ads at https://www.google.com/adsense"); 
else{if(k){f=h.page_level_pubvars||{};if(X(M).page_contains_reactive_tag&&!X(M).allow_second_reactive_tag){if(f.pltais){Nm(!1);return}throw new T("Only one 'enable_page_level_ads' allowed per page.");}X(M).page_contains_reactive_tag=!0;Nm(f.google_pgb_reactive===7)}d.google_unique_id=hh(e);for(const l of Zo)d[l]=d[l]||e[l];d.google_loader_used!=="sd"&&(d.google_loader_used="aa");d.google_reactive_tag_first=(X(M).first_tag_on_page||0)===1;xk(164,()=>{const {yb:l,Qb:m}=Fn(e,d);c.appendChild(m);go(d); 
typeof d.google_reactive_sra_index==="number"&&d.google_ad_unit_key||(d.google_ad_unit_key=ho(c,d),d.google_ad_dom_fingerprint=Jn(e,c));var p=d.google_start_time??kh;const v=(new Date).getTime();d.google_lrv=eg();d.google_async_iframe_id=l;d.google_start_time=p;d.google_bpp=v>p?v-p:1;n||(p=e.fqjyf||{},e.fqjyf=p,p[l]={LmpfC:d,klgrb:b===1?a.Aa:$o});co(e,()=>{var t=m,y=b===1?{OSwJs:1,QJRox:a.Aa,mqAVR:{crjDQ:g}}:{OSwJs:2,mqAVR:{crjDQ:g}};if(!t||!t.isConnected)if(t=e.document.getElementById(String(d.google_async_iframe_id)+ 
"_host"),t==null)throw Error("no_div");(y=e.google_sa_impl({pubWin:e,vars:d,innerInsElement:t,KCuMo:y}))&&zk(911,y)})})}else e.document.createComment&&c.appendChild(e.document.createComment("No ad requested because of display:none on the adsbygoogle tag"))} 
function bp(a,b,c){var d=Lm(c);!d?.Pa||b.google_adtest==="on"||typeof a.className==="string"&&RegExp("(\\W|^)adsbygoogle-noablate(\\W|$)").test(a.className)?d=!1:d.Qa?(a=bj(a,c),c=Yi(c).clientHeight,d=((c===0?null:a/c)||0)>=d.Qa):d=!0;return d} 
function cp(a,b,c,d){a.className+=" adsbygoogle-ablated-ad-slot";const e=c.fqjyf||{};c.fqjyf=e;const f=String(ma(a));e[f]={LmpfC:b,klgrb:d};a.setAttribute("google_element_uid",f);Lm(c)?.Nb==="slot"&&(Td(a.getAttribute("width"))!==null&&a.setAttribute("width","0"),Td(a.getAttribute("height"))!==null&&a.setAttribute("height","0"),a.style.width="0px",a.style.height="0px")} 
function dp(a){const b=a.google_pgb_reactive==null||a.google_pgb_reactive===3;return(a.google_reactive_ad_format===1||a.google_reactive_ad_format===8)&&b};function ep(a,b,c){return a?.[c]??b?.attributes.getNamedItem(`data-${c.slice(7).replace(/_/g,"-")}`)?.value}function fp(a,b){return{kxiJd:{VbBrq:ep(a,b,"google_ad_public_floor"),hZGxt:ep(a,b,"google_ad_private_floor")},ZGVTR:ep(a,b,"google_pucrd"),zUuKK:ep(a,b,"google_cust_criteria"),XBDmz:ep(a,b,"google_tfs")}};function gp(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&od(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function hp(a,b,c){for(const f of b)a:{b=a;var d=f,e=c;for(let g=0;g<b.g.length;g++){if(b.g[g].element.contains(d)){b.g[g].labels.add(e);break a}if(d.contains(b.g[g].element)){b.g[g].element=d;b.g[g].labels.add(e);break a}}b.g.push({element:d,labels:new Set([e])})}}class ip{constructor(){this.g=[]}getSlots(){return this.g}} 
function jp(a){const b=Ok(a),c=new ip;hp(c,b.lb,1);hp(c,b.mb,2);hp(c,b.ub,3);hp(c,b.Pb,4);hp(c,b.nb,5);hp(c,b.Eb,6);return c.getSlots().map(d=>{var e=new rf;var f=[...d.labels];e=Dc(e,1,f,Pb);d=d.element.getBoundingClientRect();f=new qf;f=Yc(f,1,d.left+a.scrollX);f=Yc(f,2,d.top+a.scrollY);f=Yc(f,3,d.width);d=Yc(f,4,d.height);d=hd(d);e=Oc(e,2,d);return hd(e)}).sort((d,e)=>{d=C(d,qf,2);d=H(d,2);e=C(e,qf,2);e=H(e,2);return d-e})};function tg(a,b,c=0){a.g.size>0||kp(a);c=Math.min(Math.max(0,c),9);const d=a.g.get(c);d?d.push(b):a.g.set(c,[b])}function lp(a,b,c,d){ie(b,c,d);sl(a,()=>je(b,c,d))}function mp(a,b){a.state!==1&&(a.state=1,a.g.size>0&&np(a,b))} 
function kp(a){a.l.document.visibilityState?lp(a,a.l.document,"visibilitychange",b=>{a.l.document.visibilityState==="hidden"&&mp(a,b);a.l.document.visibilityState==="visible"&&(a.state=0)}):"onpagehide"in a.l?(lp(a,a.l,"pagehide",b=>{mp(a,b)}),lp(a,a.l,"pageshow",()=>{a.state=0})):lp(a,a.l,"beforeunload",b=>{mp(a,b)})}function np(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var op=class extends rl{constructor(a){super();this.l=a;this.state=0;this.g=new Map}};async function pp(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function qp(a){const b=a.state.pc;return b!==null&&b!==0?b:a.state.pc=Zd(a.l)}function rp(a){const b=a.state.wpc;return b!==null&&b!==""?b:a.state.wpc=Gn(a.l)}function sp(a,b){var c=new If;var d=qp(a);c=$c(c,1,d);d=rp(a);c=cd(c,2,d);c=$c(c,3,a.state.sd);return $c(c,7,Math.round(b||a.l.performance.now()))}function tp(a,b,c){a.j&&!a.state.le.includes(b)&&(a.state.le.push(b),pg(a.g,c))}async function up(a){await pp(a.l,()=>!(!qp(a)||!rp(a)))} 
async function vp(a){try{return await up(a),!0}catch(b){return!1}}function wp(a){var b=L(xp);if(b.j){var c=b.u;a(c);b.state.psi=x(c)}}function yp(a){tg(a.i,()=>{var b=sp(a);b=E(b,12,Jf,a.A);tp(a,3,b)},9)}function zp(a){tg(a.i,()=>{var b=sp(a);var c=a.B?.()??new Hf;b=E(b,20,Jf,c);tp(a,4,b)},9)} 
function Ap(a){const b=new Ef;tg(a.i,()=>{Oc(b,2,a.u);$c(b,3,a.state.tar);var c=a.l;var d=new vf;var e=jp(c);d=Pc(d,1,e);e=hd(tf(sf(new uf,S(c)),Yi(c).clientHeight));d=Oc(d,2,e);c=hd(tf(sf(new uf,Yi(c).scrollWidth),Yi(c).scrollHeight));c=Oc(d,3,c);c=hd(c);Oc(b,4,c);c=a.g;d=sp(a);d=E(d,8,Jf,b);pg(c,d)},9)} 
async function Bp(a){var b=L(xp);if(b.j&&!b.state.le.includes(1)){b.state.le.push(1);var c=b.l.performance.now();if(Q(li)){if(!await vp(b))return}else await up(b);var d=new Af;a=Ec(d,5,Lb(a),!1);d=tf(sf(new uf,Yi(b.l).scrollWidth),Yi(b.l).scrollHeight);a=Oc(a,2,d);d=tf(sf(new uf,S(b.l)),Yi(b.l).clientHeight);a=Oc(a,1,d);for(var e=d=b.l;d&&d!=d.parent;)d=d.parent,od(d)&&(e=d);a=cd(a,4,e.location.href);d=gp(b.l);d!==0&&(e=new zf,d=ed(e,1,d),Oc(a,3,d));d=b.g;c=sp(b,c);c=E(c,4,Jf,a);pg(d,c);yp(b);Q(ei)&& 
zp(b);Ap(b)}}async function Cp(a,b,c){if(a.j&&c.length&&!a.state.lgdp.includes(Number(b))){a.state.lgdp.push(Number(b));var d=a.l.performance.now();if(Q(li)){if(!await vp(a))return}else await up(a);var e=a.g;a=sp(a,d);d=new yf;b=fd(d,1,b);c=Dc(b,2,c,Rb);c=E(a,9,Jf,c);pg(e,c)}}async function Dp(a,b){if(Q(li)){if(!await vp(a))return}else await up(a);var c=a.g;a=sp(a);a=$c(a,3,1);b=E(a,10,Jf,b);pg(c,b)} 
async function Ep(a,b){if(Q(li)){if(!await vp(a))return}else await up(a);var c=a.g;a=sp(a);a=$c(a,3,1);b=E(a,18,Jf,b);pg(c,b)} 
var xp=class{constructor(a,b){this.l=gh()||window;this.i=b??new op(this.l);this.g=a??new vg(2,eg(),100,100,!0,this.i);this.state=ll(gl(),33,()=>{const c=R(fi);return{sd:c,ssp:c>0&&Qd()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get j(){return this.state.ssp}get u(){return xk(1178,()=>id(Df,fc(this.state.psi||[])))||new Df}get A(){return xk(1227,()=>id(Gf,fc(this.state.cc||[])))||new Gf}};function Fp(){var a=window;return q.google_adtest==="on"||q.google_adbreak_test==="on"||a.location.host.endsWith("h5games.usercontent.goog")||a.location.host==="gamesnacks.com"?a.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(b=>Math.floor(Number(b))).filter(b=>!isNaN(b)&&b>0)||[]:[]};function Gp(a,b){return a instanceof HTMLScriptElement&&b.test(a.src)?0:1}function Hp(a){var b=M.document;if(b.currentScript)return Gp(b.currentScript,a);for(const c of b.scripts)if(Gp(c,a)===0)return 0;return 1};function Ip(a,b){const c=!!C(b,Vm,26)?.g();return{[3]:{[55]:()=>a===0,[23]:d=>Sm(M,Number(d),b),[24]:d=>Wm(Number(d),c),[61]:()=>c,[63]:()=>c||I(b,8)===".google.ch"},[4]:{},[5]:{[6]:()=>I(b,15)}}};function Jp(a=q){return a.ggeac||(a.ggeac={})};function Kp(a,b=document){return!!b.featurePolicy?.features().includes(a)}function Lp(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function Mp(a,b=navigator){try{return!!b.protectedAudience?.queryFeatureSupport?.(a)}catch(c){return!1}};function Np(a,b){try{const d=a.split(".");a=q;let e=0,f;for(;a!=null&&e<d.length;e++)f=a,a=a[d[e]],typeof a==="function"&&(a=f[d[e]]());var c=a;if(typeof c===b)return c}catch{}} 
var Op={[3]:{[8]:a=>{try{return ka(a)!=null}catch{}},[9]:a=>{try{var b=ka(a)}catch{return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},[10]:()=>window===window.top,[6]:(a,b)=>Na(ah(b?Number(b):void 0),Number(a)),[27]:a=>{a=Np(a,"boolean");return a!==void 0?a:void 0},[60]:a=>{try{return!!q.document.querySelector(a)}catch{}},[80]:a=>{try{return!!q.matchMedia(a).matches}catch{}},[69]:a=>Kp(a,q.document),[70]:a=>Lp(a,q.document), 
[79]:a=>Mp(a,q.navigator)},[4]:{[3]:()=>Fd(),[6]:a=>{a=Np(a,"number");return a!==void 0?a:void 0}},[5]:{[2]:()=>window.location.href,[3]:()=>{try{return window.top.location.hash}catch{return""}},[4]:a=>{a=Np(a,"string");return a!==void 0?a:void 0},[12]:a=>{try{const b=Np(a,"string");if(b!==void 0)return atob(b)}catch(b){}}}};var Pp=class extends K{getId(){return H(this,1)}};function Qp(a){return D(a,Pp,2,B())}var Rp=class extends K{};var Sp=class extends K{};var Tp=class extends K{g(){return Rc(this,2)??rc}j(){return Rc(this,4)??rc}u(){return F(this,3)}};var Up=class extends K{};function Vp(a){return Wp({[0]:new Map,[1]:new Map,[2]:new Map},a)} 
function Wp(a,b){const c=new Map;for(const [f,g]of a[1].entries()){var d=f,e=g;const {hb:h,bb:k,eb:n}=e[e.length-1];c.set(d,h+k*n)}for(const f of b)for(const g of D(f,Rp,2,B()))if(Qp(g).length!==0){b=Tb(z(g,8))??0;!J(g,4)||J(g,13)||J(g,14)||(b=c.get(J(g,4))??0,d=(Tb(z(g,1))??0)*Qp(g).length,c.set(J(g,4),b+d));d=[];for(e=0;e<Qp(g).length;e++){const h={hb:b,bb:Tb(z(g,1))??0,eb:Qp(g).length,Db:e,ia:J(f,1),ra:g,U:Qp(g)[e]};d.push(h)}Xp(a[2],J(g,10),d)||Xp(a[1],J(g,4),d)||Xp(a[0],Qp(g)[0].getId(),d)}return a} 
function Xp(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);a.get(b).push(...c);return!0};function Yp(a=Qd()){return b=>Gh(`${b} + ${a}`)%1E3};const Zp=[12,13,20];function $p(a,b){var c=L(Bg).N;const d=ff(C(b.ra,Ze,3),c);if(!d.success)return zg(a.M,C(b.ra,Ze,3),b.ia,b.U.getId(),d),!1;if(!d.value)return!1;c=ff(C(b.U,Ze,3),c);return c.success?c.value?!0:!1:(zg(a.M,C(b.U,Ze,3),b.ia,b.U.getId(),c),!1)}function aq(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];a.includes(b)||a.push(b)} 
function bq(a,b,c,d){const e=[];var f;if(f=b!==9)a.u[b]?f=!0:(a.u[b]=!0,f=!1);if(f)return xg(a.M,b,c,e,[],4),e;f=Zp.includes(b);const g=[],h=[];for(const m of[0,1,2])for(const [p,v]of a.ma[m].entries()){var k=p,n=v;const t=new Of;var l=n.filter(y=>y.ia===b&&a.i[y.U.getId()]&&$p(a,y));if(l.length)for(const y of l)h.push(y.U);else if(!a.xa&&(m===2?(l=d[1],gd(t,2,Pf,k)):l=d[0],k=l?.(String(k))??(m===2&&J(n[0].ra,11)===1?void 0:d[0](String(k))),k!==void 0)){for(const y of n){if(y.ia!==b)continue;n=k- 
y.hb;l=y.bb;const G=y.eb,ia=y.Db;n<0||n>=l*G||n%G!==ia||!$p(a,y)||(n=J(y.ra,13),n!==0&&n!==void 0&&(l=a.j[String(n)],l!==void 0&&l!==y.U.getId()?yg(a.M,a.j[String(n)],y.U.getId(),n):a.j[String(n)]=y.U.getId()),h.push(y.U))}Jc(t,Pf)!==0&&(Zc(t,3,k),g.push(t))}}for(const m of h)d=m.getId(),e.push(d),aq(a,d,f?4:c),Qg(D(m,jf,2,B()),f?Sg():[c],a.M,d);xg(a.M,b,c,e,g,1);return e}function cq(a,b){b=b.map(c=>new Sp(c)).filter(c=>!Zp.includes(J(c,1)));a.ma=Wp(a.ma,b)} 
function dq(a,b){N(1,c=>{a.i[c]=!0},b);N(2,(c,d,e)=>bq(a,c,d,e),b);N(3,c=>(a.g[c]||[]).concat(a.g[4]),b);N(12,c=>void cq(a,c),b);N(16,(c,d)=>void aq(a,c,d),b)}var eq=class{constructor(a,b,c,{xa:d=!1,Cc:e=[]}={}){this.ma=a;this.M=c;this.u={};this.xa=d;this.g={[b]:[],[4]:[]};this.i={};this.j={};if(a=Ae()){a=a.split(",")||[];for(const f of a)(a=Number(f))&&(this.i[a]=!0)}for(const f of e)this.i[f]=!0}};function fq(a,b){a.g=Vg(14,b,()=>{})}class gq{constructor(){this.g=()=>{}}}function hq(a){L(gq).g(a)};function iq({wb:a,N:b,config:c,pb:d=Jp(),qb:e=0,M:f=new Ag(um(C(a,Tp,5)?.g())??0,um(C(a,Tp,5)?.j())??0,C(a,Tp,5)?.u()??!1),ma:g=Vp(D(a,Sp,2,B(ob)))}){d.hasOwnProperty("init-done")?(Vg(12,d,()=>{})(D(a,Sp,2,B()).map(h=>x(h))),Vg(13,d,()=>{})(D(a,jf,1,B()).map(h=>x(h)),e),b&&Vg(14,d,()=>{})(b),jq(e,d)):(dq(new eq(g,e,f,c),d),Wg(d),Xg(d),Yg(d),jq(e,d),Qg(D(a,jf,1,B(ob)),[e],f,void 0,!0),Cg=Cg||!(!c||!c.Bb),hq(Op),b&&hq(b))}function jq(a,b=Jp()){Zg(L($g),b,a);kq(b,a);fq(L(gq),b);L(Xd).B()} 
function kq(a,b){const c=L(Xd);c.j=(d,e)=>Vg(5,a,()=>!1)(d,e,b);c.u=(d,e)=>Vg(6,a,()=>0)(d,e,b);c.i=(d,e)=>Vg(7,a,()=>"")(d,e,b);c.A=(d,e)=>Vg(8,a,()=>[])(d,e,b);c.g=(d,e)=>Vg(17,a,()=>[])(d,e,b);c.B=()=>{Vg(15,a,()=>{})(b)}};function lq(a,b){b={[0]:Yp(Zd(b).toString())};b=L($g).j(a,b);a=Cp(L(xp),a,b);dh.qa(1085,a)}function mq(a,b,c){var d=X(a);if(d.plle)jq(1,Jp(a));else{d.plle=!0;d=C(b,Up,12);var e=F(b,9);iq({wb:d,N:Ip(c,b),config:{xa:e&&!!a.google_disable_experiments,Bb:e},pb:Jp(a),qb:1});if(c=I(b,15))c=Number(c),L($g).i(c);for(const f of xc(b,19,Sb,B()))L($g).g(f);lq(12,a);lq(10,a);a=pd(a)||a;qn(a.location,"google_mc_lab")&&L($g).g(44738307)}};function nq(a){tk.A(b=>{b.shv=String(a);b.mjsv=eg();const c=ah(),d=Fp();b.eid=c.concat(d).join(",")})};function oq(a){return{stavq:H(a,1),jTCuI:I(a,2),OmOVT:F(a,20),xujKL:F(a,9),AyxaY:H(a,18)!==-1?H(a,18):void 0,SLqBY:I(a,8)||void 0,xVQAt:I(a,3),OSCLM:{UWEfJ:!!C(a,Vm,26)?.g(),YguOd:!!C(a,Vm,26)?.u(),SVQEK:!!C(a,Vm,26)?.j()},jzoix:{PygXN:(bn(a)?.ea()?.g()||[]).map(b=>({aJhyn:b.getName(),ihulF:I(b,2)}))},gjPrg:bn(a)?.j()??void 0,zeuLy:bn(a)?.g()?.g()??void 0,ANqoe:I(a,17)??void 0,tnRqy:bn(a)?.D()?.B()||void 0,pnjkY:bn(a)?.da()??!0,FJPve:!1,GLnKw:!1}};var pq=class extends K{g(){return I(this,1)}j(){return J(this,2)}};var qq=class extends K{getName(){return I(this,1)}};var rq=class extends K{g(){return D(this,qq,1,B())}};var sq=class extends K{j(){return I(this,1)}g(){return C(this,pq,2)}F(){return F(this,3)}u(){return F(this,4)}H(){return C(this,Il,5)}T(){return C(this,Jl,6)}ea(){return C(this,rq,7)}da(){return F(this,8)}D(){return C(this,Dj,9)}};var Vm=class extends K{g(){return F(this,1)}u(){return F(this,2)}j(){return F(this,3)}};function bn(a){return Vc(a,sq,27,tq)}var uq=class extends K{},tq=[27,28];function vq(a){var b=tk;try{if(!sb(a))throw Error(String(a));if(a.length>0)return new uq(JSON.parse(a))}catch(c){b.J(838,c instanceof Error?c:Error(String(c)))}return new uq};function wq(a,b,c){if(c==="sd")return 0;if(F(b,22))return 7;if(F(b,16))return 6;c=bn(b)?.g()?.g();b=bn(b)?.g()?.j()??0;a=c===a;switch(b){case 1:return a?9:8;case 2:return a?11:10;case 3:return a?13:12}return 1};function xq(a,b){var c=new yq;try{const f=a.createElement("link");if(f.relList?.supports?.("compression-dictionary")&&Fa()){var d=f;if(b instanceof yd)d.href=Ad(b).toString(),d.rel="compression-dictionary";else{if(Dd.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var e=Bd.test(b)?b:void 0;e!==void 0&&(d.href=e,d.rel="compression-dictionary")}a.head.appendChild(f)}}catch(f){c.na({methodName:1296,ua:f})}} 
function zq(a){return Kd`https://googleads.g.doubleclick.net/pagead/managed/dict/${a}/adsbygoogle`};var yq=class{constructor(){this.g=tk}na(a){const b=a.ua;this.g.J(a.methodName??0,b instanceof Error?b:Error(String(b)))}};function Aq(a,b){ie(window,"message",c=>{let d;try{d=JSON.parse(c.data)}catch(e){return}!d||d.googMsgType!==a||b(d,c)})};function Bq(a,b){return b==null?`&${a}=null`:`&${a}=${Math.floor(b)}`}function Cq(a,b){return`&${a}=${b.toFixed(3)}`}function Dq(){const a=new Set,b=Nk();try{if(!b)return a;const c=b.pubads();for(const d of c.getSlots())a.add(d.getSlotId().getDomId())}catch{}return a}function Eq(a){a=a.id;return a!=null&&(Dq().has(a)||a.startsWith("google_ads_iframe_")||a.startsWith("aswift"))} 
function Fq(a,b,c){if(!a.sources)return!1;switch(Gq(a)){case 2:const d=Hq(a);if(d)return c.some(f=>Iq(d,f));break;case 1:const e=Jq(a);if(e)return b.some(f=>Iq(e,f))}return!1}function Gq(a){if(!a.sources)return 0;a=a.sources.filter(b=>b.previousRect&&b.currentRect);if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function Jq(a){return Kq(a,b=>b.currentRect)}function Hq(a){return Kq(a,b=>b.previousRect)} 
function Kq(a,b){return a.sources.reduce((c,d)=>{d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function Iq(a,b){const c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50} 
function Lq(){const a=Array.from(document.getElementsByTagName("iframe")).filter(Eq),b=[...Dq()].map(c=>document.getElementById(c)).filter(c=>c!==null);Mq=window.scrollX;Nq=window.scrollY;return Oq=[...a,...b].map(c=>c.getBoundingClientRect())} 
function Pq(){var a=new Qq;if(Q(Oi)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask","event"];for(const c of b)b={type:c,buffered:!0},c==="event"&&(b.durationThreshold=40),Rq(a).observe(b);Sq(a)}}} 
function Tq(a,b){const c=Mq!==window.scrollX||Nq!==window.scrollY?[]:Oq,d=Lq();for(const e of b.getEntries())switch(b=e.entryType,b){case "layout-shift":Uq(a,e,c,d);break;case "largest-contentful-paint":b=e;a.Ha=Math.floor(b.renderTime||b.loadTime);a.Ga=b.size;break;case "first-input":b=e;a.Da=Number((b.processingStart-b.startTime).toFixed(3));a.Ea=!0;a.g.some(f=>f.entries.some(g=>e.duration===g.duration&&e.startTime===g.startTime))||Vq(a,e);break;case "longtask":b=Math.max(0,e.duration-50);a.B+= 
b;a.H=Math.max(a.H,b);a.sa+=1;break;case "event":Vq(a,e);break;default:Fb(b,void 0)}}function Rq(a){a.M||(a.M=new PerformanceObserver(Tj(640,b=>{Tq(a,b)})));return a.M} 
function Sq(a){const b=Tj(641,()=>{var d=document;(d.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""]??0)===2&&Wq(a)}),c=Tj(641,()=>void Wq(a));document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.Ca=()=>{document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);Rq(a).disconnect()}} 
function Wq(a){if(!a.Ka){a.Ka=!0;Rq(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=Cq("cls",a.D),b+=Cq("mls",a.T),b+=Bq("nls",a.ea),window.LayoutShiftAttribution&&(b+=Cq("cas",a.A),b+=Bq("nas",a.Ja),b+=Cq("was",a.Oa)),b+=Cq("wls",a.ta),b+=Cq("tls",a.Na));window.LargestContentfulPaint&&(b+=Bq("lcp",a.Ha),b+=Bq("lcps",a.Ga));window.PerformanceEventTiming&&a.Ea&&(b+=Bq("fid",a.Da));window.PerformanceLongTaskTiming&&(b+=Bq("cbt",a.B), 
b+=Bq("mbt",a.H),b+=Bq("nlt",a.sa));let d=0;for(var c of document.getElementsByTagName("iframe"))Eq(c)&&d++;b+=Bq("nif",d);b+=Bq("ifi",ih(window));c=ah();b+=`&${"eid"}=${encodeURIComponent(c.join())}`;b+=`&${"top"}=${q===q.top?1:0}`;b+=a.Ma?`&${"qqid"}=${encodeURIComponent(a.Ma)}`:Bq("pvsid",Zd(q));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.M?a.Fa:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=Bq("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include", 
redirect:"follow",method:"get",mode:"no-cors"});a.Ca()}}function Uq(a,b,c,d){if(!b.hadRecentInput){a.D+=Number(b.value);Number(b.value)>a.T&&(a.T=Number(b.value));a.ea+=1;if(c=Fq(b,c,d))a.A+=b.value,a.Ja++;if(b.startTime-a.Ia>5E3||b.startTime-a.La>1E3)a.Ia=b.startTime,a.i=0,a.j=0;a.La=b.startTime;a.i+=b.value;c&&(a.j+=b.value);a.i>a.ta&&(a.ta=a.i,a.Oa=a.j,a.Na=b.startTime+b.duration)}} 
function Vq(a,b){Xq(a,b);const c=a.g[a.g.length-1],d=a.F[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(d.entries.push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.F[b.id]=b,a.g.push(b)),a.g.sort((e,f)=>f.latency-e.latency),a.g.splice(10).forEach(e=>{delete a.F[e.id]})}function Xq(a,b){b.interactionId&&(a.da=Math.min(a.da,b.interactionId),a.u=Math.max(a.u,b.interactionId),a.Fa=a.u?(a.u-a.da)/7+1:0)} 
var Qq=class{constructor(){this.j=this.i=this.ea=this.T=this.D=0;this.La=this.Ia=Number.NEGATIVE_INFINITY;this.g=[];this.F={};this.Fa=0;this.da=Infinity;this.Da=this.Ga=this.Ha=this.Ja=this.Oa=this.A=this.Na=this.ta=this.u=0;this.Ea=!1;this.sa=this.H=this.B=0;this.M=null;this.Ka=!1;this.Ca=()=>{};const a=document.querySelector("[data-google-query-id]");this.Ma=a?a.getAttribute("data-google-query-id"):null}},Mq,Nq,Oq=[];let Yq=null;const Zq=[],$q=new Map;let ar=-1;function br(a){return sj.test(a.className)&&a.dataset.adsbygoogleStatus!=="done"}function cr(a){var b=document.getElementsByTagName("INS");for(let d=0,e=b[d];d<b.length;e=b[++d]){var c=e;if(br(c)&&c.dataset.adsbygoogleStatus!=="reserved"&&(!a||e.id===a))return e}return null} 
function dr(a,b,c){if(a&&"shift"in a){wp(e=>{var f=Cf(e);Tc(f,2)||(e=Cf(e),ad(e,2))});for(var d=20;a.length>0&&d>0;){try{er(a.shift(),b,c)}catch(e){setTimeout(()=>{throw e;})}--d}}}function fr(){const a=Od("INS");a.className="adsbygoogle";a.className+=" adsbygoogle-noablate";Vd(a);return a} 
function gr(a,b){const c={},d=an(a.google_ad_client,b);nd(Xi,(h,k)=>{a.enable_page_level_ads===!1?c[k]=!1:a.hasOwnProperty(k)?c[k]=a[k]:d.includes(h)&&(c[k]=!1)});la(a.enable_page_level_ads)&&(c.page_level_pubvars=a.enable_page_level_ads);const e=fr();le.body.appendChild(e);const f={google_reactive_ads_config:c,google_ad_client:a.google_ad_client};f.google_pause_ad_requests=!!X(M).pause_ad_requests;const g=wq(hr(a)||Gn(M),b,f.google_loader_used);ap({ab:2,Va:e,gb:f,l:window,V:b,Ra:g});wp(h=>{var k= 
Cf(h);Tc(k,6)||(h=Cf(h),ad(h,6))})}function ir(a,b){xn(q).wasPlaTagProcessed=!0;const c=()=>{gr(a,b)},d=q.document;if(d.body||d.readyState==="complete"||d.readyState==="interactive")gr(a,b);else{const e=sd(yk(191,c));ie(d,"DOMContentLoaded",e);q.MutationObserver!=null&&(new q.MutationObserver((f,g)=>{d.body&&(e(),g.disconnect())})).observe(d,{childList:!0,subtree:!0})}} 
function er(a,b,c){const d={};xk(165,()=>{jr(a,d,b,c)},e=>{e.client=e.client||d.google_ad_client||a.google_ad_client;e.slotname=e.slotname||d.google_ad_slot;e.tag_origin=e.tag_origin||d.google_tag_origin})}function kr(a){delete a.google_checked_head;nd(a,(b,c)=>{rj[c]||(delete a[c],q.console.warn(`AdSense head tag doesn't support ${c.replace("google","data").replace(/_/g,"-")} attribute.`))})} 
function lr(a,b){var c=M.document.querySelector('script[src*="/pagead/js/adsbygoogle.js?client="]:not([data-checked-head])')||M.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js?client="]:not([data-checked-head])')||M.document.querySelector('script[src*="/pagead/js/adsbygoogle.js"][data-ad-client]:not([data-checked-head])')||M.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js"][data-ad-client]:not([data-checked-head])');if(c){c.setAttribute("data-checked-head", 
"true");var d=X(window);if(d.head_tag_slot_vars)mr(c);else{wp(g=>{g=Cf(g);Ec(g,7,Lb(!0),!1)});var e={};Vo(c,e);kr(e);e.google_ad_intent_query&&(e.google_responsive_auto_format=Wo(e),e.google_reactive_ad_format=42);var f=fe(e);d.head_tag_slot_vars=f;c={google_ad_client:e.google_ad_client,enable_page_level_ads:e};e.google_ad_intent_query&&(c.enable_ad_intent_display_ads=!0);e.google_overlays==="bottom"&&(c.overlays={bottom:!0});e.google_overlays==="collapsed-bottom"&&(c.overlays={bottom:!0,["collapsed-bottom"]:!0}); 
delete e.google_overlays;M.adsbygoogle||(M.adsbygoogle=[]);d=M.adsbygoogle;d.loaded?d.push(c):d.splice&&d.splice(0,0,c);b=bn(b)?.u();e.google_adbreak_test||b?nr(f,a):Aq("sc-cnf",()=>{nr(f,a)})}}} 
function or(a,b){var c=pr();if(c){var d={};Vo(c,d);kr(d);d.google_ad_intent_query&&(d.google_responsive_auto_format=Wo(d),d.google_reactive_ad_format=42);X(window).head_tag_slot_vars=fe(d);c=d;d={google_ad_client:c.google_ad_client,enable_page_level_ads:c};c.google_ad_intent_query&&(d.enable_ad_intent_display_ads=!0);c.google_overlays==="bottom"&&(d.overlays={bottom:!0});c.google_overlays==="collapsed-bottom"&&(d.overlays={bottom:!0,["collapsed-bottom"]:!0});delete c.google_overlays;M.adsbygoogle|| 
(M.adsbygoogle=[]);const e=M.adsbygoogle;e.loaded?e.push(d):e.splice&&e.splice(0,0,d);qr(c,b,a)}} 
function pr(){var a=M;if(a=a.document.querySelector('script[src*="/pagead/js/adsbygoogle.js?client="]:not([data-checked-head])')||a.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js?client="]:not([data-checked-head])')||a.document.querySelector('script[src*="/pagead/js/adsbygoogle.js"][data-ad-client]:not([data-checked-head])')||a.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js"][data-ad-client]:not([data-checked-head])'))if(a.setAttribute("data-checked-head","true"), 
X(window).head_tag_slot_vars)mr(a);else return wp(b=>{b=Cf(b);Ec(b,7,Lb(!0),!1)}),a}function qr(a,b,c){b=bn(b)?.u();a.google_adbreak_test||b?nr(a,c):Aq("sc-cnf",()=>{nr(a,c)})} 
function mr(a){const b=X(window).head_tag_slot_vars,c=a.getAttribute("src")||"";if((a=Te(c,"client")||a.getAttribute("data-ad-client")||"")&&a!==b.google_ad_client)throw new T(`Warning: Do not add multiple property codes with AdSense tag to avoid seeing unexpected behavior. These codes were found on the page ${a}, ${String(b.google_ad_client)}`);} 
function rr(a){if(typeof a==="object"&&a!=null){if(typeof a.type==="string")return 2;if(typeof a.sound==="string"||typeof a.preloadAdBreaks==="string"||typeof a.h5AdsConfig==="object")return 3}return 0} 
function jr(a,b,c,d){if(a==null)throw new T("push() called with no parameters.");wp(g=>{var h=Cf(g);Tc(h,3)||(g=Cf(g),ad(g,3))});var e=rr(a);if(e!==0)if(d=Om(),d.first_slotcar_request_processing_time||(d.first_slotcar_request_processing_time=Date.now(),d.adsbygoogle_execution_start_time=kh),Yq==null)sr(a),Zq.push(a);else if(e===3){const g=Yq;xk(787,()=>{g.handleAdConfig(a)})}else zk(730,Yq.handleAdBreak(a));else{kh=(new Date).getTime();$n(c,d,hr(a));tr();a:{if(!a.enable_ad_intent_display_ads&&a.enable_page_level_ads!= 
void 0){if(typeof a.google_ad_client==="string"){e=!0;break a}throw new T("'google_ad_client' is missing from the tag config.");}e=!1}if(e)wp(g=>{var h=Cf(g);Tc(h,4)||(g=Cf(g),ad(g,4))}),ur(a,d);else if((e=a.params)&&nd(e,(g,h)=>{b[h]=g}),b.google_ad_output==="js")console.warn("Ads with google_ad_output='js' have been deprecated and no longer work. Contact your AdSense account manager or switch to standard AdSense ads.");else{e=wq(hr(a)||Gn(M),d,b.google_loader_used);c=vr(b,a);Vo(c,b);var f=X(q).head_tag_slot_vars|| 
{};nd(f,(g,h)=>{b.hasOwnProperty(h)||(b[h]=g)});if(c.hasAttribute("data-require-head")&&!X(q).head_tag_slot_vars)throw new T("AdSense head tag is missing. AdSense body tags don't work without the head tag. You can copy the head tag from your account on https://adsense.com.");if(!b.google_ad_client)throw new T("Ad client is missing from the slot.");if(f=(X(M).first_tag_on_page||0)===0&&An(b))wp(g=>{var h=Cf(g);Tc(h,5)||(g=Cf(g),ad(g,5))}),wr(f);(X(M).first_tag_on_page||0)===0&&(X(M).first_tag_on_page= 
2);b.google_pause_ad_requests=!!X(M).pause_ad_requests;ap({ab:1,Va:c,gb:b,l:window,V:d,Aa:a.ofxVI?.recYb?.klgrb??fp(a.params,c),Ra:e})}}}function hr(a){return a.google_ad_client?a.google_ad_client:(a=a.params)&&a.google_ad_client?a.google_ad_client:""}function tr(){if(Q(si)){const a=Lm(M);a&&a.Pa||Mm(M)}}function wr(a){ke(()=>{xn(q).wasPlaTagProcessed||q.adsbygoogle&&q.adsbygoogle.push(a)})} 
function ur(a,b){(X(M).first_tag_on_page||0)===0&&(X(M).first_tag_on_page=1);if(a.tag_partner){var c=a.tag_partner;const d=X(q);d.tag_partners=d.tag_partners||[];d.tag_partners.push(c)}Bn(a,b);ir(a,b)} 
function vr(a,b){if(a.google_ad_format==="rewarded"){if(a.google_ad_slot==null)throw new T("Rewarded format does not have valid ad slot");if(a.google_ad_loaded_callback==null)throw new T("Rewarded format does not have ad loaded callback");a.google_reactive_ad_format=11;a.google_wrap_fullscreen_ad=!0;a.google_video_play_muted=!1;a.google_acr=a.google_ad_loaded_callback;delete a.google_ad_loaded_callback;delete a.google_ad_format}var c=!!a.google_wrap_fullscreen_ad;if(c)b=fr(),b.dataset.adsbygoogleStatus= 
"reserved",le.documentElement.appendChild(b);else if(b=b.element){if(!br(b)&&(b.id?b=cr(b.id):b=null,!b))throw new T("'element' has already been filled.");if(!("innerHTML"in b))throw new T("'element' is not a good DOM element.");}else if(b=cr(),!b)throw new T("All 'ins' elements in the DOM with class=adsbygoogle already have ads in them.");if(c){c=M;try{const e=(c||window).document,f=e.compatMode=="CSS1Compat"?e.documentElement:e.body;var d=(new be(f.clientWidth,f.clientHeight)).round()}catch(e){d= 
new be(-12245933,-12245933)}a.google_ad_height=d.height;a.google_ad_width=d.width;a.fsapi=!0}return b}function xr(a){gl().S[jl(26)]=!!Number(a)} 
function yr(a){Number(a)?X(M).pause_ad_requests=!0:(X(M).pause_ad_requests=!1,a=()=>{if(!X(M).pause_ad_requests){var b={};let c;typeof window.CustomEvent==="function"?c=new CustomEvent("adsbygoogle-pub-unpause-ad-requests-event",b):(c=document.createEvent("CustomEvent"),c.initCustomEvent("adsbygoogle-pub-unpause-ad-requests-event",!!b.bubbles,!!b.cancelable,b.detail));M.dispatchEvent(c)}},q.setTimeout(a,0),q.setTimeout(a,1E3))} 
function zr(a){typeof a==="function"&&Q(ti)&&Aq("aevi",b=>{try{const k=b.revenueMicros,n=b.revenueCurrency;if(typeof k==="number"&&typeof n==="string"){const l=b.revenueMicros,m=b.revenueCurrency;if(typeof m==="string"&&typeof l==="number"){var c=L(xp),d=new Ff;var e=bd(d,1,"CPM");var f=bd(e,2,"PRECISE");var g=bd(f,3,m);var h=uc(g,4,l==null?l:Ub(l));Ep(c,hd(h))}a({valueMicros:Math.floor(k/1E3),currencyCode:n})}}catch{console.log("onPaidEvent function call failed. The function should be in the form: (event: object) => void, please check for errors in the function you passed to iLAR.")}})} 
function Ar(a){typeof a==="function"&&window.setTimeout(a,0)}function nr(a,b){const c={...rn()};b=vn(Ld(b.Mb,new Map(Object.entries(c)))).then(d=>{Yq==null&&(d.init(a),Yq=d,Br(d))});zk(723,b);b.finally(()=>{Zq.length=0;Ak("slotcar",{event:"api_ld",time:Date.now()-kh,time_pr:Date.now()-ar});Dp(L(xp),wf(23))})} 
function Br(a){for(const [c,d]of $q){var b=c;const e=d;e!==-1&&(q.clearTimeout(e),$q.delete(b))}for(b=0;b<Zq.length;b++){if($q.has(b))continue;const c=Zq[b],d=rr(c);xk(723,()=>{d===3?a.handleAdConfig(c):d===2&&zk(730,a.handleAdBreakBeforeReady(c))})}} 
function sr(a){var b=Zq.length;if(rr(a)===2&&a.type==="preroll"&&a.adBreakDone!=null){var c=a.adBreakDone;ar===-1&&(ar=Date.now());var d=q.setTimeout(()=>{try{c({breakType:"preroll",breakName:a.name,breakFormat:"preroll",breakStatus:"timeout"}),$q.set(b,-1),Ak("slotcar",{event:"pr_to",source:"adsbygoogle"}),Dp(L(xp),wf(22))}catch(e){console.error("[Ad Placement API] adBreakDone callback threw an error:",e instanceof Error?e:Error(String(e)))}},R(Pi)*1E3);$q.set(b,d)}};(function(a,b,c,d=()=>{}){tk.H(Ck);xk(166,()=>{const e=new vg(2,a);try{Za(l=>{lk(e,1191,l)})}catch(l){}const f=vq(b);nq(I(f,2));d();re(16,[1,x(f)]);var g=gh(fh(M))||M;const h=c(a,f);var k=M.document.currentScript===null?1:Hp(h.Ob);mq(g,f,k);Q(Ki)&&I(f,29)&&xq(g.document,zq(I(f,29)));wp(l=>{var m=H(l,1)+1;Zc(l,1,m);M.top===M&&(m=H(l,2)+1,Zc(l,2,m));m=Cf(l);Tc(m,1)||(l=Cf(l),ad(l,1))});zk(1086,Bp(k===0));if(!Ea()||va(Ha(),11)>=0){vk(Q(Si));fo();wm(Kc(f,Vm,26));try{Pq()}catch{}eo();Q(mi)?or(h,f):lr(h, 
f);g=window;k=g.adsbygoogle;if(!k||!k.loaded){var n={push:l=>{er(l,h,f)},loaded:!0,pageState:oq(f)};try{Object.defineProperty(n,"requestNonPersonalizedAds",{set:xr}),Object.defineProperty(n,"pauseAdRequests",{set:yr}),Object.defineProperty(n,"onload",{set:Ar}),Object.defineProperty(n,"onPaidEvent",{set:zr})}catch{}if(k)for(const l of["requestNonPersonalizedAds","pauseAdRequests","onPaidEvent"])k[l]!==void 0&&(n[l]=k[l]);dr(k,h,f);g.adsbygoogle=n;k&&(n.onload=k.onload)}}})})(eg(),typeof sttc==="undefined"? 
void 0:sttc,function(a,b){b=H(b,1)>2012?`_fy${H(b,1)}`:"";Kd`data:text/javascript,//show_ads_impl_preview.js`;return{Mb:Kd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${b}.js`,Kb:Kd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl${b}.js`,Jb:Kd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl_with_ama${b}.js`,Ob:/^(?:https?:)?\/\/(?:pagead2\.googlesyndication\.com|securepubads\.g\.doubleclick\.net)\/pagead\/(?:js\/)?(?:show_ads|adsbygoogle(_direct)?)\.js(?:[?#].*)?$/}}); 
}).call(this,"[2021,\"r20250918\",\"r20190131\",null,null,null,null,\".google.com.eg\",null,null,null,[[[787179885,null,null,[1]],[698926295,null,null,[1]],[null,619278254,null,[null,10]],[null,45696523,null,[]],[676894296,null,null,[1]],[45693370,null,null,null,[[[6,null,null,3,null,2],[1]]]],[682658313,null,null,[1]],[null,1130,null,[null,100]],[null,1340,null,[null,0.2]],[null,1338,null,[null,0.3]],[null,1339,null,[null,0.3]],[null,1032,null,[null,200],[[[12,null,null,null,4,null,\"Android\",[\"navigator.userAgent\"]],[null,500]]]],[797780086,null,null,[1]],[null,728201648,null,[null,100]],[null,766318165,null,[null,-1]],[null,1224,null,[null,0.01]],[788424223,null,null,[1]],[null,1346,null,[null,6]],[null,1347,null,[null,3]],[null,1343,null,[null,300]],[null,1263,null,[null,-1]],[null,1323,null,[null,-1]],[null,1265,null,[null,-1]],[null,1264,null,[null,-1]],[1267,null,null,[1]],[null,66,null,[null,-1]],[null,65,null,[null,-1]],[1241,null,null,[1]],[1300,null,null,[1]],[null,null,null,[null,null,null,[\"en\",\"de\",\"fr\",\"es\",\"ja\"]],null,1273],[null,null,null,[null,null,null,[\"44786015\",\"44786016\"]],null,1261],[752401956,null,null,[1]],[null,770241922,null,[null,1000]],[789511913,null,null,[1]],[45709472,null,null,[]],[622128248,null,null,[]],[767123927,null,null,[1]],[797777487,null,null,[1]],[null,null,45718425,[null,null,\"max(100px, calc(\\u003cDH\\u003e - 400px))\"]],[null,null,45718424,[null,null,\"max(100px, calc(\\u003cDH\\u003e - 320px))\"]],[45713923,null,null,[1]],[45723644,null,null,[1]],[null,null,null,[null,null,null,[\"29_18\",\"30_19\"]],null,null,null,635821288],[null,null,780033902,[null,null,\"calc(\\u003cDH\\u003e - 74px)\"]],[null,null,716722045,[null,null,\"calc(\\u003cDH\\u003e - 30px)\"]],[null,799568408,null,[null,10]],[null,666400580,null,[null,22]],[null,null,null,[null,null,null,[\"\",\"ar\",\"bn\",\"en\",\"es\",\"fr\",\"hi\",\"id\",\"ja\",\"ko\",\"mr\",\"pt\",\"ru\",\"sr\",\"te\",\"th\",\"tr\",\"uk\",\"vi\",\"zh\"]],null,712458671],[null,751018117,null,[null,639]],[null,null,null,[],null,null,null,683929765],[742688665,null,null,[1]],[null,null,45701677,[null,null,\"784267869\"]],[null,45718465,null,[null,1500]],[null,775999093,null,[null,1]],[789220524,null,null,[1]],[null,9604,null,[null,0.7]],[null,717888910,null,[null,0.5423]],[null,9601,null,[null,0.1423]],[null,9602,null,[null,1]],[null,643258049,null,[null,0.16]],[null,643258048,null,[null,0.1542]],[null,618163195,null,[null,8000]],[null,624950166,null,[null,3000]],[null,623405755,null,[null,300]],[null,508040914,null,[null,622]],[null,547455356,null,[null,49]],[null,9605,null,[null,0.5799]],[null,717888911,null,[null,0.7]],[null,9606,null,[null,0.65]],[null,717888912,null,[null,0.5849]],[null,727864505,null,[null,3]],[null,652486359,null,[null,16]],[null,626062006,null,[null,670]],[null,9603,null,[null,4]],[null,748662193,null,[null,4]],[null,688905693,null,[null,2]],[null,650548030,null,[null,3]],[null,650548032,null,[null,300]],[null,650548031,null,[null,1]],[null,655966487,null,[null,300]],[null,655966486,null,[null,250]],[null,687270738,null,[null,317]],[null,469675170,null,[null,68040]],[45723539,null,null,[1]],[45689742,null,null,[1]],[675298507,null,null,[]],[711741274,null,null,[]],[785440426,null,null,[1]],[776685355,null,null,[]],[788556408,null,null,[1]],[797645229,null,null,[1]],[799523811,null,null,[1]],[799516317,null,null,[1]],[797615070,null,null,[1]],[797785545,null,null,[1]],[796588155,null,null,[1]],[570863962,null,null,[1]],[null,null,570879859,[null,null,\"control_1\\\\.\\\\d\"]],[null,570863961,null,[null,50]],[570879858,null,null,[1]],[781146026,null,null,[1]],[null,null,754933823,[null,null,\"1-0-45\"]],[10030,null,null,[1]],[null,1085,null,[null,5]],[null,63,null,[null,30]],[null,1080,null,[null,5]],[null,10019,null,[null,5]],[null,1027,null,[null,10]],[null,57,null,[null,120]],[1134,null,null,[1]],[null,1079,null,[null,5]],[null,1050,null,[null,30]],[781693592,null,null,[]],[751557128,null,null,[1]],[715572365,null,null,[1]],[715572366,null,null,[1]],[null,732217386,null,[null,10000]],[null,794150639,null,[null,5000]],[null,732217387,null,[null,500]],[null,733329086,null,[null,30000]],[null,629808663,null,[null,100]],[null,736623795,null,[null,250]],[null,745376892,null,[null,1]],[null,745376893,null,[null,2]],[null,550718588,null,[null,250]],[null,624290870,null,[null,50]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9nrunKdU5m96PSN1XsSGr3qOP0lvPFUB2AiAylCDlN5DTl17uDFkpQuHj1AFtgWLxpLaiBZuhrtb2WOu7ofHwEAAACKeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A93bovR+QVXNx2\/38qDbmeYYf1wdte9EO37K9eMq3r+541qo0byhYU899BhPB7Cv9QqD7wIbR1B6OAc9kEfYCA4AAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A1S5fojrAunSDrFbD8OfGmFHdRFZymSM\/1ss3G+NEttCLfHkXvlcF6LGLH8Mo5PakLO1sCASXU1\/gQf6XGuTBgwAAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXRhZ3NlcnZpY2VzLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[485990406,null,null,[]]],[[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[662101537,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[662101539,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\",\"4\"]]]]],[10,[[50,[[31067422],[31067423,[[null,1032,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31084127],[31084128]]],[50,[[31093039],[31093040,[[null,766318165,null,[null,150]]]],[31094719,[[null,766318165,null,[null,450]]]]]],[null,[[31093741],[31093742,[[675298507,null,null,[1]]]]]],[12,[[31093846],[31093848,[[null,10029,null,[null,2]]]],[31093849,[[null,10029,null,[null,3]]]],[31093850,[[null,10029,null,[null,4]]]]]],[100,[[31094369],[31094370,[[797429201,null,null,[1]]]]]],[100,[[31094532],[31094533,[[798031579,null,null,[1]]]]]],[2,[[31094566],[31094567,[[732272249,null,null,[1]]]]]],[200,[[31094584],[31094585,[[788558947,null,null,[1]]]]]],[100,[[31094651],[31094652,[[803163504,null,null,[1]]]]]],[10,[[31094693],[31094694,[[782575400,null,null,[1]]]]]],[50,[[31094742],[31094743,[[45724823,null,null,[1]]]]]],[1000,[[31094765,[[null,null,14,[null,null,\"31094765\"]]],[6,null,null,null,6,null,\"31094765\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31094766,[[null,null,14,[null,null,\"31094766\"]]],[6,null,null,null,6,null,\"31094766\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31094795,[[null,null,14,[null,null,\"31094795\"]]],[6,null,null,null,6,null,\"31094795\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31094796,[[null,null,14,[null,null,\"31094796\"]]],[6,null,null,null,6,null,\"31094796\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[10,[[31094805],[31094806,[[805023856,null,null,[1]]]]]],[1,[[42531513],[42531514,[[316,null,null,[1]]]]]],[1,[[42531644],[42531645,[[368,null,null,[1]]]],[42531646,[[369,null,null,[1]],[368,null,null,[1]]]]]],[50,[[42531705],[42531706]]],[1,[[42532242],[42532243,[[1256,null,null,[1]],[290,null,null,[1]]]]]],[50,[[42532523],[42532524,[[1300,null,null,[]]]]]],[null,[[42532525],[42532526]]],[100,[[42533293],[42533294,[[1383,null,null,[1]],[null,54,null,[null,100]],[null,66,null,[null,10]],[null,65,null,[null,1000]]]]],null,145],[1,[[44801778],[44801779,[[506914611,null,null,[1]]]]],[4,null,55],143],[50,[[95344787,[[null,null,null,[null,null,null,[\"95344792\"]],null,null,null,630330362]]],[95344788,[[566279275,null,null,[1]],[622128248,null,null,[1]],[null,null,null,[null,null,null,[\"95344793\"]],null,null,null,630330362]]],[95344789,[[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344794\"]],null,null,null,630330362]]],[95344790,[[566279275,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344795\"]],null,null,null,630330362]]],[95344791,[[566279275,null,null,[1]],[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344796\"]],null,null,null,630330362]]]],[4,null,55],143],[1,[[95345037],[95345038,[[1377,null,null,[1]]]]],[4,null,55]],[10,[[95352051,[[null,null,null,[null,null,null,[\"95352054\"]],null,null,null,630330362]]],[95352052,[[null,9601,null,[]],[null,null,null,[null,null,null,[\"95352055\"]],null,null,null,630330362]]]],[4,null,55],143],[10,[[95366174],[95366175,[[1389,null,null,[1]],[null,1388,null,[null,0.3]]]],[95366176,[[1390,null,null,[1]],[null,1388,null,[null,0.3]]]],[95366177,[[1389,null,null,[1]],[null,1388,null,[null,0.27]]]],[95366178,[[1390,null,null,[1]],[null,1388,null,[null,0.27]]]]]],[null,[[95366179],[95366180],[95366181],[95366182],[95366183]]],[50,[[95366794],[95366795,[[781693592,null,null,[1]]]]]],[50,[[95367554],[95367555,[[45693370,null,null,[]]]]]],[1,[[95369079],[95369080,[[1393,null,null,[1]],[null,1394,null,[null,120]],[null,1396,null,[null,1000]],[null,1395,null,[null,500]]]]]],[null,[[95369081],[95369082]]],[null,[[95370627,[[null,null,null,[null,null,null,[\"95370629\"]],null,null,null,630330362]]],[95370628,[[787137387,null,null,[1]],[null,null,null,[null,null,null,[\"95370630\"]],null,null,null,630330362]]]],[4,null,55]],[500,[[95370775,[[null,null,null,[null,null,null,[\"95370777\"]],null,null,null,630330362]]],[95370776,[[683614711,null,null,[1]],[null,null,null,[null,null,null,[\"95370778\"]],null,null,null,630330362]]]],[4,null,55]],[10,[[95371225],[95371226,[[null,1346,null,[null,4.5]],[null,1347,null,[null,2.25]]]],[95371227,[[null,1346,null,[null,3.75]],[null,1347,null,[null,1.875]]]],[95371228,[[null,1346,null,[null,3]],[null,1347,null,[null,1.5]]]]]],[500,[[95371810,[[null,null,null,[null,null,null,[\"95371812\"]],null,null,null,630330362]]],[95371811,[[759602315,null,null,[1]],[null,null,null,[null,null,null,[\"95371813\"]],null,null,null,630330362]]]],[4,null,55]],[500,[[95371814,[[null,null,null,[null,null,null,[\"95371816\"]],null,null,null,630330362]]],[95371815,[[747408261,null,null,[1]],[null,null,null,[null,null,null,[\"95371817\"]],null,null,null,630330362]]]],[4,null,55]],[50,[[95371966],[95371967,[[45709472,null,null,[1]]]]]],[10,[[95372193,null,[6,null,null,null,6,null,\"\"]]],[4,null,55],63,null,null,null,700,null,null,null,null,2],[10,[[95372194,[[null,null,14,[null,null,\"95372194\"]]],[6,null,null,null,6,null,\"\"]]],[4,null,55],63,null,null,null,800,null,null,null,null,2],[10,[[95372195,[[null,null,14,[null,null,\"95372195\"]],[772097522,null,null,[1]]],[6,null,null,null,6,null,\"\"]]],[4,null,55],63,null,null,null,900,null,null,null,null,2],[10,[[95372357,[[null,null,null,[null,null,null,[\"95372359\"]],null,null,null,630330362]]],[95372358,[[45724507,null,null,[1]],[null,null,null,[null,null,null,[\"95372360\"]],null,null,null,630330362]]]],[4,null,55]]]],[17,[[10,[[31084487],[31084488]],null,null,null,null,32,null,null,142,1],[10,[[31089209],[31089210]],null,null,null,null,39,null,null,189,1],[10,[[31093043],[31093044,[[752401956,null,null,[1]]]],[31093045,[[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,100,null,200,1],[96,[[95353306]],[2,[[4,null,55],[7,null,null,15,null,20250214],[6,null,null,3,null,2]]],null,null,null,null,null,null,215,1],[896,[[95353307,null,[4,null,71,null,null,null,null,[\"215\",\"14\"]]]],[2,[[4,null,55],[7,null,null,15,null,20250214],[6,null,null,3,null,2]]],null,null,null,null,96,null,215,1],[50,[[95360683],[95360684],[95360685]],[7,null,null,15,null,20250514],null,null,null,42,850,null,189,1],[50,[[95360691],[95360692],[95360693]],[2,[[8,null,null,15,null,20250514],[7,null,null,15,null,20250528]]],null,null,null,42,850,null,189,1],[10,[[95361163],[95361164]],[7,null,null,15,null,20250514],null,null,null,39,null,null,189,1],[100,[[95368093],[95368094,[[45712481,null,null,[1]]]],[95370792,[[45712481,null,null,[1]]]]],[4,null,55],null,null,null,null,500,null,221,1],[20,[[95368426],[95368427],[95368428],[95368429],[95368430],[95368431]],null,null,null,null,44,730,null,189,1],[10,[[95372438],[95372439,[[1392,null,null,[1]]]]],[4,null,55],null,null,null,null,500,null,220,1],[50,[[95372614],[95372615,[[767123927,null,null,[]]]]],[4,null,55],null,null,null,null,null,null,225,1]]]],null,null,[null,1000,1,1000]],null,null,null,1,\"foulabook.com\",543844403,null,null,null,null,null,null,null,[0,0,0],[\"ca-pub-3171756961112635\",null,1,null,[[[[null,0,null,null,null,null,\"DIV#container\\u003eDIV.v-page-heading.v-bg-stylish\"],4,[\"50px\",\"10px\",1],[2],null,null,null,1],[[null,0,null,null,null,null,\"DIV#container\\u003eDIV.container\\u003eDIV.row.center\\u003eDIV\\u003eP\"],1,[\"10px\",\"30px\",1],[2],null,null,null,1],[[null,0,null,null,null,null,\"DIV.header-container\"],4,[\"10px\",\"10px\",1],[2],null,null,null,1],[[null,0,null,null,null,null,\"DIV#container\\u003eDIV.v-bg-stylish.light-style\"],4,[\"30px\",\"10px\",1],[2],null,null,null,1]]],[null,[[null,null,null,null,null,null,[0,2]]]]],null,\"m202509180101\"]");
