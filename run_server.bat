@echo off
chcp 65001 > nul
title سيرفر FoulaBook Scraper

echo.
echo ========================================
echo    🚀 سيرفر FoulaBook Scraper
echo ========================================
echo.

echo 📦 جاري التحقق من المكتبات المطلوبة...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تثبيت المكتبات المطلوبة
    echo يرجى التأكد من تثبيت Python بشكل صحيح
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

echo 📁 إنشاء المجلدات المطلوبة...
if not exist "scraped_articles" mkdir scraped_articles
if not exist "published_articles" mkdir published_articles
if not exist "logs" mkdir logs
echo ✅ تم إنشاء المجلدات

echo.
echo 🌐 بدء تشغيل السيرفر...
echo 📍 الرابط: http://localhost:5000
echo 📊 لوحة التحكم متاحة الآن
echo 🔄 اضغط Ctrl+C للإيقاف
echo.

echo 🌐 فتح المتصفح...
start http://localhost:5000

python server.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل السيرفر
    echo يرجى التحقق من ملف server.py
    pause
    exit /b 1
)

pause
