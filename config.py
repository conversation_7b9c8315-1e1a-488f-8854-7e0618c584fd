# -*- coding: utf-8 -*-
"""
ملف إعدادات سيرفر FoulaBook Scraper
"""

import os

# إعدادات السيرفر
SERVER_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True,
    'threaded': True
}

# إعدادات المجلدات
DIRECTORIES = {
    'scraped_articles': 'scraped_articles',
    'published_articles': 'published_articles',
    'logs': 'logs',
    'downloads': 'downloads',
    'templates': 'templates'
}

# إعدادات السحب
SCRAPING_CONFIG = {
    'timeout': 30,
    'max_retries': 3,
    'delay_between_requests': 1,
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# إعدادات التليجرام (اختيارية)
TELEGRAM_CONFIG = {
    'bot_token': '',  # ضع رمز البوت هنا
    'channel_id': '',  # ضع معرف القناة هنا
    'enabled': False
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_max_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'allowed_hosts': ['localhost', '127.0.0.1'],
    'max_file_size': 100 * 1024 * 1024,  # 100 MB
    'allowed_extensions': ['.pdf', '.epub', '.mobi', '.txt', '.doc', '.docx']
}

# إعدادات قاعدة البيانات (للمستقبل)
DATABASE_CONFIG = {
    'type': 'sqlite',
    'path': 'foulabook_scraper.db',
    'enabled': False
}

# إعدادات التخزين المؤقت
CACHE_CONFIG = {
    'enabled': True,
    'timeout': 3600,  # ساعة واحدة
    'max_size': 1000
}

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    for dir_name, dir_path in DIRECTORIES.items():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {dir_path}")

def get_config(section):
    """الحصول على إعدادات قسم معين"""
    configs = {
        'server': SERVER_CONFIG,
        'directories': DIRECTORIES,
        'scraping': SCRAPING_CONFIG,
        'telegram': TELEGRAM_CONFIG,
        'logging': LOGGING_CONFIG,
        'security': SECURITY_CONFIG,
        'database': DATABASE_CONFIG,
        'cache': CACHE_CONFIG
    }
    return configs.get(section, {})

def update_config(section, key, value):
    """تحديث إعداد معين"""
    configs = {
        'server': SERVER_CONFIG,
        'directories': DIRECTORIES,
        'scraping': SCRAPING_CONFIG,
        'telegram': TELEGRAM_CONFIG,
        'logging': LOGGING_CONFIG,
        'security': SECURITY_CONFIG,
        'database': DATABASE_CONFIG,
        'cache': CACHE_CONFIG
    }
    
    if section in configs and key in configs[section]:
        configs[section][key] = value
        return True
    return False

if __name__ == "__main__":
    # إنشاء المجلدات عند تشغيل الملف مباشرة
    create_directories()
    print("✅ تم إعداد جميع المجلدات المطلوبة")
