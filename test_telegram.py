#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار سريع للتليجرام
Quick Telegram Test Script
"""

import sys
import os
from telegram_bot import TelegramBot, save_telegram_config

def main():
    print("🤖 اختبار ربط التليجرام")
    print("=" * 50)
    
    # طلب البيانات من المستخدم
    print("\n📝 يرجى إدخال البيانات التالية:")
    
    token = input("🔑 رمز البوت (من BotFather): ").strip()
    if not token:
        print("❌ رمز البوت مطلوب!")
        return
    
    channel_id = input("📢 معرف القناة (مثل @channel أو -1001234567890): ").strip()
    if not channel_id:
        print("❌ معرف القناة مطلوب!")
        return
    
    print("\n🔄 جاري اختبار الاتصال...")
    
    # إنشاء البوت
    bot = TelegramBot(token, channel_id)
    
    # اختبار الاتصال بالبوت
    print("1️⃣ اختبار الاتصال بالبوت...")
    success, message = bot.test_connection()
    
    if not success:
        print(f"❌ فشل الاتصال بالبوت: {message}")
        return
    
    print(f"✅ نجح الاتصال بالبوت: {message}")
    
    # اختبار إرسال رسالة
    print("2️⃣ اختبار إرسال رسالة...")
    test_message = """🧪 <b>رسالة اختبار</b>

هذه رسالة اختبار من نظام سحب الكتب.
إذا وصلتك هذه الرسالة، فإن الربط نجح! ✅

🕐 الوقت: {time}
🤖 البوت: نشط ويعمل بشكل صحيح""".format(
        time=__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    
    success, result = bot.send_message(test_message)
    
    if not success:
        print(f"❌ فشل إرسال الرسالة: {result}")
        return
    
    print("✅ تم إرسال رسالة الاختبار بنجاح!")
    
    # اختبار رفع كتاب تجريبي
    print("3️⃣ اختبار رفع كتاب تجريبي...")
    
    test_book = {
        'title': 'تحميل كتاب اختبار النظام',
        'author': 'مطور النظام',
        'series': 'سلسلة الاختبارات',
        'language': 'العربية',
        'pages': '50 صفحة',
        'edition': 'الطبعة الأولى',
        'publisher': 'مطبعة التجارب',
        'year': '2024',
        'rating': '5/5',
        'format': 'PDF',
        'size': '1.2 MB',
        'category': 'تقنية',
        'description': 'هذا كتاب تجريبي لاختبار نظام رفع الكتب على التليجرام. يحتوي على معلومات وهمية للاختبار فقط.'
    }
    
    success, result = bot.upload_book(test_book)
    
    if not success:
        print(f"❌ فشل رفع الكتاب التجريبي: {result}")
        return
    
    print("✅ تم رفع الكتاب التجريبي بنجاح!")
    
    # حفظ الإعدادات
    print("4️⃣ حفظ الإعدادات...")
    
    if save_telegram_config(token, channel_id):
        print("✅ تم حفظ إعدادات التليجرام بنجاح!")
    else:
        print("⚠️ تعذر حفظ الإعدادات، لكن الاختبار نجح")
    
    print("\n🎉 تم ربط التليجرام بنجاح!")
    print("=" * 50)
    print("📋 ملخص النتائج:")
    print(f"✅ البوت: متصل ويعمل")
    print(f"✅ القناة: {channel_id}")
    print(f"✅ إرسال الرسائل: يعمل")
    print(f"✅ رفع الكتب: يعمل")
    print(f"✅ حفظ الإعدادات: تم")
    print("\n🚀 يمكنك الآن استخدام النظام لرفع الكتب على التليجرام!")

def interactive_setup():
    """إعداد تفاعلي للتليجرام"""
    print("🛠️ إعداد التليجرام التفاعلي")
    print("=" * 50)
    
    print("\n📖 خطوات الإعداد:")
    print("1. إنشاء بوت جديد عبر @BotFather")
    print("2. إنشاء قناة أو مجموعة")
    print("3. إضافة البوت كمدير في القناة")
    print("4. الحصول على معرف القناة")
    
    choice = input("\nهل تريد المتابعة؟ (y/n): ").lower()
    if choice != 'y':
        print("تم الإلغاء.")
        return
    
    print("\n🤖 إنشاء البوت:")
    print("1. ابحث عن @BotFather في التليجرام")
    print("2. أرسل /newbot")
    print("3. اتبع التعليمات لإنشاء البوت")
    print("4. احتفظ برمز البوت (Token)")
    
    input("\nاضغط Enter عندما تنتهي من إنشاء البوت...")
    
    print("\n📢 إعداد القناة:")
    print("1. أنشئ قناة جديدة")
    print("2. أضف البوت كمدير مع صلاحيات النشر")
    print("3. احصل على معرف القناة:")
    print("   - للقنوات العامة: @channel_name")
    print("   - للقنوات الخاصة: -1001234567890")
    
    input("\nاضغط Enter عندما تنتهي من إعداد القناة...")
    
    # تشغيل الاختبار
    main()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--setup":
        interactive_setup()
    else:
        main()
