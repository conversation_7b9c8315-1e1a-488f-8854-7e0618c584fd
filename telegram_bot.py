#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام رفع الملفات على التليجرام
Telegram File Upload System
"""

import os
import json
import logging
import requests
from datetime import datetime
import mimetypes

class TelegramBot:
    def __init__(self, token=None, channel_id=None):
        """
        إنشاء بوت التليجرام
        
        Args:
            token (str): رمز البوت من BotFather
            channel_id (str): معرف القناة أو المجموعة
        """
        self.token = token
        self.channel_id = channel_id
        self.base_url = f"https://api.telegram.org/bot{token}" if token else None
        
        # إعداد السجلات
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def test_connection(self):
        """اختبار الاتصال بالبوت"""
        if not self.token:
            return False, "رمز البوت غير محدد"
        
        try:
            response = requests.get(f"{self.base_url}/getMe", timeout=10)
            data = response.json()
            
            if data.get('ok'):
                bot_info = data.get('result', {})
                return True, f"تم الاتصال بنجاح! البوت: {bot_info.get('first_name', 'غير محدد')}"
            else:
                return False, f"خطأ في الاتصال: {data.get('description', 'خطأ غير معروف')}"
                
        except Exception as e:
            return False, f"خطأ في الاتصال: {str(e)}"
    
    def send_message(self, text, parse_mode='HTML'):
        """إرسال رسالة نصية"""
        if not self.token or not self.channel_id:
            return False, "البيانات غير مكتملة"
        
        try:
            data = {
                'chat_id': self.channel_id,
                'text': text,
                'parse_mode': parse_mode
            }
            
            response = requests.post(f"{self.base_url}/sendMessage", data=data, timeout=30)
            result = response.json()
            
            if result.get('ok'):
                return True, "تم إرسال الرسالة بنجاح"
            else:
                return False, f"خطأ في الإرسال: {result.get('description', 'خطأ غير معروف')}"
                
        except Exception as e:
            return False, f"خطأ في إرسال الرسالة: {str(e)}"
    
    def send_document(self, file_path, caption="", parse_mode='HTML'):
        """رفع ملف على التليجرام"""
        if not self.token or not self.channel_id:
            return False, "البيانات غير مكتملة"
        
        if not os.path.exists(file_path):
            return False, "الملف غير موجود"
        
        try:
            # تحديد نوع الملف
            mime_type, _ = mimetypes.guess_type(file_path)
            
            with open(file_path, 'rb') as file:
                files = {'document': file}
                data = {
                    'chat_id': self.channel_id,
                    'caption': caption,
                    'parse_mode': parse_mode
                }
                
                response = requests.post(
                    f"{self.base_url}/sendDocument", 
                    files=files, 
                    data=data, 
                    timeout=300  # 5 دقائق للملفات الكبيرة
                )
                
                result = response.json()
                
                if result.get('ok'):
                    file_info = result.get('result', {}).get('document', {})
                    return True, {
                        'message': 'تم رفع الملف بنجاح',
                        'file_id': file_info.get('file_id'),
                        'file_size': file_info.get('file_size'),
                        'file_name': file_info.get('file_name')
                    }
                else:
                    return False, f"خطأ في رفع الملف: {result.get('description', 'خطأ غير معروف')}"
                    
        except Exception as e:
            return False, f"خطأ في رفع الملف: {str(e)}"
    
    def upload_book(self, book_data, file_path=None):
        """رفع كتاب مع معلوماته على التليجرام"""
        try:
            # التحقق من وجود الملف أولاً
            if not file_path or not os.path.exists(file_path):
                return False, "ملف الكتاب غير موجود. يجب تحميل الملف أولاً قبل رفعه على التليجرام."

            # التحقق من حجم الملف (حد التليجرام 50 ميجابايت)
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:  # 50 MB
                return False, f"حجم الملف كبير جداً ({file_size / (1024*1024):.1f} MB). الحد الأقصى للتليجرام 50 MB."

            # إنشاء وصف الكتاب
            caption = self._create_book_caption(book_data)

            # رفع الملف مع الوصف
            self.logger.info(f"بدء رفع ملف: {os.path.basename(file_path)} ({file_size / (1024*1024):.1f} MB)")
            success, result = self.send_document(file_path, caption)

            if success:
                # إنشاء رابط التليجرام للملف
                message_id = result.get('message_id')
                if message_id and self.channel_id:
                    # تحديث رابط التحميل في بيانات الكتاب
                    channel_name = self.channel_id.replace('@', '') if self.channel_id.startswith('@') else self.channel_id
                    telegram_link = f"https://t.me/{channel_name}/{message_id}"

                    # حفظ الرابط الأصلي واستبداله برابط التليجرام
                    book_data['telegram_download_link'] = telegram_link
                    book_data['original_download_link'] = book_data.get('downloadLink', '')
                    book_data['downloadLink'] = telegram_link  # استبدال الرابط الأصلي

                    self.logger.info(f"تم تحديث رابط التحميل: {telegram_link}")

                self.logger.info(f"تم رفع كتاب بنجاح: {book_data.get('title', 'غير محدد')}")
                return success, result
            else:
                return success, result

        except Exception as e:
            error_msg = f"خطأ في رفع الكتاب: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _create_book_caption(self, book_data):
        """إنشاء وصف منسق للكتاب"""
        # استخراج البيانات مع التعامل مع التحديثات اليدوية
        title = book_data.get('title', 'عنوان غير محدد')
        author = book_data.get('author', 'مؤلف غير محدد')
        series = book_data.get('series', 'لا توجد')
        language = book_data.get('language', 'العربية')
        pages = book_data.get('pages', 'غير محدد')
        edition = book_data.get('edition', 'غير محدد')
        publisher = book_data.get('publisher', 'غير محدد')
        year = book_data.get('year', 'غير محدد')
        rating = book_data.get('rating', '5/5')
        format_type = book_data.get('format', 'PDF')
        size = book_data.get('size', 'غير محدد')
        category = book_data.get('category', 'غير محدد')
        description = book_data.get('description', '')

        # الحصول على إعدادات الموقع والقناة من ملف الإعدادات
        website_url = self._get_website_url()
        channel_username = self._get_channel_username()

        # تنظيف النص للهاشتاجات
        clean_category = category.replace(' ', '_').replace('-', '_') if category != 'غير محدد' else 'كتب'
        clean_format = format_type.replace('.', '') if format_type != 'غير محدد' else 'PDF'

        caption = f"""📚 <b>{title}</b>

👤 <b>المؤلف:</b> {author}
📖 <b>السلسلة:</b> {series}
🌐 <b>اللغة:</b> {language}
📄 <b>الصفحات:</b> {pages}
📝 <b>الطبعة:</b> {edition}
🏢 <b>دار النشر:</b> {publisher}
📅 <b>سنة النشر:</b> {year}
⭐ <b>التقييم:</b> {rating}
📁 <b>الصيغة:</b> {format_type}
💾 <b>حجم الملف:</b> {size}
🏷️ <b>التصنيف:</b> {category}

📋 <b>الوصف:</b>
{description[:300]}{'...' if len(description) > 300 else ''}

🔗 <b>موقعنا:</b> {website_url}
📱 <b>قناتنا:</b> {channel_username}

#كتب #قراءة #{clean_category} #{clean_format}"""

        return caption

    def _get_website_url(self):
        """الحصول على رابط الموقع من الإعدادات"""
        try:
            config_file = 'server_settings.json'
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get('website_url', 'https://thanwiabook.com')
        except:
            pass
        return 'https://thanwiabook.com'

    def _get_channel_username(self):
        """الحصول على اسم القناة من الإعدادات"""
        try:
            config_file = 'server_settings.json'
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get('channel_username', '@ThanwiaBook')
        except:
            pass
        return '@ThanwiaBook'

def load_telegram_config():
    """تحميل إعدادات التليجرام"""
    config_file = 'server_settings.json'
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                return settings.get('telegram_token'), settings.get('telegram_channel')
        except Exception as e:
            logging.error(f"خطأ في تحميل إعدادات التليجرام: {e}")
    
    return None, None

def save_telegram_config(token, channel_id):
    """حفظ إعدادات التليجرام"""
    config_file = 'server_settings.json'
    
    try:
        # تحميل الإعدادات الحالية
        settings = {}
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        
        # تحديث إعدادات التليجرام
        settings['telegram_token'] = token
        settings['telegram_channel'] = channel_id
        settings['telegram_enabled'] = True
        
        # حفظ الإعدادات
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        
        return True
        
    except Exception as e:
        logging.error(f"خطأ في حفظ إعدادات التليجرام: {e}")
        return False

# مثال على الاستخدام
if __name__ == "__main__":
    # تحميل الإعدادات
    token, channel_id = load_telegram_config()
    
    if token and channel_id:
        bot = TelegramBot(token, channel_id)
        
        # اختبار الاتصال
        success, message = bot.test_connection()
        print(f"اختبار الاتصال: {message}")
        
        if success:
            # مثال على رفع كتاب
            book_data = {
                'title': 'تحميل كتاب مثال للاختبار',
                'author': 'مؤلف تجريبي',
                'language': 'العربية',
                'pages': '100 صفحة',
                'format': 'PDF',
                'size': '2.5 MB',
                'category': 'أدب',
                'description': 'هذا كتاب تجريبي للاختبار'
            }
            
            success, result = bot.upload_book(book_data)
            print(f"رفع الكتاب: {result}")
    else:
        print("يرجى تكوين إعدادات التليجرام أولاً")
