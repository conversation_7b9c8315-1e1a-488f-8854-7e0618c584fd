$(document).ready(function(){

    var bookId = 0;

    // Mark as likess AJAX
      $('.mark-as-favourites').on('click', function() {
        var bookId = $(this).attr('data-src');
        //var favouriteElem = $('.favourite-count');
        //var favouriteElem = document.getElementById("favourites"+bookId).innerText;
       
         $.ajax({
        method: 'POST',
        url: urlFavourite,
        data: {bookId: bookId, _token: token}
        })
        .done(function(result) {
            //event.target.innerText = isLike ? event.target.innerText == 'Like' ? 'You like this post' : 'Like' : event.target.innerText == 'Dislike' ? 'You don\'t like this post' : 'Dislike';
            if(result.status === 1)
                {
                  // change likes image
                  if(result.currentValue === 'like')
                  {
                    /*toastr.options.rtl = true;
                    toastr.success("تم إضافة الكتاب إلى مفضلتك.");*/

                    //document.getElementById("favourites"+bookId).innerHTML = (parseInt(favouriteElem)) + 1;

                    $(".mark-as-favourites[data-src=" + bookId + "]").find('i').attr('data-original-title','أزل من المفضلة');
                    $(".mark-as-favourites[data-src=" + bookId + "]").find('i').removeClass('fa-bookmark-o').addClass('fa-bookmark');
                  } else {
                    /*toastr.options.rtl = true;
                    toastr.info("تم إزالة الكتاب من المفضلة.");*/

                    //document.getElementById("favourites"+bookId).innerHTML = (parseInt(favouriteElem)) - 1;

                    $(".mark-as-favourites[data-src=" + bookId + "]").find('i').attr('data-original-title','أضف إلى المفضلة');
                    $(".mark-as-favourites[data-src=" + bookId + "]").find('i').removeClass('fa-bookmark').addClass('fa-bookmark-o');
                  }
                }
        })
        .fail(function(xhr, status, error)
        {
        //console.log(error);
            toastr.options.rtl = true;
            toastr.error("آسف, أنت بحاجة إلى تسجيل الدخول أولا");
        });
      });
});
