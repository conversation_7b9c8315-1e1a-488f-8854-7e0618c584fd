﻿/* Info
================================================== 
     Author: www.master-themes.com 
     Version: 1.0
     License: GNU General Public License version
 ================================================== 
 Info */

.v-bg-stylish.v-divider {
    border-color: #e4e4e4;
}

.v-bg-stylish.v-bg-stylish-v1 {
    background-color: #FFFFFF;
    background-image: url(../img/base/ff_rp_bkg2.png);
    background-repeat: repeat;
    background-position: center top;
    -moz-background-size: auto;
    background-size: auto;
    border-top: 1px solid rgb(236, 236, 236);
    border-bottom: 1px solid rgb(238, 238, 238);
}

.v-page-heading.v-bg-stylish.v-bg-stylish-v1 {
background-color: #f7f7f7;
    border-color: #e6e6e6;
}

.v-bg-stylish.v-bg-stylish-v1, .v-bg-stylish.v-bg-stylish-v1 h1,
.v-bg-stylish.v-bg-stylish-v1 h2,
.v-bg-stylish.v-bg-stylish-v1 h3,
.v-bg-stylish.v-bg-stylish-v1 h3,
.v-bg-stylish.v-bg-stylish-v1 h4,
.v-bg-stylish.v-bg-stylish-v1 h5,
.v-bg-stylish.v-bg-stylish-v1 h6,
.v-bg-stylish-v1 .carousel-wrap > a {
    color: #333;
}

.v-bg-stylish.v-bg-stylish-v1 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v1 h4.v-center-heading span:after {
    border-color: #333;
}

.v-bg-stylish-v1.v-full-width-text:after {
    border-top-color: #FFFFFF;
    background-image: url(../img/base/page-heading-bg.png);
    background-repeat: repeat;
    background-size: cover;
}

.v-bg-stylish-v1 h4.v-text-heading, .v-bg-stylish-v1 h4.v-heading {
    border-bottom-color: #333;
}

.v-bg-stylish.v-bg-stylish-v2 {
    background-color: #FFFFFF;
}

.v-bg-stylish.v-bg-stylish-v2 {
    background-image: url(../img/base/pw_maze_white.png);
    background-repeat: repeat;
    background-position: center top;
    -moz-background-size: auto;
    background-size: auto;
    box-shadow: inset 0 0 1px rgba(0,0,0,.1);
}

.v-bg-stylish.v-bg-stylish-v2,
.v-bg-stylish.v-bg-stylish-v2 h1,
.v-bg-stylish.v-bg-stylish-v2 h2,
.v-bg-stylish.v-bg-stylish-v2 h3,
.v-bg-stylish.v-bg-stylish-v2 h3,
.v-bg-stylish.v-bg-stylish-v2 h4,
.v-bg-stylish.v-bg-stylish-v2 h5,
.v-bg-stylish.v-bg-stylish-v2 h6,
.v-bg-stylish-v2 .carousel-wrap > a {
    color: #333;
}

.v-bg-stylish.v-bg-stylish-v2 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v2 h4.v-center-heading span:after {
    border-color: #333;
}

.v-bg-stylish-v2.v-full-width-text:after {
    border-top-color: #FFFFFF;
}

.v-bg-stylish-v2 h4.v-text-heading, .v-bg-stylish-v2 h4.v-heading {
    border-bottom-color: #333;
}

.v-bg-stylish.v-bg-stylish-v3 {
    background-image: url(../img/base/tiny_grid.png);
    background-repeat: repeat;
    background-position: center top;
    background-size: auto;
    border-top: 1px solid rgb(236, 236, 236);
    border-bottom: 1px solid rgb(228, 228, 228);
    box-shadow: none;
}

.v-bg-stylish.v-bg-stylish-v3 .flex-control-nav li a {
    color: #7A7A7A;
}

.v-bg-stylish.v-bg-stylish-v3 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v3 h4.v-center-heading span:after {
    border-color: #ffffff;
}

.v-bg-stylish-v3.v-full-width-text:after {
    border-top-color: #FFFFFF;
}

.v-bg-stylish-v3 h4.v-text-heading, .v-bg-stylish-v3 h4.v-heading {
    border-bottom-color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v4 {
    background-color: #f7f7f7;
    border-color: #e4e4e4;
}

.v-bg-stylish.v-bg-stylish-v4,
.v-bg-stylish.v-bg-stylish-v4 h1,
.v-bg-stylish.v-bg-stylish-v4 h2,
.v-bg-stylish.v-bg-stylish-v4 h3,
.v-bg-stylish.v-bg-stylish-v4 h3,
.v-bg-stylish.v-bg-stylish-v4 h4,
.v-bg-stylish.v-bg-stylish-v4 h5,
.v-bg-stylish.v-bg-stylish-v4 h6,
.v-bg-stylish-v4 .carousel-wrap > a {
    color: #333;
}

.v-bg-stylish.v-bg-stylish-v4 h4.v-center-heading span:before, .v-bg-stylish.v-bg-stylish-v4 h4.v-center-heading span:after {
    border-color: #4a4a4a;
}

.v-bg-stylish-v4.v-full-width-text:after {
    background-color: #f6f6f6;
}

.v-bg-stylish-v4 h4.v-text-heading, .v-bg-stylish-v4 h4.v-heading {
    border-bottom-color: #4a4a4a;
}

.v-bg-stylish.v-bg-stylish-v5 {
    background-color: #4a4a4a;
}

.v-bg-stylish.v-bg-stylish-v5,
.v-bg-stylish.v-bg-stylish-v5 h1,
.v-bg-stylish.v-bg-stylish-v5 h2,
.v-bg-stylish.v-bg-stylish-v5 h3,
.v-bg-stylish.v-bg-stylish-v5 h3,
.v-bg-stylish.v-bg-stylish-v5 h4,
.v-bg-stylish.v-bg-stylish-v5 h5,
.v-bg-stylish.v-bg-stylish-v5 h6,
.v-bg-stylish-v5 .carousel-wrap > a {
    color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v5 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v5 h4.v-center-heading span:after {
    border-color: #ffffff;
}

.v-bg-stylish-v5.v-full-width-text:after {
    background-color: #4a4a4a;
    border-color: #4a4a4a;
}

.v-bg-stylish-v5 h4.v-text-heading, .v-bg-stylish-v5 h4.v-heading {
    border-bottom-color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v6 .social-icons a {
    color: #999;
}

.v-bg-stylish.v-bg-stylish-v6 {
    background-image: url(../img/base/ff_header_bkg_dark.png);
    background-repeat: repeat;
    background-position: center top;
    background-size: auto;
    background-color: #FFFFFF;
}

.v-bg-stylish.v-bg-stylish-v6,
.v-bg-stylish.v-bg-stylish-v6 h1,
.v-bg-stylish.v-bg-stylish-v6 h2,
.v-bg-stylish.v-bg-stylish-v6 h3,
.v-bg-stylish.v-bg-stylish-v6 h3,
.v-bg-stylish.v-bg-stylish-v6 h4,
.v-bg-stylish.v-bg-stylish-v6 h5,
.v-bg-stylish.v-bg-stylish-v6 h6,
.v-bg-stylish-v6 .carousel-wrap > a {
    color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v6 h4.v-center-heading span:before, .v-bg-stylish.v-bg-stylish-v6 h4.v-center-heading span:after {
    border-color: #ffffff;
}

.v-bg-stylish-v6.v-full-width-text:after {
    background-image: url(../img/base/ff_header_bkg_dark.png);
    background-repeat: repeat;
    border: 0;
    background-color: transparent;
}

.v-bg-stylish-v6 h4.v-text-heading, .v-bg-stylish-v6 h4.v-heading {
    border-bottom-color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v7 {
    background-color: #ffc154;
}

.v-bg-stylish.v-bg-stylish-v7,
.v-bg-stylish.v-bg-stylish-v7 h1,
.v-bg-stylish.v-bg-stylish-v7 h2,
.v-bg-stylish.v-bg-stylish-v7 h3,
.v-bg-stylish.v-bg-stylish-v7 h3,
.v-bg-stylish.v-bg-stylish-v7 h4,
.v-bg-stylish.v-bg-stylish-v7 h5,
.v-bg-stylish.v-bg-stylish-v7 h6,
.v-bg-stylish-v7 .carousel-wrap > a {
    color: #4a504e;
}

.v-bg-stylish.v-bg-stylish-v7 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v7 h4.v-center-heading span:after {
    border-color: #4a504e;
}

.v-bg-stylish-v7.v-full-width-text:after {
    background-color: #ffc154;
    border-color: #ffc154;
}

.v-bg-stylish-v7 h4.v-text-heading, .v-bg-stylish-v7 h4.v-heading {
    border-bottom-color: #4a504e;
}

.v-bg-stylish.v-bg-stylish-v8 {
    background-color: #000000;
}

.v-bg-stylish-v8.v-full-width-text:after {
    background-color: #000000;
    border-color: #000000;
}

.v-bg-stylish.v-bg-stylish-v8,
.v-bg-stylish.v-bg-stylish-v8 h1,
.v-bg-stylish.v-bg-stylish-v8 h2,
.v-bg-stylish.v-bg-stylish-v8 h3,
.v-bg-stylish.v-bg-stylish-v8 h3,
.v-bg-stylish.v-bg-stylish-v8 h4,
.v-bg-stylish.v-bg-stylish-v8 h5,
.v-bg-stylish.v-bg-stylish-v8 h6,
.v-bg-stylish-v8 .carousel-wrap > a {
    color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v8 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v8 h4.v-center-heading span:after {
    border-color: #ffffff;
}

.v-bg-stylish-v8.v-full-width-text:after {
    border-top-color: #000000;
}

.v-bg-stylish-v8 h4.v-text-heading,
.v-bg-stylish-v8 h4.v-heading {
    border-bottom-color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v9 {
    background-image: url(../img/base/starring.png);
    background-repeat: repeat;
    background-position: center top;
    -moz-background-size: auto;
    background-size: auto;
}

.v-bg-stylish-v9.v-full-width-text:after {
    background-image: url(../img/base/starring.png);
    background-repeat: repeat;
    border: 0;
}

.v-bg-stylish.v-bg-stylish-v9,
.v-bg-stylish.v-bg-stylish-v9 h1,
.v-bg-stylish.v-bg-stylish-v9 h2,
.v-bg-stylish.v-bg-stylish-v9 h3,
.v-bg-stylish.v-bg-stylish-v9 h3,
.v-bg-stylish.v-bg-stylish-v9 h4,
.v-bg-stylish.v-bg-stylish-v9 h5,
.v-bg-stylish.v-bg-stylish-v9 h6,
.v-bg-stylish-v9 .carousel-wrap > a {
    color: #FFFFFF;
}

.v-bg-stylish.v-bg-stylish-v9 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v9 h4.v-center-heading span:after {
    border-color: #cf514b;
}

.v-bg-stylish-v9 h4.v-text-heading,
.v-bg-stylish-v9 h4.v-heading {
    border-bottom-color: #cf514b;
}

 

.v-bg-stylish.v-bg-stylish-v10,
.v-bg-stylish.v-bg-stylish-v10 h1,
.v-bg-stylish.v-bg-stylish-v10 h2,
.v-bg-stylish.v-bg-stylish-v10 h3,
.v-bg-stylish.v-bg-stylish-v10 h3,
.v-bg-stylish.v-bg-stylish-v10 h4,
.v-bg-stylish.v-bg-stylish-v10 h5,
.v-bg-stylish.v-bg-stylish-v10 h6,
.v-bg-stylish-v10 .carousel-wrap > a {
    color: #ffffff;
}

.v-bg-stylish.v-bg-stylish-v10 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v10 h4.v-center-heading span:after {
    border-color: #ffffff;
}

.v-bg-stylish-v10 h4.v-text-heading,
.v-bg-stylish-v10 h4.v-heading {
    border-bottom-color: #ffffff;
}

.v-bg-stylish.light-style,
.v-bg-stylish.light-style h1,
.v-bg-stylish.light-style h2,
.v-bg-stylish.light-style h3,
.v-bg-stylish.light-style h3,
.v-bg-stylish.light-style h4,
.v-bg-stylish.light-style h5,
.v-bg-stylish.light-style h6 {
    color: #fff!important;
}

.v-bg-stylish.dark-style,
.v-bg-stylish.dark-style h1,
.v-bg-stylish.dark-style h2,
.v-bg-stylish.dark-style h3,
.v-bg-stylish.dark-style h3,
.v-bg-stylish.dark-style h4,
.v-bg-stylish.dark-style h5,
.v-bg-stylish.dark-style h6 {
    color: #4a4a4a!important;
}

.v-parallax .v-bg-overlay {
    background-repeat: repeat;
    background-position: center center;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.8;
    background-image: url('../img/base/video-overlay-1.png');
}

.v-parallax .v-bg-overlay.overlay-colored {
    opacity: 0.6;
}

.v-bg-stylish.v-bg-stylish-v11 {
    background-color: #f7f7f7;
    background-image: url(../img/base/v-arrow.png);
    background-position: top center;
    background-repeat: no-repeat;
    border: 0;
    box-shadow: none;
    border-bottom: 1px solid rgb(238, 238, 238);
}

.v-bg-stylish.v-bg-stylish-v11,
.v-bg-stylish.v-bg-stylish-v11 h1,
.v-bg-stylish.v-bg-stylish-v11 h2,
.v-bg-stylish.v-bg-stylish-v11 h3,
.v-bg-stylish.v-bg-stylish-v11 h3,
.v-bg-stylish.v-bg-stylish-v11 h4,
.v-bg-stylish.v-bg-stylish-v11 h5,
.v-bg-stylish.v-bg-stylish-v11 h6,
.v-bg-stylish-v11 .carousel-wrap > a {
    color: #4a4a4a;
}

.v-bg-stylish.v-bg-stylish-v11 h4.v-center-heading span:before,
.v-bg-stylish.v-bg-stylish-v11 h4.v-center-heading span:after {
    border-color: #4a4a4a;
}

.v-bg-stylish-v11.v-full-width-text:after {
    border-top-color: #FFFFFF;
}

.v-bg-stylish-v11 h4.v-text-heading, .v-bg-stylish-v1 h4.v-heading {
    border-bottom-color: #4a4a4a;
}