/*Custom Size*/
@media (min-width: 1200px) {
    .container {
        width: 1100px;
    }
}
.v-heading-v2 span,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus,
.widget .tagcloud a:hover,
.widget ul.wp-tag-cloud li:hover > a,
.widget.v-photo-stream-widget li,
.v-bg-stylish.v-bg-stylish-v10,
.v-bg-stylish-v10.v-full-width-text:after,
.v-parallax .v-bg-overlay.overlay-colored,
.recent-post figure,
header div.search .btn-primary:hover,
header div.search .btn-primary,
header ul.nav-pills > li > a:hover,
.portfolio-grid li,
.feature-box-icon:hover,
.sf-hover .feature-box-icon,
.feature-box[class*="feature-box-secundary-"] .feature-box-icon,
.sf-hover .feature-box-line,
.pricing-column.highlight h3,
span.highlighted,
span.dropcap4,
.progress-bar-primary,
.v-process-steps .feature-box-icon:hover {
    background-color: #3799ef;
}


.v-color,
div.v-color,
.v-link,
a:hover,
.carousel-wrap > a:hover,
#back-to-top:hover,
.v-pagination a:hover,
.sidebar .v-category-widget ul > li a:hover,
.sidebar .v-archive-widget ul > li a:hover,
.sidebar .v-nav-menu-widget ul > li a:hover,
.v-nav-menu-widget ul > li.current-menu-item a,
.sidebar .v-meta-data-widget ul > li a:hover,
.sidebar .v-recent-entry-widget ul > li a:hover,
.list-toggle:after,
.v-right-sidebar-inner > .active > a,
.v-right-sidebar-inner > .active > a:focus,
.v-right-sidebar-inner > .active > a:hover,
footer a:hover,
.copyright a:hover,
.read-more i:before,
.read-more em:before,
.item-link:hover,
.search-item-meta-down a,
.search-item-meta a,
.v-search-items .star-vote li,
.v-blog-item-details a:hover,
.author-link,
.v-blog-item .read-more,
.article-body-wrap .share-links a:hover,
.post-header .post-meta-info a:hover,
.post-content h2.title a:hover,
.recent-post .post-item-details a:hover,
a.current-menu-item,
header nav ul.nav-main ul.dropdown-menu > li > a:hover,
header nav ul.nav-main li a:hover,
header nav.mega-menu ul.nav-main li.mega-menu-item ul.sub-menu a:hover,
header nav ul.nav-main li.dropdown.active > a i.fa-caret-down,
header ul.nav-pills > li.active > a,
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus,
header nav ul.nav-main li.dropdown:hover > a i.fa-caret-down,
.slideout-filter ul li a:hover,
.v-portfolio-item .v-portfolio-item-permalink, .read-more-link,
.share-links > a:hover,
.comments-likes a:hover i,
.comments-likes a:hover span,
.comments-likes a:hover span, .comments-likes a:hover i,
.portfolio-pagination div:hover > i,
.v-icon,
.pricing-column ul li i.fa,
.pricing-column h3,
.v-divider.v-up a,
span.dropcap2,
.v-list li i,
.v-list-v2 li i,
.media-body .reply-link:hover  {
    color: #3799ef;
}

a.current{
    color: #3799ef !important;
}
.v-container {
    padding-top: 70px !important;
    padding-bottom: 70px !important;
}


.v-pagination li a:hover,
ul.bar-styling li:not(.selected) > a:hover,
ul.bar-styling li > .comments-likes:hover,
ul.page-numbers li > a:hover,
ul.page-numbers li > span.current,
a.text-link,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus,
.pagination > li:not(.active) > a:hover,
.pagination > li:not(.active) > span:hover,
.read-more,
.logged-in-as a,
.comment-meta-actions .edit-link,
.comment-meta-actions .comment-reply,
.media-body .reply-link,
.v-bg-stylish-v10.v-full-width-text:after,
.v-blog-item-details a,
header div.search .btn-primary:hover,
header div.search .btn-primary,
.testimonial.testimonial-thumb-side .testimonial-author .featured-thumbnail,
.v-clients-wrap .owl-carousel .owl-item figure:hover,
blockquote.pullquote  {
    border-color: #3799ef;
}

.signup,
header nav ul.nav-main ul.dropdown-menu,
header nav.mega-menu ul.nav-main li.mega-menu-item ul.dropdown-menu,
.v-tagline-box-v1 {
    border-top-color: #3799ef;
}

.v-heading-v2 h2, .v-heading-v2 h3, .v-heading-v2 h4,
.v-link:hover,
.v-search-items a:hover,
.post-info a,
.tags-wrap .tags a,
.v-team-member-box .read-more:hover,
.v-tweets-slide-widget .tweet-text a {
    border-bottom-color:#3799ef;
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus,
.sidebar .v-nav-menu-widget ul > li.active,
.v-control-right .v-smash-text-wrap,
.author-info-wrap,
.testimonial.testimonial-thumb-side .testimonial-author .featured-thumbnail:after,
blockquote.hero,
.v-tagline-box-v2 {
    border-right-color: #3799ef;
}

figure:hover .overlay {
    box-shadow: inset 0 0 0 500px #3799ef;
}


.v-pagination li a:hover,
ul.bar-styling li:not(.selected) > a:hover,
ul.bar-styling li > .comments-likes:hover,
ul.page-numbers li > a:hover,
ul.page-numbers li > span.current,
.pagination > li:not(.active) > a:hover,
.pagination > li:not(.active) > span:hover,
.slideout-filter ul li.selected a,
.btn.v-btn.v-btn-default {
    background: #3799ef;
}



h2.v-bg-box-shadow-text-50 {
    margin-top: 25px;
    margin-bottom: 0px;
    background-color: rgba(0,0,0,0.3);
    box-shadow: -8px 0 0 rgba(0,0,0,0.3), 8px 0 0 rgba(0,0,0,0.3);
    color: #ffffff !important;
    font-weight: 300;
    font-size: 50px;
    line-height: 50px;
    padding: 10px;
}

h2.v-bg-box-shadow-text-40 {
    margin-top: 0px;
    margin-bottom: 25px;
    background-color: rgba(0,0,0,0.3);
    box-shadow: -8px 0 0 rgba(0,0,0,0.3), 8px 0 0 rgba(0,0,0,0.3);
    color: #ffffff !important;
    font-weight: 600;
    font-size: 26px;
    line-height: 35px;
    padding: 10px;
}


.ui-widget-content a {
    color: #333333;
}

.ui-widget-content a:hover {
    color: #1dc6df;
}



.v-custom-heading{
    display: block;
    font-family: "Droid Serif", Georgia, Times, serif;
    font-weight: normal;
    font-style: italic;
    float: none;
    color: #333;
}
.special_amp {
    font-family: "Baskerville", "Palatino Linotype", "Palatino", "Times New Roman", serif;
    font-style: italic;
    font-size: 1.3em;
    line-height: 0.5em;
    font-weight: normal;
    color:#3799ef;
}
/* Custom*/
.pointer {cursor: pointer;}
.star-custom-color {color: #FA604A;}
.star-custom-color-active {color: #F2B827;}
i.custom {font-size: 18px; }/*color: gray;*/
i.active{color: #3799ef;}
i.active-like{color: #E04C4C;}
i.active-like-donate{color: #FC121E;}
i.active-favourite{color: #3799ef;}

h5 a {
    display: block;
    padding-top: 2px;
    padding-bottom: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.toast-message{
    font-size: 13px;
}