
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"google.com.eg"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":12,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":4},{"function":"__ccd_ga_first","priority":11,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":17},{"function":"__set_product_settings","priority":10,"vtp_instanceDestinationId":"G-H0B0F8T7E2","vtp_foreignTldMacroResult":["macro",1],"vtp_isChinaVipRegionMacroResult":["macro",2],"tag_id":16},{"function":"__ccd_ga_regscope","priority":9,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":15},{"function":"__ccd_em_download","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":14},{"function":"__ccd_em_form","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":13},{"function":"__ccd_em_outbound_click","priority":6,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":12},{"function":"__ccd_em_page_view","priority":5,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":11},{"function":"__ccd_em_scroll","priority":4,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":10},{"function":"__ccd_em_site_search","priority":3,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":9},{"function":"__ccd_em_video","priority":2,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":8},{"function":"__ccd_conversion_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":7},{"function":"__gct","vtp_trackingId":"G-H0B0F8T7E2","vtp_sessionDuration":0,"tag_id":1},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-H0B0F8T7E2","tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",12]],[["if",1],["add",0,13,11,10,9,8,7,6,5,4,3,2,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AN"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"AR"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AS"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"BC"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"BD"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"r",[46,"x"],[36,[1,[15,"x"],[21,[2,[2,[15,"x"],"toLowerCase",[7]],"match",[7,[15,"q"]]],[45]]]]],[50,"s",[46,"x"],[52,"y",[2,[17,[15,"x"],"pathname"],"split",[7,"."]]],[52,"z",[39,[18,[17,[15,"y"],"length"],1],[16,[15,"y"],[37,[17,[15,"y"],"length"],1]],""]],[36,[16,[2,[15,"z"],"split",[7,"/"]],0]]],[50,"t",[46,"x"],[36,[39,[12,[2,[17,[15,"x"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"x"],"pathname"],[0,"/",[17,[15,"x"],"pathname"]]]]],[50,"u",[46,"x"],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"i"],true],[43,[15,"y"],[15,"e"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmDownloadActivity"]],[52,"e","speculative"],[52,"f","ae_block_downloads"],[52,"g","file_download"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",[15,"__module_enhancedMeasurement"]],[52,"n",["require","parseUrl"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[2,[15,"m"],"A",[7,"download"]]],[52,"q",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"v",["l",[8,"checkValidation",true]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"x","y"],["y"],[52,"z",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"],"deferrable",true]],[52,"aA",[16,[15,"x"],"gtm.elementUrl"]],[52,"aB",["n",[15,"aA"]]],[22,[28,[15,"aB"]],[46,[36]]],[52,"aC",["s",[15,"aB"]]],[22,[28,["r",[15,"aC"]]],[46,[53,[36]]]],[52,"aD",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_url",["u",[15,"aB"]],"link_text",[16,[15,"x"],"gtm.elementText"],"file_name",["t",[15,"aB"]],"file_extension",[15,"aC"]]],["w",[15,"z"]],["o",[15,"p"],[15,"g"],[15,"aD"],[15,"z"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"y",[46,"aF"],[52,"aG",[30,[16,[15,"aF"],[15,"q"]],[8]]],[43,[15,"aG"],"event_usage",[7,[17,[15,"j"],"H"]]],[43,[15,"aF"],[15,"q"],[15,"aG"]]],[50,"z",[46,"aF","aG"],[52,"aH",[30,[16,[15,"aF"],[15,"q"]],[8]]],[43,[15,"aH"],[15,"p"],true],[43,[15,"aH"],[15,"k"],true],[22,[16,[15,"aG"],"gtm.formCanceled"],[46,[53,[43,[15,"aH"],[15,"r"],true]]]],[43,[15,"aF"],[15,"q"],[15,"aH"]]],[50,"aA",[46,"aF","aG","aH"],[52,"aI",[2,[15,"x"],"filter",[7,[51,"",[7,"aK"],[36,[20,[2,[15,"aK"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aI"],"length"],0],[46,[53,["w",[15,"aI"],[15,"aF"],[15,"aG"],[15,"aH"]]]]],[52,"aJ",[2,[15,"x"],"filter",[7,[51,"",[7,"aK"],[36,[21,[2,[15,"aK"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aJ"],"length"],0],[46,[53,[43,[15,"aH"],"deferrable",true],["w",[15,"aJ"],[15,"aF"],[15,"aG"],[15,"aH"]]]]]],[52,"b",[15,"__module_eventUsageTrackerIds"]],[52,"c",[15,"__module_featureFlags"]],[52,"d",[15,"__module_ccdEmFormActivity"]],[52,"e",["require","internal.getProductSettingsParameter"]],[52,"f",["require","internal.isFeatureEnabled"]],[52,"g",["require","queryPermission"]],[52,"h",["require","templateStorage"]],[52,"i",["require","internal.trackUsage"]],[52,"j",[15,"__module_goldEventUsageId"]],[52,"k","speculative"],[52,"l","ae_block_form"],[52,"m","form_submit"],[52,"n","form_start"],[52,"o","isRegistered"],[52,"p","em_event"],[52,"q","eventMetadata"],[52,"r","form_event_canceled"],[52,"s",[17,[15,"a"],"instanceDestinationId"]],[22,["e",[15,"s"],[15,"l"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"h"],"getItem",[7,[15,"o"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"h"],"setItem",[7,[15,"o"],true]],[52,"t",["require","internal.addFormInteractionListener"]],[52,"u",["require","internal.addFormSubmitListener"]],[52,"v",[15,"__module_enhancedMeasurement"]],[52,"w",["require","internal.sendGtagEvent"]],[52,"x",[2,[15,"v"],"A",[7,"form"]]],[52,"aB",[8]],[52,"aC",[51,"",[7,"aF","aG"],[22,[15,"aG"],[46,["aG"]]],[52,"aH",[16,[15,"aF"],"gtm.elementId"]],[22,[16,[15,"aB"],[15,"aH"]],[46,[36]]],[43,[15,"aB"],[15,"aH"],true],[52,"aI",[8,"form_id",[15,"aH"],"form_name",[16,[15,"aF"],"gtm.interactedFormName"],"form_destination",[16,[15,"aF"],"gtm.elementUrl"],"form_length",[16,[15,"aF"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"aF"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"aF"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"aF"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"aF"],"gtm.interactedFormFieldPosition"]]],[52,"aJ",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["y",[15,"aJ"]],["z",[15,"aJ"],[15,"aF"]],["aA",[15,"n"],[15,"aI"],[15,"aJ"]]]],[52,"aD",["f",[17,[15,"c"],"CV"]]],[52,"aE",[51,"",[7,"aF","aG"],["aC",[15,"aF"],[44]],[52,"aH",[8,"form_id",[16,[15,"aF"],"gtm.elementId"],"form_name",[16,[15,"aF"],"gtm.interactedFormName"],"form_destination",[16,[15,"aF"],"gtm.elementUrl"],"form_length",[16,[15,"aF"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"aD"],[16,[15,"aF"],"gtm.formSubmitElementText"],[16,[15,"aF"],"gtm.formSubmitButtonText"]]]],[43,[15,"aH"],"event_callback",[15,"aG"]],[52,"aI",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["y",[15,"aI"]],["z",[15,"aI"],[15,"aF"]],["aA",[15,"m"],[15,"aH"],[15,"aI"]]]],[22,[15,"aD"],[46,[53,[52,"aF",["require","internal.addDataLayerEventListener"]],[52,"aG",["require","internal.enableAutoEventOnFormSubmit"]],[52,"aH",["require","internal.enableAutoEventOnFormInteraction"]],[52,"aI",["aH"]],[22,[28,[15,"aI"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aF","gtm.formInteract",[15,"aC"],[15,"aI"]],[52,"aJ",["aG",[8,"checkValidation",false,"waitForTags",false]]],[22,[28,[15,"aJ"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aF","gtm.formSubmit",[15,"aE"],[15,"aJ"]]]],[46,[53,[52,"aF",["f",[17,[15,"c"],"ET"]]],[52,"aG",["f",[17,[15,"c"],"ES"]]],[22,[1,[15,"aF"],[28,["g","detect_form_interaction_events"]]],[46,[53,["i","GTAG_EVENT_FEATURE_CHANNEL",[17,[15,"b"],"T"]],[22,[15,"aG"],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]]]]],["t",[15,"aC"]],[22,[1,[15,"aF"],[28,["g","detect_form_submit_events",[8,"waitForTags",false]]]],[46,[53,["i","GTAG_EVENT_FEATURE_CHANNEL",[17,[15,"b"],"U"]],[22,[15,"aG"],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]]]]],["u",[15,"aE"],[8,"waitForCallbacks",false,"checkValidation",false]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"s",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",""],[22,[1,[15,"y"],[17,[15,"y"],"href"]],[46,[53,[41,"aA"],[3,"aA",[2,[17,[15,"y"],"href"],"indexOf",[7,"#"]]],[3,"z",[39,[23,[15,"aA"],0],[17,[15,"y"],"href"],[2,[17,[15,"y"],"href"],"substring",[7,0,[15,"aA"]]]]]]]],[36,[15,"z"]]],[50,"t",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",[17,[15,"y"],"hostname"]],[52,"aA",[2,[15,"z"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"aA"],[16,[15,"aA"],0]],[46,[3,"z",[2,[15,"z"],"substring",[7,[17,[16,[15,"aA"],0],"length"]]]]]],[36,[15,"z"]]],[50,"u",[46,"y"],[22,[28,[15,"y"]],[46,[36,false]]],[52,"z",[2,[17,[15,"y"],"hostname"],"toLowerCase",[7]]],[22,[28,[15,"z"]],[46,[53,[36,false]]]],[41,"aA"],[3,"aA",[2,["t",["p",["o"]]],"toLowerCase",[7]]],[41,"aB"],[3,"aB",[37,[17,[15,"z"],"length"],[17,[15,"aA"],"length"]]],[22,[1,[18,[15,"aB"],0],[29,[2,[15,"aA"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aB"],[3,"aB",[37,[15,"aB"],1]]],[3,"aA",[0,".",[15,"aA"]]]]]],[22,[1,[19,[15,"aB"],0],[12,[2,[15,"z"],"indexOf",[7,[15,"aA"],[15,"aB"]]],[15,"aB"]]],[46,[53,[36,false]]]],[36,true]],[50,"x",[46,"y"],[52,"z",[8]],[43,[15,"z"],[15,"i"],true],[43,[15,"z"],[15,"e"],true],[43,[15,"y"],"eventMetadata",[15,"z"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmOutboundClickActivity"]],[52,"e","speculative"],[52,"f","ae_block_outbound_click"],[52,"g","click"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",[15,"__module_enhancedMeasurement"]],[52,"n",["require","internal.getRemoteConfigParameter"]],[52,"o",["require","getUrl"]],[52,"p",["require","parseUrl"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"r",[2,[15,"m"],"A",[7,"outbound_click"]]],[52,"v",["n",[15,"j"],"cross_domain_conditions"]],[52,"w",["l",[8,"affiliateDomains",[15,"v"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"w"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"y","z"],[52,"aA",["p",[16,[15,"y"],"gtm.elementUrl"]]],[22,[28,["u",[15,"aA"]]],[46,[53,["z"],[36]]]],[52,"aB",[8,"link_id",[16,[15,"y"],"gtm.elementId"],"link_classes",[16,[15,"y"],"gtm.elementClasses"],"link_url",["s",[15,"aA"]],"link_domain",["t",[15,"aA"]],"outbound",true]],[43,[15,"aB"],"event_callback",[15,"z"]],[52,"aC",[8,"eventId",[16,[15,"y"],"gtm.uniqueEventId"],"deferrable",true]],["x",[15,"aC"]],["q",[15,"r"],[15,"g"],[15,"aB"],[15,"aC"]]],[15,"w"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[17,[15,"f"],"R"],true],[43,[15,"t"],[17,[15,"f"],"BZ"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",[15,"__module_enhancedMeasurement"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[2,[15,"m"],"A",[7,"page_view"]]],[52,"p",[8,"interval",1000,"useV2EventName",true]],[52,"q",["l",[15,"p"]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.oldUrl"]],[22,[20,[16,[15,"s"],"gtm.newUrl"],[15,"u"]],[46,[36]]],[52,"v",[16,[15,"s"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"v"],"pushState"],[21,[15,"v"],"popstate"]],[21,[15,"v"],"replaceState"]],[46,[53,[36]]]],[52,"w",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"w"],"page_location",[16,[15,"s"],"gtm.newUrl"]],[43,[15,"w"],"page_referrer",[15,"u"]]]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"]]],[22,[21,[17,[15,"a"],"deferPageView"],false],[46,[53,[43,[15,"x"],"deferrable",true]]]],["r",[15,"x"]],["n",[15,"o"],[15,"h"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[15,"i"],true],[43,[15,"s"],[15,"e"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmScrollActivity"]],[52,"e","speculative"],[52,"f","ae_block_scroll"],[52,"g","scroll"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnScroll"]],[52,"m",[15,"__module_enhancedMeasurement"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[2,[15,"m"],"A",[7,"scroll"]]],[52,"p",["l",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.scrollDepth",[51,"",[7,"r","s"],["s"],[52,"t",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"],"deferrable",true]],[52,"u",[8,"percent_scrolled",[16,[15,"r"],"gtm.scrollThreshold"]]],["q",[15,"t"]],["n",[15,"o"],[15,"g"],[15,"u"],[15,"t"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"A",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"B",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"s",[46,"t"],[52,"u",[8]],[43,[15,"u"],[15,"k"],true],[43,[15,"u"],[15,"e"],true],[43,[15,"t"],"eventMetadata",[15,"u"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmVideoActivity"]],[52,"e","speculative"],[52,"f","ae_block_video"],[52,"g","video_start"],[52,"h","video_progress"],[52,"i","video_complete"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"l"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"l"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["require","internal.addDataLayerEventListener"]],[52,"n",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"o",[15,"__module_enhancedMeasurement"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",[2,[15,"o"],"A",[7,"video"]]],[52,"r",["n",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"r"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"j"],true]],["m","gtm.video",[51,"",[7,"t","u"],["u"],[52,"v",[16,[15,"t"],"gtm.videoStatus"]],[41,"w"],[22,[20,[15,"v"],"start"],[46,[53,[3,"w",[15,"g"]]]],[46,[22,[20,[15,"v"],"progress"],[46,[53,[3,"w",[15,"h"]]]],[46,[22,[20,[15,"v"],"complete"],[46,[53,[3,"w",[15,"i"]]]],[46,[53,[36]]]]]]]],[52,"x",[8,"video_current_time",[16,[15,"t"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"t"],"gtm.videoDuration"],"video_percent",[16,[15,"t"],"gtm.videoPercent"],"video_provider",[16,[15,"t"],"gtm.videoProvider"],"video_title",[16,[15,"t"],"gtm.videoTitle"],"video_url",[16,[15,"t"],"gtm.videoUrl"],"visible",[16,[15,"t"],"gtm.videoVisible"]]],[52,"y",[8,"eventId",[16,[15,"t"],"gtm.uniqueEventId"],"deferrable",true]],["s",[15,"y"]],["p",[15,"q"],[15,"w"],[15,"x"],[15,"y"]]],[15,"r"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[50,"d",[46,"e"],[2,[15,"c"],"A",[7,[15,"e"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_taskPlatformDetection"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"e"],["d",[15,"e"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"DJ"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"W"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"CG"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"BZ"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"X"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CG"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CH"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","auid"],[52,"z","aw_remarketing_only"],[52,"aA","discount"],[52,"aB","aw_feed_country"],[52,"aC","aw_feed_language"],[52,"aD","items"],[52,"aE","aw_merchant_id"],[52,"aF","aw_basket_type"],[52,"aG","client_id"],[52,"aH","conversion_cookie_prefix"],[52,"aI","conversion_id"],[52,"aJ","conversion_linker"],[52,"aK","conversion_api"],[52,"aL","cookie_deprecation"],[52,"aM","cookie_expires"],[52,"aN","cookie_prefix"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","developer_id"],[52,"aX","shipping"],[52,"aY","engagement_time_msec"],[52,"aZ","estimated_delivery_date"],[52,"bA","event_developer_id_string"],[52,"bB","event"],[52,"bC","event_timeout"],[52,"bD","first_party_collection"],[52,"bE","match_id"],[52,"bF","gdpr_applies"],[52,"bG","google_analysis_params"],[52,"bH","_google_ng"],[52,"bI","gpp_sid"],[52,"bJ","gpp_string"],[52,"bK","gsa_experiment_id"],[52,"bL","gtag_event_feature_usage"],[52,"bM","iframe_state"],[52,"bN","ignore_referrer"],[52,"bO","is_passthrough"],[52,"bP","_lps"],[52,"bQ","language"],[52,"bR","merchant_feed_label"],[52,"bS","merchant_feed_language"],[52,"bT","merchant_id"],[52,"bU","new_customer"],[52,"bV","page_hostname"],[52,"bW","page_path"],[52,"bX","page_referrer"],[52,"bY","page_title"],[52,"bZ","_platinum_request_status"],[52,"cA","quantity"],[52,"cB","restricted_data_processing"],[52,"cC","screen_resolution"],[52,"cD","send_page_view"],[52,"cE","server_container_url"],[52,"cF","session_duration"],[52,"cG","session_engaged_time"],[52,"cH","session_id"],[52,"cI","_shared_user_id"],[52,"cJ","delivery_postal_code"],[52,"cK","topmost_url"],[52,"cL","transaction_id"],[52,"cM","transport_url"],[52,"cN","update"],[52,"cO","_user_agent_architecture"],[52,"cP","_user_agent_bitness"],[52,"cQ","_user_agent_full_version_list"],[52,"cR","_user_agent_mobile"],[52,"cS","_user_agent_model"],[52,"cT","_user_agent_platform"],[52,"cU","_user_agent_platform_version"],[52,"cV","_user_agent_wow64"],[52,"cW","user_data"],[52,"cX","user_data_auto_latency"],[52,"cY","user_data_auto_meta"],[52,"cZ","user_data_auto_multi"],[52,"dA","user_data_auto_selectors"],[52,"dB","user_data_auto_status"],[52,"dC","user_data_mode"],[52,"dD","user_id"],[52,"dE","user_properties"],[52,"dF","us_privacy_string"],[52,"dG","value"],[52,"dH","_fpm_parameters"],[52,"dI","_host_name"],[52,"dJ","_in_page_command"],[52,"dK","non_personalized_ads"],[52,"dL","conversion_label"],[52,"dM","page_location"],[52,"dN","global_developer_id_string"],[52,"dO","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BP",[15,"y"],"BS",[15,"z"],"BT",[15,"aA"],"BU",[15,"aB"],"BV",[15,"aC"],"BW",[15,"aD"],"BX",[15,"aE"],"BY",[15,"aF"],"CG",[15,"aG"],"CL",[15,"aH"],"CM",[15,"aI"],"JT",[15,"dL"],"CN",[15,"aJ"],"CP",[15,"aK"],"CQ",[15,"aL"],"CS",[15,"aM"],"CW",[15,"aN"],"CX",[15,"aO"],"CY",[15,"aP"],"CZ",[15,"aQ"],"DA",[15,"aR"],"DB",[15,"aS"],"DC",[15,"aT"],"DD",[15,"aU"],"DH",[15,"aV"],"DI",[15,"aW"],"DU",[15,"aX"],"DW",[15,"aY"],"EA",[15,"aZ"],"EE",[15,"bA"],"EG",[15,"bB"],"EI",[15,"bC"],"EN",[15,"bD"],"EW",[15,"bE"],"FG",[15,"bF"],"JV",[15,"dN"],"FK",[15,"bG"],"FL",[15,"bH"],"FO",[15,"bI"],"FP",[15,"bJ"],"FR",[15,"bK"],"FS",[15,"bL"],"FU",[15,"bM"],"FV",[15,"bN"],"GA",[15,"bO"],"GB",[15,"bP"],"GC",[15,"bQ"],"GJ",[15,"bR"],"GK",[15,"bS"],"GL",[15,"bT"],"GP",[15,"bU"],"GS",[15,"bV"],"JU",[15,"dM"],"GT",[15,"bW"],"GU",[15,"bX"],"GV",[15,"bY"],"HD",[15,"bZ"],"HF",[15,"cA"],"HJ",[15,"cB"],"HN",[15,"cC"],"HQ",[15,"cD"],"HS",[15,"cE"],"HU",[15,"cF"],"HW",[15,"cG"],"HX",[15,"cH"],"HZ",[15,"cI"],"IA",[15,"cJ"],"JW",[15,"dO"],"IF",[15,"cK"],"II",[15,"cL"],"IJ",[15,"cM"],"IL",[15,"cN"],"IO",[15,"cO"],"IP",[15,"cP"],"IQ",[15,"cQ"],"IR",[15,"cR"],"IS",[15,"cS"],"IT",[15,"cT"],"IU",[15,"cU"],"IV",[15,"cV"],"IW",[15,"cW"],"IX",[15,"cX"],"IY",[15,"cY"],"IZ",[15,"cZ"],"JA",[15,"dA"],"JB",[15,"dB"],"JC",[15,"dC"],"JE",[15,"dD"],"JF",[15,"dE"],"JH",[15,"dF"],"JI",[15,"dG"],"JK",[15,"dH"],"JL",[15,"dI"],"JM",[15,"dJ"],"JQ",[15,"dK"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","allow_ad_personalization"],[52,"e","consent_state"],[52,"f","consent_updated"],[52,"g","conversion_linker_enabled"],[52,"h","cookie_options"],[52,"i","em_event"],[52,"j","event_start_timestamp_ms"],[52,"k","event_usage"],[52,"l","ga4_collection_subdomain"],[52,"m","handle_internally"],[52,"n","hit_type"],[52,"o","hit_type_override"],[52,"p","is_conversion"],[52,"q","is_external_event"],[52,"r","is_first_visit"],[52,"s","is_first_visit_conversion"],[52,"t","is_fpm_encryption"],[52,"u","is_fpm_split"],[52,"v","is_gcp_conversion"],[52,"w","is_google_signals_allowed"],[52,"x","is_server_side_destination"],[52,"y","is_session_start"],[52,"z","is_session_start_conversion"],[52,"aA","is_sgtm_ga_ads_conversion_study_control_group"],[52,"aB","is_sgtm_prehit"],[52,"aC","is_split_conversion"],[52,"aD","is_syn"],[52,"aE","prehit_for_retry"],[52,"aF","redact_ads_data"],[52,"aG","redact_click_ids"],[52,"aH","send_ccm_parallel_ping"],[52,"aI","send_user_data_hit"],[52,"aJ","speculative"],[52,"aK","syn_or_mod"],[52,"aL","transient_ecsid"],[52,"aM","transmission_type"],[52,"aN","user_data"],[52,"aO","user_data_from_automatic"],[52,"aP","user_data_from_automatic_getter"],[52,"aQ","user_data_from_code"],[52,"aR","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"D",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"L",[15,"h"],"R",[15,"i"],"W",[15,"j"],"X",[15,"k"],"AF",[15,"l"],"AH",[15,"m"],"AI",[15,"n"],"AJ",[15,"o"],"AN",[15,"p"],"AP",[15,"q"],"AR",[15,"r"],"AS",[15,"s"],"AU",[15,"t"],"AV",[15,"u"],"AW",[15,"v"],"AX",[15,"w"],"BB",[15,"x"],"BC",[15,"y"],"BD",[15,"z"],"BE",[15,"aA"],"BF",[15,"aB"],"BH",[15,"aC"],"BI",[15,"aD"],"BN",[15,"aE"],"BQ",[15,"aF"],"BR",[15,"aG"],"BT",[15,"aH"],"BX",[15,"aI"],"BZ",[15,"aJ"],"CC",[15,"aK"],"CD",[15,"aL"],"CE",[15,"aM"],"CF",[15,"aN"],"CG",[15,"aO"],"CH",[15,"aP"],"CI",[15,"aQ"],"CJ",[15,"aR"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",42],[52,"f",43],[52,"g",44],[52,"h",45],[52,"i",46],[52,"j",47],[52,"k",113],[52,"l",129],[52,"m",142],[52,"n",156],[52,"o",168],[52,"p",174],[52,"q",178],[52,"r",212],[52,"s",240],[52,"t",241],[52,"u",243],[52,"v",252],[52,"w",253],[52,"x",254],[36,[8,"ER",[15,"v"],"DC",[15,"o"],"V",[15,"b"],"W",[15,"c"],"X",[15,"d"],"AC",[15,"e"],"AD",[15,"f"],"AE",[15,"g"],"AF",[15,"h"],"AG",[15,"i"],"AH",[15,"j"],"DG",[15,"p"],"DJ",[15,"q"],"EJ",[15,"s"],"BN",[15,"k"],"EL",[15,"u"],"BZ",[15,"l"],"EK",[15,"t"],"ET",[15,"x"],"ES",[15,"w"],"CM",[15,"m"],"DV",[15,"r"],"CV",[15,"n"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_goldEventUsageId",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",1],[52,"c",2],[52,"d",5],[52,"e",6],[52,"f",7],[52,"g",8],[52,"h",9],[52,"i",11],[52,"j",15],[52,"k",16],[52,"l",20],[52,"m",21],[52,"n",23],[52,"o",24],[52,"p",27],[36,[8,"O",[15,"j"],"W",[15,"n"],"P",[15,"k"],"X",[15,"o"],"K",[15,"i"],"A",[15,"b"],"T",[15,"l"],"E",[15,"d"],"F",[15,"e"],"B",[15,"c"],"H",[15,"g"],"I",[15,"h"],"G",[15,"f"],"U",[15,"m"],"AA",[15,"p"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_enhancedMeasurement",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"c",[46,"d"],[52,"e",["b"]],[36,[15,"e"]]],[52,"b",["require","internal.getDestinationIds"]],[36,[8,"A",[15,"c"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_eventUsageTrackerIds",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",20],[52,"c",21],[36,[8,"T",[15,"b"],"U",[15,"c"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"B",[15,"c"],"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_platformDetection",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46],[68,"l",[53,[22,[28,["e",[17,[15,"b"],"EL"]]],[46,[53,[36]]]],[52,"l",[7]],[22,["g"],[46,[2,[15,"l"],"push",[7,"ac"]]]],[22,["h"],[46,[2,[15,"l"],"push",[7,"sqs"]]]],[22,["i"],[46,[2,[15,"l"],"push",[7,"dud"]]]],[22,["j"],[46,[2,[15,"l"],"push",[7,"woo"]]]],[22,["k"],[46,[2,[15,"l"],"push",[7,"fw"]]]],[22,[18,[17,[15,"l"],"length"],0],[46,[36,[8,"plf",[2,[15,"l"],"join",[7,"."]]]]]]],[46]]],[50,"g",[46],[68,"l",[53,[36,[28,[28,["c","script[data-requiremodule^=\"mage/\"]"]]]]],[46]],[36,false]],[50,"h",[46],[68,"l",[53,[36,[28,[28,["c","script[src^=\"//assets.squarespace.com/\"]"]]]]],[46]],[36,false]],[50,"i",[46],[68,"l",[53,[36,[28,[28,["c","script[id=\"d-js-core\"]"]]]]],[46]],[36,false]],[50,"j",[46],[68,"l",[53,[36,[28,[28,["c",[0,[0,"script[src*=\"woocommerce\"],","link[href*=\"woocommerce\"],"],"[class|=\"woocommerce\"]"]]]]]],[46]],[36,false]],[50,"k",[46],[68,"l",[53,[36,[28,[28,["c",[0,[0,"meta[content*=\"fourthwall\"],","script[src*=\"fourthwall\"],"],"link[href*=\"fourthwall\"]"]]]]]],[46]],[36,false]],[52,"b",[15,"__module_featureFlags"]],[52,"c",["require","internal.getFirstElementByCssSelector"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",["require","internal.isFeatureEnabled"]],[36,[8,"A",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"R"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BZ"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k","l"],[22,[20,[15,"k"],[44]],[46,[53,[3,"k",[20,[2,[15,"j"],"indexOf",[7,"AW-"]],0]]]]],["c",[15,"j"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[20,[15,"n"],[15,"g"]],[20,[15,"n"],[15,"f"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[17,[15,"d"],"R"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[22,[1,[28,[15,"k"]],[2,[15,"m"],"getMetadata",[7,[15,"h"]]]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"d"],"BZ"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_id",[44]]],[2,[15,"m"],"setHitData",[7,"form_name",[44]]],[2,[15,"m"],"setHitData",[7,"form_destination",[44]]],[2,[15,"m"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"n"],[15,"f"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"n"],[15,"g"]],[46,[53,[2,[15,"m"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_form"],[52,"f","form_submit"],[52,"g","form_start"],[52,"h","form_event_canceled"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"R"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BZ"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"R"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"BF"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"GU"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"GU"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"BZ"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"AH"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"R"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BZ"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"R"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"BZ"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"j",[46,"m","n","o"],[50,"t",[46,"v"],[52,"w",[16,[15,"i"],[15,"v"]]],[22,[28,[15,"w"]],[46,[36]]],[53,[41,"x"],[3,"x",0],[63,[7,"x"],[23,[15,"x"],[17,[15,"w"],"length"]],[33,[15,"x"],[3,"x",[0,[15,"x"],1]]],[46,[53,[52,"y",[16,[15,"w"],[15,"x"]]],["q",[15,"p"],[17,[15,"y"],"name"],[17,[15,"y"],"value"]]]]]]],[50,"u",[46,"v"],[22,[30,[28,[15,"r"]],[21,[17,[15,"r"],"length"],2]],[46,[53,[36,false]]]],[41,"w"],[3,"w",[16,[15,"v"],[15,"s"]]],[22,[20,[15,"w"],[44]],[46,[53,[3,"w",[16,[15,"v"],[15,"r"]]]]]],[36,[28,[28,[15,"w"]]]]],[22,[28,[15,"n"]],[46,[36]]],[52,"p",[30,[17,[15,"m"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"q",["h",[15,"f"],[15,"o"]]],[52,"r",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"o"]]],["$0"]]],[52,"s",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"o"]]],["$0"]]],[53,[41,"v"],[3,"v",0],[63,[7,"v"],[23,[15,"v"],[17,[15,"n"],"length"]],[33,[15,"v"],[3,"v",[0,[15,"v"],1]]],[46,[53,[52,"w",[16,[15,"n"],[15,"v"]]],[22,[30,[17,[15,"w"],"disallowAllRegions"],["u",[17,[15,"w"],"disallowedRegions"]]],[46,[53,["t",[17,[15,"w"],"redactFieldGroup"]]]]]]]]]],[50,"k",[46,"m"],[52,"n",[8]],[22,[28,[15,"m"]],[46,[36,[15,"n"]]]],[52,"o",[2,[15,"m"],"split",[7,","]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[2,[16,[15,"o"],[15,"p"]],"trim",[7]]],[22,[28,[15,"q"]],[46,[6]]],[52,"r",[2,[15,"q"],"split",[7,"-"]]],[52,"s",[16,[15,"r"],0]],[52,"t",[39,[20,[17,[15,"r"],"length"],2],[15,"q"],[44]]],[22,[30,[28,[15,"s"]],[21,[17,[15,"s"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"t"],[44]],[30,[23,[17,[15,"t"],"length"],4],[18,[17,[15,"t"],"length"],6]]],[46,[53,[6]]]],[43,[15,"n"],[15,"q"],true]]]]],[36,[15,"n"]]],[50,"l",[46,"m"],[22,[28,[17,[15,"m"],"settingsTable"]],[46,[36,[7]]]],[52,"n",[8]],[53,[41,"o"],[3,"o",0],[63,[7,"o"],[23,[15,"o"],[17,[17,[15,"m"],"settingsTable"],"length"]],[33,[15,"o"],[3,"o",[0,[15,"o"],1]]],[46,[53,[52,"p",[16,[17,[15,"m"],"settingsTable"],[15,"o"]]],[52,"q",[17,[15,"p"],"redactFieldGroup"]],[22,[28,[16,[15,"i"],[15,"q"]]],[46,[6]]],[43,[15,"n"],[15,"q"],[8,"redactFieldGroup",[15,"q"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"r",[16,[15,"n"],[15,"q"]]],[22,[17,[15,"p"],"disallowAllRegions"],[46,[53,[43,[15,"r"],"disallowAllRegions",true],[6]]]],[43,[15,"r"],"disallowedRegions",["k",[17,[15,"p"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"n"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[52,"i",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"j"],"B",[15,"l"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_taskPlatformDetection",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"d",[46,"e"],[52,"f",[2,[15,"c"],"A",[7]]],[22,[15,"f"],[46,[53,[2,[15,"e"],"mergeHitDataForKey",[7,[17,[15,"b"],"FK"],[15,"f"]]]]]]],[52,"b",[15,"__module_gtagSchema"]],[52,"c",[15,"__module_platformDetection"]],[36,[8,"A",[15,"d"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_em_download":{"2":true,"5":true}
,
"__ccd_em_form":{"2":true,"5":true}
,
"__ccd_em_outbound_click":{"2":true,"5":true}
,
"__ccd_em_page_view":{"2":true,"5":true}
,
"__ccd_em_scroll":{"2":true,"5":true}
,
"__ccd_em_site_search":{"2":true,"5":true}
,
"__ccd_em_video":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"1","10":"G-H0B0F8T7E2|GT-5RFH9KJ","11":true,"14":"59h0","15":"0","16":"MTQ5MTAyNDA3MzkyOTI2ODQyMDk=","17":"","19":"dataLayer","2":true,"20":"","21":"www.googletagmanager.com","22":"eyIwIjoiRUciLCIxIjoiRUctREsiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20uZWciLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"EG","31":"EG-DK","32":true,"34":"G-H0B0F8T7E2","35":"G","36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BBQWZll9li8Tmd+OqlNwb9XYZeeBLTyEEfDdyfl3gl6ITkh9JGvMCxm5tJdfJs9M/Dolq5UQN0Je6flBEnb4y08=\",\"version\":0},\"id\":\"72908db7-35a5-4f01-9510-af9cff2d8907\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BDNtRP3bEA9Z/Y+5oND1FoTMBxFrxkAixFVlvm9oeDfXT2Sux0DTPd3hFYUH1tTsKgf6Iy1wrugCi6eEppICl94=\",\"version\":0},\"id\":\"71507b14-a4fd-4187-b40c-f7be48ad8fe8\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BHNVGSrVgWYmkCCEPTRqviY/7WfmT1Tqi0lujHCwazOqGINY/qaGQJI0HD2BOEuUb/g8NkdKyjOAJ6AcVGUne5M=\",\"version\":0},\"id\":\"47a61a9c-f44b-4e89-b355-5a4a76a0be5e\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BPZGWCyLC8kr1L7MzZOjmKVzONpkORWVFCuC8Imx+Q7TQ/2qgF1oUmzVC4fe7q5WBhqK9kqLrFn6zeq26RxIQPo=\",\"version\":0},\"id\":\"3da1ea81-9bde-46ab-a6c7-af7d261640a2\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BAY/i921+UvLEVy4DQTuuwOqUexvHaghO6MmwttxECFpXo/+TBoT1NrnGnnWpccBcMm9XA27NLcWF7FNaN9mu0g=\",\"version\":0},\"id\":\"0d08def1-788e-450e-831f-d066e18f1572\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","45":true,"46":{"1":"1000","10":"5940","11":"59f0","14":"1000","16":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","17":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","2":"9","20":"5000","21":"5000","22":"3.2.0","23":"0.0.0","25":"1","26":"4000","27":"100","3":"5","4":"ad_storage|analytics_storage|ad_user_data|ad_personalization","44":"15000","48":"30000","5":"ad_storage|analytics_storage|ad_user_data","6":"1","60":"0","7":"10"},"48":true,"5":"G-H0B0F8T7E2","55":["G-H0B0F8T7E2"],"56":[],"6":"103579964","8":"res_ts:1727211485065184,srv_cl:808485049,ds:live,cv:1","9":"G-H0B0F8T7E2"}
,"permissions":{
"__c":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{"read_dom_elements":{"allowedCssSelectors":"any"}}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__set_product_settings"

]


}



};




var k,ba=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=da(this),ia=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ma={},na={},oa=function(a,b,c){if(!c||a!=null){var d=na[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},pa=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ma?g=ma:g=fa;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=ia&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ca(ma,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(na[n]===void 0){var r=
Math.random()*1E9>>>0;na[n]=ia?fa.Symbol(n):"$jscp$"+r+"$"+n}ca(g,na[n],{configurable:!0,writable:!0,value:q})}}};pa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ca(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var qa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ra;if(ia&&typeof Object.setPrototypeOf=="function")ra=Object.setPrototypeOf;else{var sa;a:{var va={a:!0},wa={};try{wa.__proto__=va;sa=wa.a;break a}catch(a){}sa=!1}ra=sa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var xa=ra,ya=function(a,b){a.prototype=qa(b.prototype);a.prototype.constructor=a;if(xa)xa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.nr=b.prototype},l=function(a){var b=typeof ma.Symbol!="undefined"&&ma.Symbol.iterator&&a[ma.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ba(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},Aa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},Ba=function(a){return a instanceof Array?a:Aa(l(a))},Da=function(a){return Ca(a,a)},Ca=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ea=ia&&typeof oa(Object,"assign")=="function"?oa(Object,"assign"):function(a,b){if(a==null)throw new TypeError("No nullish arg");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};
pa("Object.assign",function(a){return a||Ea},"es6");var Fa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ga=this||self,Ha=function(a,b){function c(){}c.prototype=b.prototype;a.nr=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.xs=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ia=function(a,b){this.type=a;this.data=b};var Ja=function(){this.map={};this.C={}};Ja.prototype.get=function(a){return this.map["dust."+a]};Ja.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ja.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ja.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ka=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ja.prototype.Ba=function(){return Ka(this,1)};Ja.prototype.sc=function(){return Ka(this,2)};Ja.prototype.Zb=function(){return Ka(this,3)};var La=function(){};La.prototype.reset=function(){};var Ma=function(a,b){this.R=a;this.parent=b;this.M=this.C=void 0;this.Ab=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ja};Ma.prototype.add=function(a,b){Na(this,a,b,!1)};Ma.prototype.nh=function(a,b){Na(this,a,b,!0)};var Na=function(a,b,c,d){if(!a.Ab)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ma.prototype;k.set=function(a,b){this.Ab||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.mb=function(){var a=new Ma(this.R,this);this.C&&a.Mb(this.C);a.Tc(this.H);a.Od(this.M);return a};k.Hd=function(){return this.R};k.Mb=function(a){this.C=a};k.Rm=function(){return this.C};k.Tc=function(a){this.H=a};k.ij=function(){return this.H};k.Ta=function(){this.Ab=!0};k.Od=function(a){this.M=a};k.ob=function(){return this.M};var Oa=function(){this.value={};this.prefix="gtm."};Oa.prototype.set=function(a,b){this.value[this.prefix+String(a)]=b};Oa.prototype.get=function(a){return this.value[this.prefix+String(a)]};Oa.prototype.has=function(a){return this.value.hasOwnProperty(this.prefix+String(a))};function Pa(){try{if(Map)return new Map}catch(a){}return new Oa};var Qa=function(){this.values=[]};Qa.prototype.add=function(a){this.values.indexOf(a)===-1&&this.values.push(a)};Qa.prototype.has=function(a){return this.values.indexOf(a)>-1};var Ra=function(a,b){this.ja=a;this.parent=b;this.R=this.H=void 0;this.Ab=!1;this.M=function(d,e,f){return d.apply(e,f)};this.C=Pa();var c;a:{try{if(Set){c=new Set;break a}}catch(d){}c=new Qa}this.U=c};Ra.prototype.add=function(a,b){Sa(this,a,b,!1)};Ra.prototype.nh=function(a,b){Sa(this,a,b,!0)};var Sa=function(a,b,c,d){a.Ab||a.U.has(b)||(d&&a.U.add(b),a.C.set(b,c))};k=Ra.prototype;
k.set=function(a,b){this.Ab||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.U.has(a)||this.C.set(a,b))};k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.mb=function(){var a=new Ra(this.ja,this);this.H&&a.Mb(this.H);a.Tc(this.M);a.Od(this.R);return a};k.Hd=function(){return this.ja};k.Mb=function(a){this.H=a};k.Rm=function(){return this.H};
k.Tc=function(a){this.M=a};k.ij=function(){return this.M};k.Ta=function(){this.Ab=!0};k.Od=function(a){this.R=a};k.ob=function(){return this.R};var Ta=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.bn=a;this.Jm=c===void 0?!1:c;this.debugInfo=[];this.C=b};ya(Ta,Error);var Ua=function(a){return a instanceof Ta?a:new Ta(a,void 0,!0)};var Wa=[];function Xa(a){return Wa[a]===void 0?!1:Wa[a]};var Ya=Pa();function Za(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=ab(a,e.value),c instanceof Ia);e=d.next());return c}
function ab(a,b){try{if(Xa(17)){var c=b[0],d=b.slice(1),e=String(c),f=Ya.has(e)?Ya.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=Aa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(Ba(m)))}catch(q){var p=a.Rm();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var bb=function(){this.H=new La;this.C=Xa(17)?new Ra(this.H):new Ma(this.H)};k=bb.prototype;k.Hd=function(){return this.H};k.Mb=function(a){this.C.Mb(a)};k.Tc=function(a){this.C.Tc(a)};k.execute=function(a){return this.Gj([a].concat(Ba(Fa.apply(1,arguments))))};k.Gj=function(){for(var a,b=l(Fa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=ab(this.C,c.value);return a};
k.Wo=function(a){var b=Fa.apply(1,arguments),c=this.C.mb();c.Od(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=ab(c,f.value);return d};k.Ta=function(){this.C.Ta()};var cb=function(){this.Fa=!1;this.da=new Ja};k=cb.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Ba=function(){return this.da.Ba()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};k.Ta=function(){this.Fa=!0};k.Ab=function(){return this.Fa};function db(){for(var a=eb,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function fb(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var eb,gb;function hb(a){eb=eb||fb();gb=gb||db();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(eb[m],eb[n],eb[p],eb[q])}return b.join("")}
function ib(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=gb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}eb=eb||fb();gb=gb||db();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var jb={};function kb(a,b){jb[a]=jb[a]||[];jb[a][b]=!0}function lb(){delete jb.GA4_EVENT}function mb(){jb.GTAG_EVENT_FEATURE_CHANNEL=nb}function ob(a){var b=jb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return hb(c.join("")).replace(/\.+$/,"")};function pb(){}function qb(a){return typeof a==="function"}function sb(a){return typeof a==="string"}function tb(a){return typeof a==="number"&&!isNaN(a)}function ub(a){return Array.isArray(a)?a:[a]}function vb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function wb(a,b){if(!tb(a)||!tb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function xb(a,b){for(var c=new yb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function zb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function Ab(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function Bb(a){return Math.round(Number(a))||0}function Cb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function Db(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function Eb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function Fb(){return new Date(Date.now())}function Gb(){return Fb().getTime()}var yb=function(){this.prefix="gtm.";this.values={}};yb.prototype.set=function(a,b){this.values[this.prefix+a]=b};yb.prototype.get=function(a){return this.values[this.prefix+a]};yb.prototype.contains=function(a){return this.get(a)!==void 0};
function Hb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Ib(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Jb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Kb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Lb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Mb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Nb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Ob=/^\w{1,9}$/;function Pb(a,b){a=a||{};b=b||",";var c=[];zb(a,function(d,e){Ob.test(d)&&e&&c.push(d)});return c.join(b)}function Qb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Rb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Sb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Tb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Ub(){var a=w,b;a:{var c=a.crypto||a.msCrypto;if(c&&c.getRandomValues)try{var d=new Uint8Array(25);c.getRandomValues(d);b=btoa(String.fromCharCode.apply(String,Ba(d))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(e){}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Vb=globalThis.trustedTypes,Xb;function Yb(){var a=null;if(!Vb)return a;try{var b=function(c){return c};a=Vb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Zb(){Xb===void 0&&(Xb=Yb());return Xb};var $b=function(a){this.C=a};$b.prototype.toString=function(){return this.C+""};function ac(a){var b=a,c=Zb(),d=c?c.createScriptURL(b):b;return new $b(d)}function bc(a){if(a instanceof $b)return a.C;throw Error("");};var dc=Da([""]),ec=Ca(["\x00"],["\\0"]),fc=Ca(["\n"],["\\n"]),hc=Ca(["\x00"],["\\u0000"]);function ic(a){return a.toString().indexOf("`")===-1}ic(function(a){return a(dc)})||ic(function(a){return a(ec)})||ic(function(a){return a(fc)})||ic(function(a){return a(hc)});var jc=function(a){this.C=a};jc.prototype.toString=function(){return this.C};var kc=function(a){this.Dq=a};function lc(a){return new kc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var mc=[lc("data"),lc("http"),lc("https"),lc("mailto"),lc("ftp"),new kc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function nc(a){var b;b=b===void 0?mc:b;if(a instanceof jc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof kc&&d.Dq(a))return new jc(a)}}var oc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function pc(a){var b;if(a instanceof jc)if(a instanceof jc)b=a.C;else throw Error("");else b=oc.test(a)?a:void 0;return b};function qc(a,b){var c=pc(b);c!==void 0&&(a.action=c)};function rc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var sc=function(a){this.C=a};sc.prototype.toString=function(){return this.C+""};var uc=function(){this.C=tc[0].toLowerCase()};uc.prototype.toString=function(){return this.C};function vc(a,b){var c=[new uc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof uc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var wc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function xc(a){return a===null?"null":a===void 0?"undefined":a};var w=window,yc=window.history,z=document,zc=navigator;function Ac(){var a;try{a=zc.serviceWorker}catch(b){return}return a}var Bc=z.currentScript,Cc=Bc&&Bc.src;function Dc(a,b){var c=w,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Ec(a){return(zc.userAgent||"").indexOf(a)!==-1}function Fc(){return Ec("Firefox")||Ec("FxiOS")}function Gc(){return(Ec("GSA")||Ec("GoogleApp"))&&(Ec("iPhone")||Ec("iPad"))}function Hc(){return Ec("Edg/")||Ec("EdgA/")||Ec("EdgiOS/")}
var Ic={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Jc={height:1,onload:1,src:1,style:1,width:1};function Kc(a,b,c){b&&zb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Lc(a,b,c,d,e){var f=z.createElement("script");Kc(f,d,Ic);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=ac(xc(a));f.src=bc(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Mc(){if(Cc){var a=Cc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Nc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Kc(g,c,Jc);d&&zb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Oc(a,b,c,d){return Pc(a,b,c,d)}function Qc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Rc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Sc(a){w.setTimeout(a,0)}function Tc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Uc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Vc(a){var b=z.createElement("div"),c=b,d,e=xc("A<div>"+a+"</div>"),f=Zb(),g=f?f.createHTML(e):e;d=new sc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof sc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Wc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Xc(a,b,c){var d;try{d=zc.sendBeacon&&zc.sendBeacon(a)}catch(e){kb("TAGGING",15)}d?b==null||b():Pc(a,b,c)}function Yc(a,b){try{return zc.sendBeacon(a,b)}catch(c){kb("TAGGING",15)}return!1}var Zc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function $c(a,b,c,d,e){if(cd()){var f=oa(Object,"assign").call(Object,{},Zc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=w.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),
!1;if(b){var h=Yc(a,b);h?d==null||d():e==null||e();return h}dd(a,d,e);return!0}function cd(){return typeof w.fetch==="function"}function ed(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function fd(){var a=w.performance;if(a&&qb(a.now))return a.now()}
function gd(){var a,b=w.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function hd(){return w.performance||void 0}function id(){var a=w.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Pc=function(a,b,c,d){var e=new Image(1,1);Kc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},dd=Xc;function jd(a,b){return this.evaluate(a)&&this.evaluate(b)}function kd(a,b){return this.evaluate(a)===this.evaluate(b)}function ld(a,b){return this.evaluate(a)||this.evaluate(b)}function md(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function nd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function od(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=w.location.href;d instanceof cb&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var pd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,qd=function(a){if(a==null)return String(a);var b=pd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},rd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},sd=function(a){if(!a||qd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!rd(a,"constructor")&&!rd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
rd(a,b)},td=function(a,b){var c=b||(qd(a)=="array"?[]:{}),d;for(d in a)if(rd(a,d)){var e=a[d];qd(e)=="array"?(qd(c[d])!="array"&&(c[d]=[]),c[d]=td(e,c[d])):sd(e)?(sd(c[d])||(c[d]={}),c[d]=td(e,c[d])):c[d]=e}return c};function ud(a){if(a==void 0||Array.isArray(a)||sd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function vd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var wd=function(a){a=a===void 0?[]:a;this.da=new Ja;this.values=[];this.Fa=!1;for(var b in a)a.hasOwnProperty(b)&&(vd(b)?this.values[Number(b)]=a[Number(b)]:this.da.set(b,a[b]))};k=wd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof wd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Fa)if(a==="length"){if(!vd(b))throw Ua(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else vd(a)?this.values[Number(a)]=b:this.da.set(a,b)};k.get=function(a){return a==="length"?this.length():vd(a)?this.values[Number(a)]:this.da.get(a)};k.length=function(){return this.values.length};k.Ba=function(){for(var a=this.da.Ba(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.sc=function(){for(var a=this.da.sc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Zb=function(){for(var a=this.da.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){vd(a)?delete this.values[Number(a)]:this.Fa||this.da.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,Ba(Fa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=Fa.apply(2,arguments);return b===void 0&&c.length===0?new wd(this.values.splice(a)):new wd(this.values.splice.apply(this.values,[a,b||0].concat(Ba(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,Ba(Fa.apply(0,arguments)))};k.has=function(a){return vd(a)&&this.values.hasOwnProperty(a)||this.da.has(a)};k.Ta=function(){this.Fa=!0;Object.freeze(this.values)};k.Ab=function(){return this.Fa};
function xd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var yd=function(a,b){this.functionName=a;this.Gd=b;this.da=new Ja;this.Fa=!1};k=yd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new wd(this.Ba())};k.invoke=function(a){return this.Gd.call.apply(this.Gd,[new zd(this,a)].concat(Ba(Fa.apply(1,arguments))))};k.apply=function(a,b){return this.Gd.apply(new zd(this,a),b)};k.Kb=function(a){var b=Fa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(Ba(b)))}catch(c){}};
k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Ba=function(){return this.da.Ba()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};k.Ta=function(){this.Fa=!0};k.Ab=function(){return this.Fa};var Ad=function(a,b){yd.call(this,a,b)};ya(Ad,yd);var Bd=function(a,b){yd.call(this,a,b)};ya(Bd,yd);var zd=function(a,b){this.Gd=a;this.J=b};
zd.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?ab(b,a):a};zd.prototype.getName=function(){return this.Gd.getName()};zd.prototype.Hd=function(){return this.J.Hd()};var Cd=function(){this.map=new Map};Cd.prototype.set=function(a,b){this.map.set(a,b)};Cd.prototype.get=function(a){return this.map.get(a)};var Dd=function(){this.keys=[];this.values=[]};Dd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};Dd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function Ed(){try{return Map?new Cd:new Dd}catch(a){return new Dd}};var Fd=function(a){if(a instanceof Fd)return a;if(ud(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Fd.prototype.getValue=function(){return this.value};Fd.prototype.toString=function(){return String(this.value)};var Hd=function(a){this.promise=a;this.Fa=!1;this.da=new Ja;this.da.set("then",Gd(this));this.da.set("catch",Gd(this,!0));this.da.set("finally",Gd(this,!1,!0))};k=Hd.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Ba=function(){return this.da.Ba()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};
var Gd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new Ad("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof Ad||(d=void 0);e instanceof Ad||(e=void 0);var f=this.J.mb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Fd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Hd(h)})};Hd.prototype.Ta=function(){this.Fa=!0};Hd.prototype.Ab=function(){return this.Fa};function B(a,b,c){var d=Ed(),e=function(g,h){for(var m=g.Ba(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof wd){var m=[];d.set(g,m);for(var n=g.Ba(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Hd)return g.promise.then(function(t){return B(t,b,1)},function(t){return Promise.reject(B(t,b,1))});if(g instanceof cb){var q={};d.set(g,q);e(g,q);return q}if(g instanceof Ad){var r=function(){for(var t=
[],v=0;v<arguments.length;v++)t[v]=Id(arguments[v],b,c);var x=new Ma(b?b.Hd():new La);b&&x.Od(b.ob());return f(Xa(17)?g.apply(x,t):g.invoke.apply(g,[x].concat(Ba(t))))};d.set(g,r);e(g,r);return r}var u=!1;switch(c){case 1:u=!0;break;case 2:u=!1;break;case 3:u=!1;break;default:}if(g instanceof Fd&&u)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Id(a,b,c){var d=Ed(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||Ab(g)){var m=new wd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(sd(g)){var p=new cb;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new Ad("",function(){for(var t=Fa.apply(0,arguments),v=[],x=0;x<t.length;x++)v[x]=B(this.evaluate(t[x]),b,c);return f(this.J.ij()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var u=!1;switch(c){case 1:u=!0;break;case 2:u=!1;break;default:}if(g!==void 0&&u)return new Fd(g)};return f(a)};var Jd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof wd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new wd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new wd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new wd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
Ba(Fa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ua(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ua(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=xd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new wd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=xd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(Ba(Fa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,Ba(Fa.apply(1,arguments)))}};var Kd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ld=new Ia("break"),Md=new Ia("continue");function Nd(a,b){return this.evaluate(a)+this.evaluate(b)}function Od(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof wd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ua(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=B(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ua(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Kd.hasOwnProperty(e)){var m=2;m=1;var n=B(f,void 0,m);return Id(d[e].apply(d,n),this.J)}throw Ua(Error("TypeError: "+e+" is not a function"));}if(d instanceof wd){if(d.has(e)){var p=d.get(String(e));if(p instanceof Ad){var q=xd(f);return Xa(17)?p.apply(this.J,q):p.invoke.apply(p,[this.J].concat(Ba(q)))}throw Ua(Error("TypeError: "+e+" is not a function"));
}if(Jd.supportedMethods.indexOf(e)>=0){var r=xd(f);return Jd[e].call.apply(Jd[e],[d,this.J].concat(Ba(r)))}}if(d instanceof Ad||d instanceof cb||d instanceof Hd){if(d.has(e)){var u=d.get(e);if(u instanceof Ad){var t=xd(f);return Xa(17)?u.apply(this.J,t):u.invoke.apply(u,[this.J].concat(Ba(t)))}throw Ua(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof Ad?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Fd&&e==="toString")return d.toString();
throw Ua(Error("TypeError: Object has no '"+e+"' property."));}function Qd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Rd(){var a=Fa.apply(0,arguments),b=this.J.mb(),c=Za(b,a);if(c instanceof Ia)return c}function Sd(){return Ld}
function Td(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ia)return d}}function Ud(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Vd(){return Md}function Wd(a,b){return new Ia(a,this.evaluate(b))}
function Xd(a,b){var c=Fa.apply(2,arguments),d;d=new wd;for(var e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(Ba(c));this.J.add(a,this.evaluate(g))}function Yd(a,b){return this.evaluate(a)/this.evaluate(b)}function Zd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Fd,f=d instanceof Fd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function $d(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}
function ae(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Za(f,d);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}}}function be(a,b,c){if(typeof b==="string")return ae(a,function(){return b.length},function(f){return f},c);if(b instanceof cb||b instanceof Hd||b instanceof wd||b instanceof Ad){var d=b.Ba(),e=d.length;return ae(a,function(){return e},function(f){return d[f]},c)}}
function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){g.set(d,h);return g},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){var m=g.mb();m.nh(d,h);return m},e,f)}function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){var m=g.mb();m.add(d,h);return m},e,f)}
function fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){g.set(d,h);return g},e,f)}function he(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){var m=g.mb();m.nh(d,h);return m},e,f)}function ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){var m=g.mb();m.add(d,h);return m},e,f)}
function ge(a,b,c){if(typeof b==="string")return ae(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof wd)return ae(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ua(Error("The value is not iterable."));}
function je(a,b,c,d){function e(q,r){for(var u=0;u<f.length();u++){var t=f.get(u);r.add(t,q.get(t))}}var f=this.evaluate(a);if(!(f instanceof wd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),m=g.mb();for(e(g,m);ab(m,b);){var n=Za(m,h);if(n instanceof Ia){if(n.type==="break")break;if(n.type==="return")return n}var p=g.mb();e(m,p);ab(p,c);m=p}}
function ke(a,b){var c=Fa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof wd))throw Error("Error: non-List value given for Fn argument names.");return new Ad(a,function(){return function(){var f=Fa.apply(0,arguments),g=d.mb();g.ob()===void 0&&g.Od(this.J.ob());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new wd(h));var r=Za(g,c);if(r instanceof Ia)return r.type===
"return"?r.data:r}}())}function le(a){var b=this.evaluate(a),c=this.J;if(me&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ne(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ua(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof cb||d instanceof Hd||d instanceof wd||d instanceof Ad)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:vd(e)&&(c=d[e]);else if(d instanceof Fd)return;return c}function oe(a,b){return this.evaluate(a)>this.evaluate(b)}function pe(a,b){return this.evaluate(a)>=this.evaluate(b)}
function qe(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Fd&&(c=c.getValue());d instanceof Fd&&(d=d.getValue());return c===d}function re(a,b){return!qe.call(this,a,b)}function se(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Za(this.J,d);if(e instanceof Ia)return e}var me=!1;
function te(a,b){return this.evaluate(a)<this.evaluate(b)}function ue(a,b){return this.evaluate(a)<=this.evaluate(b)}function ve(){for(var a=new wd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function we(){for(var a=new cb,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function xe(a,b){return this.evaluate(a)%this.evaluate(b)}
function ye(a,b){return this.evaluate(a)*this.evaluate(b)}function ze(a){return-this.evaluate(a)}function Ae(a){return!this.evaluate(a)}function Be(a,b){return!Zd.call(this,a,b)}function Ce(){return null}function De(a,b){return this.evaluate(a)||this.evaluate(b)}function Ee(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Fe(a){return this.evaluate(a)}function Ge(){return Fa.apply(0,arguments)}function He(a){return new Ia("return",this.evaluate(a))}
function Ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ua(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof Ad||d instanceof wd||d instanceof cb)&&d.set(String(e),f);return f}function Je(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ke(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ia){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ia&&(g.type==="return"||g.type==="continue")))return g}
function Le(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Me(a){var b=this.evaluate(a);return b instanceof Ad?"function":typeof b}function Ne(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Oe(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Za(this.J,e);if(f instanceof Ia){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Za(this.J,e);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Pe(a){return~Number(this.evaluate(a))}function Qe(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Re(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Se(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Te(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ue(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ve(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function We(){}
function Xe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ia)return d}catch(h){if(!(h instanceof Ta&&h.Jm))throw h;var e=this.J.mb();a!==""&&(h instanceof Ta&&(h=h.bn),e.add(a,new Fd(h)));var f=this.evaluate(c),g=Za(e,f);if(g instanceof Ia)return g}}function Ye(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ta&&f.Jm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ia)return e;if(c)throw c;if(d instanceof Ia)return d};var $e=function(){this.C=new bb;Ze(this)};$e.prototype.execute=function(a){return this.C.Gj(a)};var Ze=function(a){var b=function(c,d){var e=new Bd(String(c),d);e.Ta();var f=String(c);a.C.C.set(f,e);Ya.set(f,e)};b("map",we);b("and",jd);b("contains",md);b("equals",kd);b("or",ld);b("startsWith",nd);b("variable",od)};$e.prototype.Mb=function(a){this.C.Mb(a)};var bf=function(){this.H=!1;this.C=new bb;af(this);this.H=!0};bf.prototype.execute=function(a){return cf(this.C.Gj(a))};var df=function(a,b,c){return cf(a.C.Wo(b,c))};bf.prototype.Ta=function(){this.C.Ta()};
var af=function(a){var b=function(c,d){var e=String(c),f=new Bd(e,d);f.Ta();a.C.C.set(e,f);Ya.set(e,f)};b(0,Nd);b(1,Od);b(2,Pd);b(3,Qd);b(56,Te);b(57,Qe);b(58,Pe);b(59,Ve);b(60,Re);b(61,Se);b(62,Ue);b(53,Rd);b(4,Sd);b(5,Td);b(68,Xe);b(52,Ud);b(6,Vd);b(49,Wd);b(7,ve);b(8,we);b(9,Td);b(50,Xd);b(10,Yd);b(12,Zd);b(13,$d);b(67,Ye);b(51,ke);b(47,ce);b(54,de);b(55,ee);b(63,je);b(64,fe);b(65,he);b(66,ie);b(15,le);b(16,ne);b(17,ne);b(18,oe);b(19,pe);b(20,qe);b(21,re);b(22,se);b(23,te);b(24,ue);b(25,xe);b(26,
ye);b(27,ze);b(28,Ae);b(29,Be);b(45,Ce);b(30,De);b(32,Ee);b(33,Ee);b(34,Fe);b(35,Fe);b(46,Ge);b(36,He);b(43,Ie);b(37,Je);b(38,Ke);b(39,Le);b(40,Me);b(44,We);b(41,Ne);b(42,Oe)};bf.prototype.Hd=function(){return this.C.Hd()};bf.prototype.Mb=function(a){this.C.Mb(a)};bf.prototype.Tc=function(a){this.C.Tc(a)};
function cf(a){if(a instanceof Ia||a instanceof Ad||a instanceof wd||a instanceof cb||a instanceof Hd||a instanceof Fd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var ef=function(a){this.message=a};function ff(a){a.Ds=!0;return a};var gf=ff(function(a){return typeof a==="string"});function hf(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new ef("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function jf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var kf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function lf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+hf(e)+c}a<<=2;d||(a|=32);return c=""+hf(a|b)+c}
function mf(a,b){var c;var d=a.Fh,e=a.Um;d===void 0?c="":(e||(e=0),c=""+lf(1,1)+hf(d<<2|e));var f=a.Bp,g=a.Hp,h="4"+c+(f?""+lf(2,1)+hf(f):"")+(g?""+lf(12,1)+hf(g):""),m,n=a.on;m=n&&kf.test(n)?""+lf(3,2)+n:"";var p,q=a.ln;p=q?""+lf(4,1)+hf(q):"";var r;var u=a.ctid;if(u&&b){var t=lf(5,3),v=u.split("-"),x=v[0].toUpperCase();if(x!=="GTM"&&x!=="OPT")r="";else{var y=v[1];r=""+t+hf(1+y.length)+(a.Eq||0)+y}}else r="";var A=a.lr,D=a.canonicalId,E=a.Pa,J=a.Hs,F=h+m+p+r+(A?""+lf(6,1)+hf(A):"")+(D?""+lf(7,3)+
hf(D.length)+D:"")+(E?""+lf(8,3)+hf(E.length)+E:"")+(J?""+lf(9,3)+hf(J.length)+J:""),M;var U=a.Ip;U=U===void 0?{}:U;for(var ha=[],S=l(Object.keys(U)),aa=S.next();!aa.done;aa=S.next()){var ta=aa.value;ha[Number(ta)]=U[ta]}if(ha.length){var la=lf(10,3),ea;if(ha.length===0)ea=hf(0);else{for(var Z=[],ka=0,za=!1,ua=0;ua<ha.length;ua++){za=!0;var Va=ua%6;ha[ua]&&(ka|=1<<Va);Va===5&&(Z.push(hf(ka)),ka=0,za=!1)}za&&Z.push(hf(ka));ea=Z.join("")}var $a=ea;M=""+la+hf($a.length)+$a}else M="";var cc=a.Kq,Wb=a.Yq,
rb=a.mr;return F+M+(cc?""+lf(11,3)+hf(cc.length)+cc:"")+(Wb?""+lf(13,3)+hf(Wb.length)+Wb:"")+(rb?""+lf(14,1)+hf(rb):"")};var nf=function(){function a(b){return{toString:function(){return b}}}return{En:a("consent"),ek:a("convert_case_to"),fk:a("convert_false_to"),gk:a("convert_null_to"),hk:a("convert_true_to"),ik:a("convert_undefined_to"),Er:a("debug_mode_metadata"),Ra:a("function"),ah:a("instance_name"),ap:a("live_only"),bp:a("malware_disabled"),METADATA:a("metadata"),fp:a("original_activity_id"),Yr:a("original_vendor_template_id"),Xr:a("once_on_load"),ep:a("once_per_event"),fm:a("once_per_load"),bs:a("priority_override"),
hs:a("respected_consent_types"),om:a("setup_tags"),mh:a("tag_id"),Am:a("teardown_tags")}}();var Jf;var Kf=[],Lf=[],Mf=[],Nf=[],Of=[],Pf,Qf,Rf;function Sf(a){Rf=Rf||a}
function Tf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Kf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Nf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Mf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Uf(p[r])}Lf.push(p)}}
function Uf(a){}var Vf,Wf=[],Xf=[];function Yf(a,b){var c={};c[nf.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Zf(a,b,c){try{return Qf($f(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var $f=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=ag(a[e],b,c));return d},ag=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(ag(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Kf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[nf.ah]);try{var m=$f(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=bg(m,{event:b,index:f,type:2,
name:h});Vf&&(d=Vf.Jp(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[ag(a[n],b,c)]=ag(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=ag(a[q],b,c);Rf&&(p=p||Rf.Aq(r));d.push(r)}return Rf&&p?Rf.Op(d):d.join("");case "escape":d=ag(a[1],b,c);if(Rf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Rf.Bq(a))return Rf.Qq(d);d=String(d);for(var u=2;u<a.length;u++)uf[a[u]]&&(d=uf[a[u]](d));return d;
case "tag":var t=a[1];if(!Nf[t])throw Error("Unable to resolve tag reference "+t+".");return{Om:a[2],index:t};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[nf.Ra]=a[1];var x=Zf(v,b,c),y=!!a[4];return y||x!==2?y!==(x===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},bg=function(a,b){var c=a[nf.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Pf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Wf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Lb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Kf[q];break;case 1:r=Nf[q];break;default:n="";break a}var u=r&&r[nf.ah];n=u?String(u):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var t,v,x;if(f&&Xf.indexOf(c)===-1){Xf.push(c);
var y=Gb();t=e(g);var A=Gb()-y,D=Gb();v=Jf(c,h,b);x=A-(Gb()-D)}else if(e&&(t=e(g)),!e||f)v=Jf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),ud(t)?(Array.isArray(t)?Array.isArray(v):sd(t)?sd(v):typeof t==="function"?typeof v==="function":t===v)||d.reportMacroDiscrepancy(d.id,c):t!==v&&d.reportMacroDiscrepancy(d.id,c),x!==void 0&&d.reportMacroDiscrepancy(d.id,c,x));return e?t:v};var cg=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ya(cg,Error);cg.prototype.getMessage=function(){return this.message};function dg(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)dg(a[c],b[c])}};function eg(){return function(a,b){var c;var d=fg;a instanceof Ta?(a.C=d,c=a):c=new Ta(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function fg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)tb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function gg(a){function b(r){for(var u=0;u<r.length;u++)d[r[u]]=!0}for(var c=[],d=[],e=hg(a),f=0;f<Lf.length;f++){var g=Lf[f],h=ig(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Nf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function ig(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function hg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Zf(Mf[c],a));return b[c]}};function jg(a,b){b[nf.ek]&&typeof a==="string"&&(a=b[nf.ek]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(nf.gk)&&a===null&&(a=b[nf.gk]);b.hasOwnProperty(nf.ik)&&a===void 0&&(a=b[nf.ik]);b.hasOwnProperty(nf.hk)&&a===!0&&(a=b[nf.hk]);b.hasOwnProperty(nf.fk)&&a===!1&&(a=b[nf.fk]);return a};var kg=function(){this.C={}},mg=function(a,b){var c=lg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,Ba(Fa.apply(0,arguments)))})};function ng(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new cg(c,d,g);}}
function og(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(Ba(Fa.apply(1,arguments))));ng(e,b,d,g);ng(f,b,d,g)}}}};var rg=function(a,b){var c=this;this.H={};this.C=new kg;var d={},e={},f=og(this.C,a,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(Ba(Fa.apply(1,arguments)))):{}});zb(b,function(g,h){function m(p){var q=Fa.apply(1,arguments);if(!n[p])throw pg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(Ba(q)))}var n={};zb(h,function(p,q){var r=qg(p,q);n[p]=r.assert;d[p]||(d[p]=r.V);r.Hm&&!e[p]&&(e[p]=r.Hm)});c.H[g]=function(p,q){var r=n[p];if(!r)throw pg(p,
{},"The requested permission "+p+" is not configured.");var u=Array.prototype.slice.call(arguments,0);r.apply(void 0,u);f.apply(void 0,u);var t=e[p];t&&t.apply(null,[m].concat(Ba(u.slice(1))))}})},sg=function(a){return lg.H[a]||function(){}};function qg(a,b){var c=Yf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=pg;try{return bg(c)}catch(d){return{assert:function(e){throw new cg(e,{},"Permission "+e+" is unknown.");},V:function(){throw new cg(a,{},"Permission "+a+" is unknown.");}}}}
function pg(a,b,c){return new cg(a,b,c)};var tg=!1;var ug={};ug.zr=Cb('');ug.Bs=Cb('');
var yg=function(a){var b={},c=0;zb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(vg.hasOwnProperty(e))b[vg[e]]=g;else if(wg.hasOwnProperty(e)){var h=wg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=xg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var u=String.fromCharCode(c<10?48+c:65+c-10);b["k"+u]=(""+String(e)).replace(/~/g,"~~");b["v"+u]=g;c++}}});var d=[];zb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
vg={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},wg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},xg=["ca",
"c2","c3","c4","c5"];var zg=[];function Ag(a){switch(a){case 1:return 0;case 235:return 18;case 38:return 13;case 256:return 11;case 257:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 17;case 75:return 3;case 103:return 14;case 197:return 15;case 109:return 19;case 116:return 4;case 135:return 8;case 136:return 5;case 261:return 20}}function Bg(a,b){zg[a]=b;var c=Ag(a);c!==void 0&&(Wa[c]=b)}function C(a){Bg(a,!0)}C(39);
C(145);C(153);C(144);C(120);C(5);C(111);C(139);
C(87);C(92);C(159);
C(132);C(20);C(72);
C(113);C(154);C(116);Bg(23,!1),C(24);C(29);Cg(26,25);
C(37);C(9);C(91);C(123);C(158);C(71);
C(136);
C(127);C(27);
C(69);C(135);C(95);
C(38);C(103);C(112);C(101);
C(122);C(121);C(21);C(134);C(22);
C(141);C(90);
C(59);C(175);C(177);
C(185);
C(197);C(200);
C(206);C(231);C(232);C(241);
function G(a){return!!zg[a]}
function Cg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?C(b):C(a)};
var Dg=function(){this.events=[];this.C="";this.ra={};this.baseUrl="";this.M=0;this.R=this.H=!1;this.endpoint=0;G(89)&&(this.R=!0)};Dg.prototype.add=function(a){return this.U(a)?(this.events.push(a),this.C=a.H,this.ra=a.ra,this.baseUrl=a.baseUrl,this.M+=a.R,this.H=a.M,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ja=a.eventId,this.ma=a.priorityId,!0):!1};Dg.prototype.U=function(a){return this.events.length?this.events.length>=20||a.R+this.M>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.M&&this.Ja(a):!0};Dg.prototype.Ja=function(a){var b=this;if(!this.R)return this.C===a.H;var c=Object.keys(this.ra);return c.length===Object.keys(a.ra).length&&c.every(function(d){return a.ra.hasOwnProperty(d)&&String(b.ra[d])===String(a.ra[d])})};var H={P:{Nn:1,Pn:2,Bm:3,hm:4,pk:5,qk:6,To:7,Qn:8,So:9,Mn:10,Ln:11,tm:12,qm:13,Wj:14,yn:15,An:16,bm:17,rk:18,am:19,On:20,cp:21,Dn:22,zn:23,Bn:24,nk:25,Uj:26,np:27,Jl:28,Sl:29,Rl:30,Ql:31,Ml:32,Kl:33,Ll:34,Il:35,Hl:36}};H.P[H.P.Nn]="CREATE_EVENT_SOURCE";H.P[H.P.Pn]="EDIT_EVENT";H.P[H.P.Bm]="TRAFFIC_TYPE";H.P[H.P.hm]="REFERRAL_EXCLUSION";H.P[H.P.pk]="ECOMMERCE_FROM_GTM_TAG";H.P[H.P.qk]="ECOMMERCE_FROM_GTM_UA_SCHEMA";H.P[H.P.To]="GA_SEND";H.P[H.P.Qn]="EM_FORM";H.P[H.P.So]="GA_GAM_LINK";H.P[H.P.Mn]="CREATE_EVENT_AUTO_PAGE_PATH";
H.P[H.P.Ln]="CREATED_EVENT";H.P[H.P.tm]="SIDELOADED";H.P[H.P.qm]="SGTM_LEGACY_CONFIGURATION";H.P[H.P.Wj]="CCD_EM_EVENT";H.P[H.P.yn]="AUTO_REDACT_EMAIL";H.P[H.P.An]="AUTO_REDACT_QUERY_PARAM";H.P[H.P.bm]="MULTIPLE_PAGEVIEW_FROM_CONFIG";H.P[H.P.rk]="EM_EVENT_SENT_BEFORE_CONFIG";H.P[H.P.am]="LOADED_VIA_CST_OR_SIDELOADING";H.P[H.P.On]="DECODED_PARAM_MATCH";H.P[H.P.cp]="NON_DECODED_PARAM_MATCH";H.P[H.P.Dn]="CCD_EVENT_SGTM";H.P[H.P.zn]="AUTO_REDACT_EMAIL_SGTM";H.P[H.P.Bn]="AUTO_REDACT_QUERY_PARAM_SGTM";
H.P[H.P.nk]="DAILY_LIMIT_REACHED";H.P[H.P.Uj]="BURST_LIMIT_REACHED";H.P[H.P.np]="SHARED_USER_ID_SET_AFTER_REQUEST";H.P[H.P.Jl]="GA4_MULTIPLE_SESSION_COOKIES";H.P[H.P.Sl]="INVALID_GA4_SESSION_COUNT";H.P[H.P.Rl]="INVALID_GA4_LAST_EVENT_TIMESTAMP";H.P[H.P.Ql]="INVALID_GA4_JOIN_TIMER";H.P[H.P.Ml]="GA4_STALE_SESSION_COOKIE_SELECTED";H.P[H.P.Kl]="GA4_SESSION_COOKIE_GS1_READ";H.P[H.P.Ll]="GA4_SESSION_COOKIE_GS2_READ";H.P[H.P.Il]="GA4_DL_PARAM_RECOVERY_AVAILABLE";H.P[H.P.Hl]="GA4_DL_PARAM_RECOVERY_APPLIED";var Eg={},Fg=(Eg.uaa=!0,Eg.uab=!0,Eg.uafvl=!0,Eg.uamb=!0,Eg.uam=!0,Eg.uap=!0,Eg.uapv=!0,Eg.uaw=!0,Eg);
var Ig=function(a,b){var c=a.events;if(c.length===1)return Gg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)zb(c[f].Pd,function(u,t){t!=null&&(e[u]=e[u]||{},e[u][String(t)]=e[u][String(t)]+1||1)});var g={};zb(e,function(u,t){var v,x=-1,y=0;zb(t,function(A,D){y+=D;var E=(A.length+u.length+2)*(D-1);E>x&&(v=A,x=E)});y===c.length&&(g[u]=v)});Hg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={yj:void 0},p++){var q=[];n.yj={};zb(c[p].Pd,function(u){return function(t,
v){g[t]!==""+v&&(u.yj[t]=v)}}(n));c[p].C&&q.push(c[p].C);Hg(n.yj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Gg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Hg(a.Pd,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Hg=function(a,b){zb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Jg=function(a){var b=[];zb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Kg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.ra=a.ra;this.Pd=a.Pd;this.fj=a.fj;this.M=d;this.H=Jg(a.ra);this.C=Jg(a.fj);this.R=this.C.length;if(e&&this.R>16384)throw Error("EVENT_TOO_LARGE");};
var Ng=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Lg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Mg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Lb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Mg=/^[a-z$_][\w-$]*$/i,Lg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Og=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Pg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Qg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Rg=new yb;function Sg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Rg.get(e);f||(f=new RegExp(b,d),Rg.set(e,f));return f.test(a)}catch(g){return!1}}function Tg(a,b){return String(a).indexOf(String(b))>=0}
function Ug(a,b){return String(a)===String(b)}function Vg(a,b){return Number(a)>=Number(b)}function Wg(a,b){return Number(a)<=Number(b)}function Xg(a,b){return Number(a)>Number(b)}function Yg(a,b){return Number(a)<Number(b)}function Zg(a,b){return Lb(String(a),String(b))};var fh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,gh={Fn:"function",PixieMap:"Object",List:"Array"};
function hh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=fh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof Ad?n="Fn":m instanceof wd?n="List":m instanceof cb?n="PixieMap":m instanceof Hd?n="PixiePromise":m instanceof Fd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((gh[n]||n)+", which does not match required type ")+
((gh[h]||h)+"."));}}}function I(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof Ad?d.push("function"):g instanceof wd?d.push("Array"):g instanceof cb?d.push("Object"):g instanceof Hd?d.push("Promise"):g instanceof Fd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ih(a){return a instanceof cb}function jh(a){return ih(a)||a===null||kh(a)}
function lh(a){return a instanceof Ad}function mh(a){return lh(a)||a===null||kh(a)}function nh(a){return a instanceof wd}function oh(a){return a instanceof Fd}function K(a){return typeof a==="string"}function ph(a){return K(a)||a===null||kh(a)}function qh(a){return typeof a==="boolean"}function rh(a){return qh(a)||kh(a)}function sh(a){return qh(a)||a===null||kh(a)}function th(a){return typeof a==="number"}function kh(a){return a===void 0};function uh(a){return""+a}
function vh(a,b){var c=[];return c};function wh(a,b){var c=new Ad(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ua(g);}});c.Ta();return c}
function xh(a,b){var c=new cb,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];qb(e)?c.set(d,wh(a+"_"+d,e)):sd(e)?c.set(d,xh(a+"_"+d,e)):(tb(e)||sb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ta();return c};function yh(a,b){if(!K(a))throw I(this.getName(),["string"],arguments);if(!ph(b))throw I(this.getName(),["string","undefined"],arguments);var c={},d=new cb;return d=xh("AssertApiSubject",
c)};function zh(a,b){if(!ph(b))throw I(this.getName(),["string","undefined"],arguments);if(a instanceof Hd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new cb;return d=xh("AssertThatSubject",c)};function Ah(a){return function(){for(var b=Fa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(B(b[e],d));return Id(a.apply(null,c))}}function Bh(){for(var a=Math,b=Ch,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Ah(a[e].bind(a)))}return c};function Dh(a){return a!=null&&Lb(a,"__cvt_")};function Eh(a){var b;return b};function Fh(a){var b;return b};function Gh(a){try{return encodeURI(a)}catch(b){}};function Hh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Ih=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Jh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Ih(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Ih(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Lh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Jh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Kh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Kh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Lh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Sg(d(c[0]),d(c[1]),!1);case 5:return Ug(d(c[0]),d(c[1]));case 6:return Zg(d(c[0]),d(c[1]));case 7:return Pg(d(c[0]),d(c[1]));case 8:return Tg(d(c[0]),d(c[1]));case 9:return Yg(d(c[0]),d(c[1]));case 10:return Wg(d(c[0]),d(c[1]));case 11:return Xg(d(c[0]),d(c[1]));case 12:return Vg(d(c[0]),d(c[1]));case 13:return Qg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Mh(a){if(!ph(a))throw I(this.getName(),["string|undefined"],arguments);};function Nh(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};function Ph(a){var b=B(a);return Nh(b?""+b:"")};function Qh(a,b){if(!th(a)||!th(b))throw I(this.getName(),["number","number"],arguments);return wb(a,b)};function Rh(){return(new Date).getTime()};function Sh(a){if(a===null)return"null";if(a instanceof wd)return"array";if(a instanceof Ad)return"function";if(a instanceof Fd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Th(a){function b(c){return function(d){try{return c(d)}catch(e){(tg||ug.zr)&&a.call(this,e.message)}}}return{parse:b(function(c){return Id(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(B(c))}),publicName:"JSON"}};function Uh(a){return Bb(B(a,this.J))};function Vh(a){return Number(B(a,this.J))};function Wh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Xh(a,b,c){var d=null,e=!1;return e?d:null};var Ch="floor ceil round max min abs pow sqrt".split(" ");function Yh(){var a={};return{cq:function(b){return a.hasOwnProperty(b)?a[b]:void 0},rn:function(b,c){a[b]=c},reset:function(){a={}}}}function Zh(a,b){return function(){return Ad.prototype.invoke.apply(a,[b].concat(Ba(Fa.apply(0,arguments))))}}
function $h(a,b){if(!K(a))throw I(this.getName(),["string","any"],arguments);}
function ai(a,b){if(!K(a)||!ih(b))throw I(this.getName(),["string","PixieMap"],arguments);};var bi={};var ci=function(a){var b=new cb;if(a instanceof wd)for(var c=a.Ba(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof Ad)for(var f=a.Ba(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
bi.keys=function(a){hh(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=ci(a);if(a instanceof cb||a instanceof Hd)return new wd(a.Ba());return new wd};
bi.values=function(a){hh(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=ci(a);if(a instanceof cb||a instanceof Hd)return new wd(a.sc());return new wd};
bi.entries=function(a){hh(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=ci(a);if(a instanceof cb||a instanceof Hd)return new wd(a.Zb().map(function(b){return new wd(b)}));return new wd};
bi.freeze=function(a){(a instanceof cb||a instanceof Hd||a instanceof wd||a instanceof Ad)&&a.Ta();return a};bi.delete=function(a,b){if(a instanceof cb&&!a.Ab())return a.remove(b),!0;return!1};function L(a,b){var c=Fa.apply(2,arguments),d=a.J.ob();if(!d)throw Error("Missing program state.");if(d.Wq){try{d.Im.apply(null,[b].concat(Ba(c)))}catch(e){throw kb("TAGGING",21),e;}return}d.Im.apply(null,[b].concat(Ba(c)))};var di=function(){this.H={};this.C={};this.M=!0;};di.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};di.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
di.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:qb(b)?wh(a,b):xh(a,b)};function ei(a,b){var c=void 0;return c};function fi(){var a={};
return a};var N={m:{Ma:"ad_personalization",aa:"ad_storage",W:"ad_user_data",ka:"analytics_storage",hc:"region",ia:"consent_updated",ug:"wait_for_update",Rn:"app_remove",Sn:"app_store_refund",Tn:"app_store_subscription_cancel",Un:"app_store_subscription_convert",Vn:"app_store_subscription_renew",Wn:"consent_update",tk:"add_payment_info",uk:"add_shipping_info",Ud:"add_to_cart",Vd:"remove_from_cart",vk:"view_cart",Xc:"begin_checkout",Wd:"select_item",jc:"view_item_list",yc:"select_promotion",kc:"view_promotion",
rb:"purchase",Xd:"refund",mc:"view_item",wk:"add_to_wishlist",Xn:"exception",Yn:"first_open",Zn:"first_visit",na:"gtag.config",nc:"gtag.get",ao:"in_app_purchase",Yc:"page_view",bo:"screen_view",co:"session_start",eo:"source_update",fo:"timing_complete",ho:"track_social",Yd:"user_engagement",io:"user_id_update",Oe:"gclid_link_decoration_source",Pe:"gclid_storage_source",oc:"gclgb",sb:"gclid",xk:"gclid_len",Zd:"gclgs",ae:"gcllp",be:"gclst",Ha:"ads_data_redaction",Qe:"gad_source",Re:"gad_source_src",
Zc:"gclid_url",yk:"gclsrc",Se:"gbraid",ce:"wbraid",Ob:"allow_ad_personalization_signals",Ag:"allow_custom_scripts",Te:"allow_direct_google_requests",Bg:"allow_display_features",Qh:"allow_enhanced_conversions",Pb:"allow_google_signals",Rh:"allow_interest_groups",jo:"app_id",ko:"app_installer_id",lo:"app_name",mo:"app_version",bd:"auid",Hr:"auto_detection_enabled",zk:"aw_remarketing",Sh:"aw_remarketing_only",Cg:"discount",Dg:"aw_feed_country",Eg:"aw_feed_language",ya:"items",Fg:"aw_merchant_id",Ak:"aw_basket_type",
Ue:"campaign_content",Ve:"campaign_id",We:"campaign_medium",Xe:"campaign_name",Ye:"campaign",Ze:"campaign_source",af:"campaign_term",Qb:"client_id",Bk:"rnd",Th:"consent_update_type",no:"content_group",oo:"content_type",tb:"conversion_cookie_prefix",Uh:"conversion_id",kb:"conversion_linker",Vh:"conversion_linker_disabled",dd:"conversion_api",Gg:"cookie_deprecation",ub:"cookie_domain",wb:"cookie_expires",Bb:"cookie_flags",ed:"cookie_name",Rb:"cookie_path",Wa:"cookie_prefix",zc:"cookie_update",fd:"country",
lb:"currency",Wh:"customer_buyer_stage",bf:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",cf:"custom_map",Hg:"gcldc",gd:"dclid",Ck:"debug_mode",Ea:"developer_id",po:"disable_merchant_reported_purchases",hd:"dc_custom_params",qo:"dc_natural_search",Dk:"dynamic_event_settings",Ek:"affiliation",Ig:"checkout_option",Zh:"checkout_step",Fk:"coupon",df:"item_list_name",ai:"list_name",ro:"promotions",ee:"shipping",Gk:"tax",Jg:"engagement_time_msec",Kg:"enhanced_client_id",so:"enhanced_conversions",
Ir:"enhanced_conversions_automatic_settings",ef:"estimated_delivery_date",bi:"euid_logged_in_state",ff:"event_callback",uo:"event_category",Ac:"event_developer_id_string",vo:"event_label",Bc:"event",Lg:"event_settings",Mg:"event_timeout",wo:"description",xo:"fatal",yo:"experiments",di:"firebase_id",fe:"first_party_collection",Ng:"_x_20",qc:"_x_19",zo:"flight_error_code",Ao:"flight_error_message",Hk:"fl_activity_category",Ik:"fl_activity_group",ei:"fl_advertiser_id",Jk:"fl_ar_dedupe",hf:"match_id",
Kk:"fl_random_number",Lk:"tran",Mk:"u",Og:"gac_gclid",he:"gac_wbraid",Nk:"gac_wbraid_multiple_conversions",Ok:"ga_restrict_domain",Pk:"ga_temp_client_id",Bo:"ga_temp_ecid",ie:"gdpr_applies",Qk:"geo_granularity",jf:"value_callback",kf:"value_key",Dc:"google_analysis_params",je:"_google_ng",ke:"google_signals",Rk:"google_tld",lf:"gpp_sid",nf:"gpp_string",Pg:"groups",Sk:"gsa_experiment_id",pf:"gtag_event_feature_usage",Tk:"gtm_up",Ec:"iframe_state",qf:"ignore_referrer",fi:"internal_traffic_results",
Uk:"_is_fpm",Fc:"is_legacy_converted",Gc:"is_legacy_loaded",gi:"is_passthrough",jd:"_lps",xb:"language",Qg:"legacy_developer_id_string",Xa:"linker",rf:"accept_incoming",Hc:"decorate_forms",oa:"domains",kd:"url_position",ld:"merchant_feed_label",md:"merchant_feed_language",nd:"merchant_id",Vk:"method",Co:"name",Wk:"navigation_type",tf:"new_customer",Rg:"non_interaction",Do:"optimize_id",Xk:"page_hostname",uf:"page_path",Ya:"page_referrer",Cb:"page_title",Yk:"passengers",Zk:"phone_conversion_callback",
Eo:"phone_conversion_country_code",al:"phone_conversion_css_class",Fo:"phone_conversion_ids",bl:"phone_conversion_number",fl:"phone_conversion_options",Go:"_platinum_request_status",Ho:"_protected_audience_enabled",me:"quantity",Sg:"redact_device_info",hi:"referral_exclusion_definition",Jr:"_request_start_time",Sb:"restricted_data_processing",Io:"retoken",Jo:"sample_rate",ii:"screen_name",Ic:"screen_resolution",il:"_script_source",Ko:"search_term",od:"send_page_view",pd:"send_to",rd:"server_container_url",
Lo:"session_attributes_encoded",vf:"session_duration",Tg:"session_engaged",ji:"session_engaged_time",Tb:"session_id",Ug:"session_number",wf:"_shared_user_id",ne:"delivery_postal_code",Kr:"_tag_firing_delay",Lr:"_tag_firing_time",Mr:"temporary_client_id",ki:"_timezone",li:"topmost_url",Vg:"tracking_id",mi:"traffic_type",Qa:"transaction_id",rc:"transport_url",jl:"trip_type",sd:"update",Db:"url_passthrough",kl:"uptgs",xf:"_user_agent_architecture",yf:"_user_agent_bitness",zf:"_user_agent_full_version_list",
Af:"_user_agent_mobile",Bf:"_user_agent_model",Cf:"_user_agent_platform",Df:"_user_agent_platform_version",Ef:"_user_agent_wow64",yb:"user_data",ml:"user_data_auto_latency",nl:"user_data_auto_meta",ol:"user_data_auto_multi",pl:"user_data_auto_selectors",ql:"user_data_auto_status",Eb:"user_data_mode",rl:"user_data_settings",Na:"user_id",Ub:"user_properties",sl:"_user_region",Ff:"us_privacy_string",Ia:"value",tl:"wbraid_multiple_conversions",vd:"_fpm_parameters",wi:"_host_name",Tl:"_in_page_command",
yi:"_ip_override",Xl:"_is_passthrough_cid",Gi:"_measurement_type",Dd:"non_personalized_ads",Ni:"_sst_parameters",mp:"sgtm_geo_user_country",de:"conversion_label",za:"page_location",Cc:"global_developer_id_string",oe:"tc_privacy_string"}};var gi={},hi=(gi[N.m.ia]="gcu",gi[N.m.oc]="gclgb",gi[N.m.sb]="gclaw",gi[N.m.xk]="gclid_len",gi[N.m.Zd]="gclgs",gi[N.m.ae]="gcllp",gi[N.m.be]="gclst",gi[N.m.bd]="auid",gi[N.m.Cg]="dscnt",gi[N.m.Dg]="fcntr",gi[N.m.Eg]="flng",gi[N.m.Fg]="mid",gi[N.m.Ak]="bttype",gi[N.m.Qb]="gacid",gi[N.m.de]="label",gi[N.m.dd]="capi",gi[N.m.Gg]="pscdl",gi[N.m.lb]="currency_code",gi[N.m.Wh]="clobs",gi[N.m.bf]="vdltv",gi[N.m.Xh]="clolo",gi[N.m.Yh]="clolb",gi[N.m.Ck]="_dbg",gi[N.m.ef]="oedeld",gi[N.m.Ac]="edid",gi[N.m.Og]=
"gac",gi[N.m.he]="gacgb",gi[N.m.Nk]="gacmcov",gi[N.m.ie]="gdpr",gi[N.m.Cc]="gdid",gi[N.m.je]="_ng",gi[N.m.lf]="gpp_sid",gi[N.m.nf]="gpp",gi[N.m.Sk]="gsaexp",gi[N.m.pf]="_tu",gi[N.m.Ec]="frm",gi[N.m.gi]="gtm_up",gi[N.m.jd]="lps",gi[N.m.Qg]="did",gi[N.m.ld]="fcntr",gi[N.m.md]="flng",gi[N.m.nd]="mid",gi[N.m.tf]=void 0,gi[N.m.Cb]="tiba",gi[N.m.Sb]="rdp",gi[N.m.Tb]="ecsid",gi[N.m.wf]="ga_uid",gi[N.m.ne]="delopc",gi[N.m.oe]="gdpr_consent",gi[N.m.Qa]="oid",gi[N.m.kl]="uptgs",gi[N.m.xf]="uaa",gi[N.m.yf]=
"uab",gi[N.m.zf]="uafvl",gi[N.m.Af]="uamb",gi[N.m.Bf]="uam",gi[N.m.Cf]="uap",gi[N.m.Df]="uapv",gi[N.m.Ef]="uaw",gi[N.m.ml]="ec_lat",gi[N.m.nl]="ec_meta",gi[N.m.ol]="ec_m",gi[N.m.pl]="ec_sel",gi[N.m.ql]="ec_s",gi[N.m.Eb]="ec_mode",gi[N.m.Na]="userId",gi[N.m.Ff]="us_privacy",gi[N.m.Ia]="value",gi[N.m.tl]="mcov",gi[N.m.wi]="hn",gi[N.m.Tl]="gtm_ee",gi[N.m.yi]="uip",gi[N.m.Gi]="mt",gi[N.m.Dd]="npa",gi[N.m.mp]="sg_uc",gi[N.m.Uh]=null,gi[N.m.Ic]=null,gi[N.m.xb]=null,gi[N.m.ya]=null,gi[N.m.za]=null,gi[N.m.Ya]=
null,gi[N.m.li]=null,gi[N.m.vd]=null,gi[N.m.Oe]=null,gi[N.m.Pe]=null,gi[N.m.Dc]=null,gi);function ii(a,b){if(a){var c=a.split("x");c.length===2&&(ji(b,"u_w",c[0]),ji(b,"u_h",c[1]))}}
function ki(a){var b=li;b=b===void 0?mi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(ni(q.value)),r.push(ni(q.quantity)),r.push(ni(q.item_id)),r.push(ni(q.start_date)),r.push(ni(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function mi(a){return oi(a.item_id,a.id,a.item_name)}function oi(){for(var a=l(Fa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function pi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ji(a,b,c){c===void 0||c===null||c===""&&!Fg[b]||(a[b]=c)}function ni(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var qi={},ri=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=wb(0,1)===0,b=wb(0,1)===0,c++,c>30)return;return a},ti={ar:si};function si(a,b){var c=qi[b];if(!(wb(0,9999)<c.probability*(c.controlId2?4:2)*1E4))return a;var d=c.studyId,e=c.experimentId,f=c.controlId,g=c.controlId2;if(!((a.exp||{})[e]||(a.exp||{})[f]||g&&(a.exp||{})[g])){var h=ri()?0:1;g&&(h|=(ri()?0:1)<<1);h===0?ui(a,e,d):h===1?ui(a,f,d):h===2&&ui(a,g,d)}return a}
function vi(a,b){return qi[b]?!!qi[b].active||qi[b].probability>.5||!!(a.exp||{})[qi[b].experimentId]:!1}function wi(a,b){for(var c=a.exp||{},d=l(Object.keys(c).map(Number)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c[f]===b)return f}}function ui(a,b,c){var d=a.exp||{};d[b]=c;a.exp=d};var O={N:{Vj:"call_conversion",Rd:"ccm_conversion",xa:"conversion",Mo:"floodlight",Hf:"ga_conversion",wd:"gcp_remarketing",Ei:"landing_page",Oa:"page_view",ve:"fpm_test_hit",Hb:"remarketing",Vb:"user_data_lead",zb:"user_data_web"}};function Ai(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var Bi=[],Ci;function Di(a,b){var c=Ei(a,!1);return c!==b?(Ci?Ci(a):Bi.push(a),b):c}function Ei(a,b){b=b===void 0?!1:b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?!!data.blob[a]:b}function Fi(a){var b;b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}function Gi(){var a=Hi.M,b=Ii(54);return b===a||isNaN(b)&&isNaN(a)?b:(Ci?Ci(54):Bi.push(54),a)}
function Ii(a){var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(a))?Number(data.blob[a]):0}function Ji(a,b){b=b===void 0?"":b;var c=Ki(46);return c&&(c==null?0:c.hasOwnProperty(a))?String(c[a]):b}function Ki(a){var b,c;return(b=data)==null?void 0:(c=b.blob)==null?void 0:c[a]}function Li(){var a=Mi;Ci=a;for(var b=l(Bi),c=b.next();!c.done;c=b.next())a(c.value);Bi.length=0};var Ni=function(){this.C=new Set;this.H=new Set},Oi=function(a){var b=Hi.U;a=a===void 0?[]:a;var c=[].concat(Ba(b.C)).concat([].concat(Ba(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Pi=function(){var a=[].concat(Ba(Hi.U.C));a.sort(function(b,c){return b-c});return a},Qi=function(){var a=Hi.U,b=Fi(44);a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Ri={},Si={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Ti={__paused:1,__tg:1},Ui;for(Ui in Si)Si.hasOwnProperty(Ui)&&(Ti[Ui]=1);var Vi=!1;function Wi(){var a=!1;a=!0;return a}var Xi=G(218)?Di(45,Wi()):Wi(),Yi,Zi=!1;Yi=Zi;var $i=null,aj=null,bj={},cj={},dj="";Ri.Oi=dj;var Hi=new function(){this.U=new Ni;this.H=this.C=!1;this.M=0;this.ma=this.Ja=this.Za="";this.ja=this.R=!1};function ej(){var a=Fi(18),b=a.length;return a[b-1]==="/"?a.substring(0,b-1):a}function fj(){return Hi.H?G(84)?Hi.M===0:Hi.M!==1:!1}function gj(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var hj=/:[0-9]+$/,ij=/^\d+\.fls\.doubleclick\.net$/;function jj(a,b,c,d){var e=kj(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function kj(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=Aa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function lj(a){try{return decodeURIComponent(a)}catch(b){}}function mj(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=nj(a.protocol)||nj(w.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:w.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||w.location.hostname).replace(hj,"").toLowerCase());return oj(a,b,c,d,e)}
function oj(a,b,c,d,e){var f,g=nj(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=pj(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(hj,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||kb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=jj(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function nj(a){return a?a.replace(":","").toLowerCase():""}function pj(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var qj={},rj=0;
function sj(a){var b=qj[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||kb("TAGGING",1),d="/"+d);var e=c.hostname.replace(hj,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};rj<5&&(qj[a]=b,rj++)}return b}function tj(a,b,c){var d=sj(a);return Sb(b,d,c)}
function uj(a){var b=sj(w.location.href),c=mj(b,"host",!1);if(c&&c.match(ij)){var d=mj(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var vj=/gtag[.\/]js/,wj=/gtm[.\/]js/,xj=!1;function yj(a){if(xj)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(vj.test(c))return"3";if(wj.test(c))return"2"}return"0"};function P(a){kb("GTM",a)};function zj(a,b){var c=Aj();c.pending||(c.pending=[]);vb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Bj(){var a=w.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Cj=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Bj()};function Aj(){var a=Dc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Cj,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Bj());return c};function Dj(){return Ei(7)&&Ej().some(function(a){return a===Fi(5)})}function Fj(){return Fi(6)||"_"+Fi(5)}function Gj(){var a=Fi(10);return a?a.split("|"):[Fi(5)]}function Ej(){var a=Fi(9);return a?a.split("|").filter(function(b){return b.indexOf("GTM-")!==0}):[]}function Hj(a){return Array.isArray(a)?a[0]:a}function Ij(){var a=Jj(Kj()),b=a&&a.parent;if(b)return Jj(b)}function Lj(){var a=Jj(Kj());if(a){for(;a.parent;){var b=Jj(a.parent);if(!b)break;a=b}return a}}
function Jj(a){var b=Aj();return a.isDestination?Hj(b.destination[a.ctid]):b.container[a.ctid]}function Mj(){var a=Aj();if(a.pending){for(var b,c=[],d=!1,e=Gj(),f=Ej(),g={},h=0;h<a.pending.length;g={ng:void 0},h++)g.ng=a.pending[h],vb(g.ng.target.isDestination?f:e,function(m){return function(n){return n===m.ng.target.ctid}}(g))?d||(b=g.ng.onLoad,d=!0):c.push(g.ng);a.pending=c;if(b)try{b(Fj())}catch(m){}}}
function Nj(){for(var a=Fi(5),b=Gj(),c=Ej(),d=function(n,p){var q={canonicalContainerId:Fi(6),scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};Bc&&(q.scriptElement=Bc);Cc&&(q.scriptSource=Cc);if(Ij()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var u;b:{var t,v=(t=q.scriptElement)==null?void 0:t.src;if(v){for(var x=Hi.H,y=sj(v),A=x?y.pathname:""+y.hostname+y.pathname,D=z.scripts,E="",J=0;J<D.length;++J){var F=D[J];if(!(F.innerHTML.length===0||!x&&F.innerHTML.indexOf(q.scriptContainerId||
"SHOULD_NOT_BE_SET")<0||F.innerHTML.indexOf(A)<0)){if(F.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){u=String(J);break b}E=String(J)}}if(E){u=E;break b}}u=void 0}var M=u;if(M){xj=!0;r=M;break a}}var U=[].slice.call(z.scripts);r=q.scriptElement?String(U.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=yj(q)}var ha=p?e.destination:e.container,S=ha[n];S?Array.isArray(S)||(p&&S.state===0&&P(93),oa(Object,"assign").call(Object,S,q)):ha[n]=q},e=Aj(),f=l(b),g=f.next();!g.done;g=f.next())d(g.value,
!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Fj()]={};Mj()}function Oj(){var a=Fj();return!!Aj().canonical[a]}function Pj(a){return!!Aj().container[a]}function Qj(a){var b=Aj().destination[a],c=Hj(b);return c?c.state!==0:!1}function Kj(){return{ctid:Fi(5),isDestination:Ei(7)}}function Rj(a,b,c){var d=Kj(),e=Aj().container[a];e&&e.state!==3||(Aj().container[a]={state:1,context:b,parent:d},zj({ctid:a,isDestination:!1},c))}
function Sj(){var a=Aj().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Tj(){var a={};zb(Aj().destination,function(b,c){var d=Hj(c);(d==null?void 0:d.state)===0&&(a[b]=d)});return a}function Uj(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Vj(){for(var a=Aj(),b=l(Gj()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};function Wj(a){a=a===void 0?[]:a;return Oi(a).join("~")}function Xj(){if(!G(118))return"";var a,b;return(((a=Jj(Kj()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var Yj={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Zj=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function ak(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return sj(""+c+b).href}}function bk(a,b){if(fj()||Hi.C)return ak(a,b)}
function ck(){return!!Ri.Oi&&Ri.Oi.split("@@").join("")!=="SGTM_TOKEN"}function dk(a){for(var b=l([N.m.rd,N.m.rc]),c=b.next();!c.done;c=b.next()){var d=Q(a,c.value);if(d)return d}}function ek(a,b,c){c=c===void 0?"":c;if(!fj())return a;var d=b?Yj[a]||"":"";d==="/gs"&&(c="");return""+ej()+d+c}function fk(a){if(!fj())return a;for(var b=l(Zj),c=b.next();!c.done;c=b.next()){var d=c.value;if(Lb(a,""+ej()+d))return a+"&_uip="+encodeURIComponent("::")}return a};function gk(a){var b=String(a[nf.Ra]||"").replace(/_/g,"");return Lb(b,"cvt")?"cvt":b}var hk=w.location.search.indexOf("?gtm_latency=")>=0||w.location.search.indexOf("&gtm_latency=")>=0;var ik=Math.random(),jk,kk=Ii(27);jk=hk||ik<kk;var lk,mk=Ii(42);lk=hk||ik>=1-mk;function nk(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var ok=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};var pk,qk;a:{for(var rk=["CLOSURE_FLAGS"],sk=Ga,tk=0;tk<rk.length;tk++)if(sk=sk[rk[tk]],sk==null){qk=null;break a}qk=sk}var uk=qk&&qk[610401301];pk=uk!=null?uk:!1;function vk(){var a=Ga.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var wk,xk=Ga.navigator;wk=xk?xk.userAgentData||null:null;function yk(a){if(!pk||!wk)return!1;for(var b=0;b<wk.brands.length;b++){var c=wk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function zk(a){return vk().indexOf(a)!=-1};function Ak(){return pk?!!wk&&wk.brands.length>0:!1}function Bk(){return Ak()?!1:zk("Opera")}function Ck(){return zk("Firefox")||zk("FxiOS")}function Dk(){return Ak()?yk("Chromium"):(zk("Chrome")||zk("CriOS"))&&!(Ak()?0:zk("Edge"))||zk("Silk")};function Ek(){return pk?!!wk&&!!wk.platform:!1}function Fk(){return zk("iPhone")&&!zk("iPod")&&!zk("iPad")}function Gk(){Fk()||zk("iPad")||zk("iPod")};var Hk=function(a){Hk[" "](a);return a};Hk[" "]=function(){};Bk();Ak()||zk("Trident")||zk("MSIE");zk("Edge");!zk("Gecko")||vk().toLowerCase().indexOf("webkit")!=-1&&!zk("Edge")||zk("Trident")||zk("MSIE")||zk("Edge");vk().toLowerCase().indexOf("webkit")!=-1&&!zk("Edge")&&zk("Mobile");Ek()||zk("Macintosh");Ek()||zk("Windows");(Ek()?wk.platform==="Linux":zk("Linux"))||Ek()||zk("CrOS");Ek()||zk("Android");Fk();zk("iPad");zk("iPod");Gk();vk().toLowerCase().indexOf("kaios");Ck();Fk()||zk("iPod");zk("iPad");!zk("Android")||Dk()||Ck()||Bk()||zk("Silk");Dk();!zk("Safari")||Dk()||(Ak()?0:zk("Coast"))||Bk()||(Ak()?0:zk("Edge"))||(Ak()?yk("Microsoft Edge"):zk("Edg/"))||(Ak()?yk("Opera"):zk("OPR"))||Ck()||zk("Silk")||zk("Android")||Gk();var Ik={},Jk=null,Kk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Jk){Jk={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Ik[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Jk[q]===void 0&&(Jk[q]=p)}}}for(var r=Ik[f],u=Array(Math.floor(b.length/3)),t=r[64]||"",v=0,x=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],D=b[v+2],E=r[y>>2],J=r[(y&3)<<4|A>>4],F=r[(A&15)<<2|D>>6],M=r[D&63];u[x++]=""+E+J+F+M}var U=0,ha=t;switch(b.length-v){case 2:U=b[v+1],ha=r[(U&15)<<2]||t;case 1:var S=b[v];u[x]=""+r[S>>2]+r[(S&3)<<4|U>>4]+ha+t}return u.join("")};var Lk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var Mk=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Nk=/#|$/,Ok=function(a,b){var c=a.search(Nk),d=Mk(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Lk(a.slice(d,e!==-1?e:0))},Pk=/[?&]($|#)/,Qk=function(a,b,c){for(var d,e=a.search(Nk),f=0,g,h=[];(g=Mk(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Pk,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var u=d.indexOf("?"),t;u<0||u>r?(u=r,t=""):t=d.substring(u+1,r);q=[d.slice(0,u),t,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Rk(a,b,c,d,e,f,g){var h=Ok(c,"fmt");if(d){var m=Ok(c,"random"),n=Ok(c,"label")||"";if(!m)return!1;var p=Kk(Lk(n)+":"+Lk(m));if(!nk(a,p,d))return!1}h&&Number(h)!==4&&(c=Qk(c,"rfmt",h));var q=Qk(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||Sk(g);Lc(q,function(){g==null||Tk(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||Tk(g);e==null||e()},f,r||void 0);return!0};var Uk={},Vk=(Uk[1]={},Uk[2]={},Uk[3]={},Uk[4]={},Uk);function Wk(a,b,c){var d=Xk(b,c);if(d){var e=Vk[b][d];e||(e=Vk[b][d]=[]);e.push(oa(Object,"assign").call(Object,{},a))}}function Yk(a,b){var c=Xk(a,b);if(c){var d=Vk[a][c];d&&(Vk[a][c]=d.filter(function(e){return!e.mn}))}}function Zk(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Xk(a,b){var c=b;if(b[0]==="/"){var d;c=((d=w.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function $k(a){var b=Fa.apply(1,arguments);lk&&(Wk(a,2,b[0]),Wk(a,3,b[0]));Xc.apply(null,Ba(b))}function al(a){var b=Fa.apply(1,arguments);lk&&Wk(a,2,b[0]);return Yc.apply(null,Ba(b))}function bl(a){var b=Fa.apply(1,arguments);lk&&Wk(a,3,b[0]);Oc.apply(null,Ba(b))}
function cl(a){var b=Fa.apply(1,arguments),c=b[0];lk&&(Wk(a,2,c),Wk(a,3,c));return $c.apply(null,Ba(b))}function dl(a){var b=Fa.apply(1,arguments);lk&&Wk(a,1,b[0]);Lc.apply(null,Ba(b))}function el(a){var b=Fa.apply(1,arguments);b[0]&&lk&&Wk(a,4,b[0]);Nc.apply(null,Ba(b))}function fl(a){var b=Fa.apply(1,arguments);lk&&Wk(a,1,b[2]);return Rk.apply(null,Ba(b))};var gl={Ka:{qe:0,ue:1,Hi:2}};gl.Ka[gl.Ka.qe]="FULL_TRANSMISSION";gl.Ka[gl.Ka.ue]="LIMITED_TRANSMISSION";gl.Ka[gl.Ka.Hi]="NO_TRANSMISSION";var hl={Z:{Gb:0,Ga:1,xc:2,Jc:3}};hl.Z[hl.Z.Gb]="NO_QUEUE";hl.Z[hl.Z.Ga]="ADS";hl.Z[hl.Z.xc]="ANALYTICS";hl.Z[hl.Z.Jc]="MONITORING";function il(){var a=Dc("google_tag_data",{});return a.ics=a.ics||new jl}var jl=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
jl.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;kb("TAGGING",19);b==null?kb("TAGGING",18):kl(this,a,b==="granted",c,d,e,f,g)};jl.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)kl(this,a[d],void 0,void 0,"","",b,c)};
var kl=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&sb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),u={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=u;r&&w.setTimeout(function(){m[b]===u&&u.quiet&&(kb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=jl.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())ll(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())ll(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&sb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Gd:b})};var ll=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.gn=!0)}};jl.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.gn){d.gn=!1;try{d.Gd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var ml=!1,nl=!1,ol={},pl={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(ol.ad_storage=1,ol.analytics_storage=1,ol.ad_user_data=1,ol.ad_personalization=1,ol),usedContainerScopedDefaults:!1};function ql(a){var b=il();b.accessedAny=!0;return(sb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,pl)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function rl(a){var b=il();b.accessedAny=!0;return b.getConsentState(a,pl)}function sl(a){var b=il();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function tl(){if(!Xa(7))return!1;var a=il();a.accessedAny=!0;if(a.active)return!0;if(!pl.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(pl.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(pl.containerScopedDefaults[c.value]!==1)return!0;return!1}function ul(a,b){il().addListener(a,b)}
function vl(a,b){il().notifyListeners(a,b)}function wl(a,b){function c(){for(var e=0;e<b.length;e++)if(!sl(b[e]))return!0;return!1}if(c()){var d=!1;ul(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function xl(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];ql(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=sb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),ul(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):w.setTimeout(function(){m(c())},500)}}))};var yl={},zl=(yl[hl.Z.Gb]=gl.Ka.qe,yl[hl.Z.Ga]=gl.Ka.qe,yl[hl.Z.xc]=gl.Ka.qe,yl[hl.Z.Jc]=gl.Ka.qe,yl),Al=function(a,b){this.C=a;this.consentTypes=b};Al.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return ql(a)});case 1:return this.consentTypes.some(function(a){return ql(a)});default:rc(this.C,"consentsRequired had an unknown type")}};
var Bl={},Cl=(Bl[hl.Z.Gb]=new Al(0,[]),Bl[hl.Z.Ga]=new Al(0,["ad_storage"]),Bl[hl.Z.xc]=new Al(0,["analytics_storage"]),Bl[hl.Z.Jc]=new Al(1,["ad_storage","analytics_storage"]),Bl);var El=function(a){var b=this;this.type=a;this.C=[];ul(Cl[a].consentTypes,function(){Dl(b)||b.flush()})};El.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Dl=function(a){return zl[a.type]===gl.Ka.Hi&&!Cl[a.type].isConsentGranted()},Fl=function(a,b){Dl(a)?a.C.push(b):b()},Gl=new Map;function Hl(a){Gl.has(a)||Gl.set(a,new El(a));return Gl.get(a)};var Il={X:{xn:"aw_user_data_cache",Mh:"cookie_deprecation_label",zg:"diagnostics_page_id",ni:"eab",No:"fl_user_data_cache",Ro:"ga4_user_data_cache",se:"ip_geo_data_cache",xi:"ip_geo_fetch_in_progress",Fi:"local_cookie_cache_map",dm:"nb_data",Ii:"page_experiment_ids",we:"pt_data",gm:"pt_listener_set",lm:"service_worker_endpoint",rm:"shared_user_id",sm:"shared_user_id_requested",kh:"shared_user_id_source"}};var Jl=function(a){return ff(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(Il.X);
function Kl(a,b){b=b===void 0?!1:b;if(Jl(a)){var c,d,e=(d=(c=Dc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Ll(a,b){var c=Kl(a,!0);c&&c.set(b)}function Ml(a){var b;return(b=Kl(a))==null?void 0:b.get()}function Nl(a,b){var c=Kl(a);if(!c){c=Kl(a,!0);if(!c)return;c.set(b)}return c.get()}function Ol(a,b){if(typeof b==="function"){var c;return(c=Kl(a,!0))==null?void 0:c.subscribe(b)}}function Pl(a,b){var c=Kl(a);return c?c.unsubscribe(b):!1};var Ql={},Rl=(Ql.tdp=1,Ql.exp=1,Ql.pid=1,Ql.dl=1,Ql.seq=1,Ql.t=1,Ql.v=1,Ql),Sl=["mcc"],Tl={},Ul={},Vl=!1;function Wl(a,b,c){Ul[a]=b;(c===void 0||c)&&Xl(a)}function Xl(a,b){Tl[a]!==void 0&&(b===void 0||!b)||Lb(Fi(5),"GTM-")&&a==="mcc"||(Tl[a]=!0)}
function Yl(a){a=a===void 0?!1:a;var b=Object.keys(Tl).filter(function(f){return Tl[f]===!0&&Ul[f]!==void 0&&(a||!Sl.includes(f))});Zl(b);var c=b.map(function(f){var g=Ul[f];typeof g==="function"&&(g=g());return g?"&"+f+"="+g:""}).join(""),d="https://"+Fi(21),e="/td?id="+Fi(5);return""+ek(d)+e+(""+c+"&z=0")}function Zl(a){a.forEach(function(b){Rl[b]||(Tl[b]=!1)})}
function $l(a){a=a===void 0?!1:a;if(Hi.ja&&lk&&Fi(5)){var b=Hl(hl.Z.Jc);if(Dl(b))Vl||(Vl=!0,Fl(b,$l));else{var c=Yl(a),d={destinationId:Fi(5),endpoint:61};a?cl(d,c,void 0,{Ch:!0},void 0,function(){bl(d,c+"&img=1")}):bl(d,c);Vl=!1}}}function am(){Object.keys(Tl).filter(function(a){return Tl[a]&&!Rl[a]}).length>0&&$l(!0)}var bm;function cm(){if(Ml(Il.X.zg)===void 0){var a=function(){Ll(Il.X.zg,wb());bm=0};a();w.setInterval(a,864E5)}else Ol(Il.X.zg,function(){bm=0});bm=0}
function dm(){cm();Wl("v","3");Wl("t","t");Wl("pid",function(){return String(Ml(Il.X.zg))});Wl("seq",function(){return String(++bm)});Wl("exp",Wj());Qc(w,"pagehide",am)};var em=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],fm=[N.m.rd,N.m.rc,N.m.fe,N.m.Qb,N.m.Tb,N.m.Na,N.m.Xa,N.m.Wa,N.m.ub,N.m.Rb],gm=!1,hm=!1,im={},jm={};function km(){!hm&&gm&&(em.some(function(a){return pl.containerScopedDefaults[a]!==1})||lm("mbc"));hm=!0}function lm(a){lk&&(Wl(a,"1"),$l())}function mm(a,b){if(!im[b]&&(im[b]=!0,jm[b]))for(var c=l(fm),d=c.next();!d.done;d=c.next())if(Q(a,d.value)){lm("erc");break}};function nm(a){kb("HEALTH",a)};var om={},pm=!1;function qm(){function a(){c!==void 0&&Pl(Il.X.se,c);try{var e=Ml(Il.X.se);om=JSON.parse(e)}catch(f){P(123),nm(2),om={}}pm=!0;b()}var b=rm,c=void 0,d=Ml(Il.X.se);d?a(d):(c=Ol(Il.X.se,a),sm())}
function sm(){function a(b){Ll(Il.X.se,b||"{}");Ll(Il.X.xi,!1)}if(!Ml(Il.X.xi)){Ll(Il.X.xi,!0);try{w.fetch("https://www.google.com/ccm/geo",{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(b){b.ok?b.text().then(function(c){a(c)},function(){a()}):a()},function(){a()})}catch(b){a()}}}function tm(){var a=Fi(22);try{return JSON.parse(ib(a))}catch(b){return P(123),nm(2),{}}}function um(){return om["0"]||""}function vm(){return om["1"]||""}
function wm(){var a=!1;a=!!om["2"];return a}function xm(){return om["6"]!==!1}function ym(){var a="";a=om["4"]||"";return a}function zm(){var a="";a=om["3"]||"";return a};var Am={},Bm=Object.freeze((Am[N.m.Ob]=1,Am[N.m.Bg]=1,Am[N.m.Qh]=1,Am[N.m.Pb]=1,Am[N.m.ya]=1,Am[N.m.ub]=1,Am[N.m.wb]=1,Am[N.m.Bb]=1,Am[N.m.ed]=1,Am[N.m.Rb]=1,Am[N.m.Wa]=1,Am[N.m.zc]=1,Am[N.m.cf]=1,Am[N.m.Ea]=1,Am[N.m.Dk]=1,Am[N.m.ff]=1,Am[N.m.Lg]=1,Am[N.m.Mg]=1,Am[N.m.fe]=1,Am[N.m.Ok]=1,Am[N.m.Dc]=1,Am[N.m.ke]=1,Am[N.m.Rk]=1,Am[N.m.Pg]=1,Am[N.m.fi]=1,Am[N.m.Fc]=1,Am[N.m.Gc]=1,Am[N.m.Xa]=1,Am[N.m.hi]=1,Am[N.m.Sb]=1,Am[N.m.od]=1,Am[N.m.pd]=1,Am[N.m.rd]=1,Am[N.m.vf]=1,Am[N.m.ji]=1,Am[N.m.ne]=1,Am[N.m.rc]=
1,Am[N.m.sd]=1,Am[N.m.rl]=1,Am[N.m.Ub]=1,Am[N.m.vd]=1,Am[N.m.Ni]=1,Am));Object.freeze([N.m.za,N.m.Ya,N.m.Cb,N.m.xb,N.m.ii,N.m.Na,N.m.di,N.m.no]);
var Cm={},Dm=Object.freeze((Cm[N.m.Rn]=1,Cm[N.m.Sn]=1,Cm[N.m.Tn]=1,Cm[N.m.Un]=1,Cm[N.m.Vn]=1,Cm[N.m.Yn]=1,Cm[N.m.Zn]=1,Cm[N.m.ao]=1,Cm[N.m.co]=1,Cm[N.m.Yd]=1,Cm)),Em={},Fm=Object.freeze((Em[N.m.tk]=1,Em[N.m.uk]=1,Em[N.m.Ud]=1,Em[N.m.Vd]=1,Em[N.m.vk]=1,Em[N.m.Xc]=1,Em[N.m.Wd]=1,Em[N.m.jc]=1,Em[N.m.yc]=1,Em[N.m.kc]=1,Em[N.m.rb]=1,Em[N.m.Xd]=1,Em[N.m.mc]=1,Em[N.m.wk]=1,Em)),Gm=Object.freeze([N.m.Ob,N.m.Te,N.m.Pb,N.m.zc,N.m.fe,N.m.qf,N.m.od,N.m.sd]),Hm=Object.freeze([].concat(Ba(Gm))),Im=Object.freeze([N.m.wb,
N.m.Mg,N.m.vf,N.m.ji,N.m.Jg]),Jm=Object.freeze([].concat(Ba(Im))),Km={},Lm=(Km[N.m.aa]="1",Km[N.m.ka]="2",Km[N.m.W]="3",Km[N.m.Ma]="4",Km),Mm={},Nm=Object.freeze((Mm.search="s",Mm.youtube="y",Mm.playstore="p",Mm.shopping="h",Mm.ads="a",Mm.maps="m",Mm));function Om(a){return typeof a!=="object"||a===null?{}:a}function Pm(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Qm(a){if(a!==void 0&&a!==null)return Pm(a)}function Rm(a){return typeof a==="number"?a:Qm(a)};function Sm(a){return a&&a.indexOf("pending:")===0?Tm(a.substr(8)):!1}function Tm(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Gb();return b<c+3E5&&b>c-9E5};var Um=!1,Vm=!1,Wm=!1,Xm=0,Ym=!1,Zm=[];function $m(a){if(Xm===0)Ym&&Zm&&(Zm.length>=100&&Zm.shift(),Zm.push(a));else if(an()){var b=Fi(41),c=Dc(b,[]);c.length>=50&&c.shift();c.push(a)}}function bn(){cn();Rc(z,"TAProdDebugSignal",bn)}function cn(){if(!Vm){Vm=!0;dn();var a=Zm;Zm=void 0;a==null||a.forEach(function(b){$m(b)})}}
function dn(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Tm(a)?Xm=1:!Sm(a)||Um||Wm?Xm=2:(Wm=!0,Qc(z,"TAProdDebugSignal",bn,!1),w.setTimeout(function(){cn();Um=!0},200))}function an(){if(!Ym)return!1;switch(Xm){case 1:case 0:return!0;case 2:return!1;default:return!1}};var en=!1;function fn(a,b){var c=Gj(),d=Ej();if(an()){var e=gn("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;$m(e)}}
function hn(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.ib;e=a.isBatched;var f;if(f=an()){var g;a:switch(c.endpoint){case 19:case 47:g=!0;break a;default:g=!1}f=!g}if(f){var h=gn("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);$m(h)}}function jn(a){an()&&hn(a())}
function gn(a,b){b=b===void 0?{}:b;b.groupId=kn;var c,d=b,e={publicId:ln};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=en?"OGT":"GTM";c.key.targetRef=mn;return c}var ln="",mn={ctid:"",isDestination:!1},kn;
function nn(a){var b=Fi(5),c=Dj(),d=Fi(6);Xm=0;Ym=!0;dn();kn=a;ln=b;en=Xi;mn={ctid:b,isDestination:c,canonicalId:d}};var on=[N.m.aa,N.m.ka,N.m.W,N.m.Ma],pn,qn;function rn(a){var b=a[N.m.hc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)zb(a,function(d){return function(e,f){if(e!==N.m.hc){var g=Pm(f),h=b[d.cg],m=um(),n=vm();nl=!0;ml&&kb("TAGGING",20);il().declare(e,g,h,m,n)}}}(c))}
function sn(a){km();!qn&&pn&&lm("crc");qn=!0;var b=a[N.m.ug];b&&P(41);var c=a[N.m.hc];c?P(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)zb(a,function(e){return function(f,g){if(f!==N.m.hc&&f!==N.m.ug){var h=Qm(g),m=c[e.dg],n=Number(b),p=um(),q=vm();n=n===void 0?0:n;ml=!0;nl&&kb("TAGGING",20);il().default(f,h,m,p,q,n,pl)}}}(d))}
function tn(a){pl.usedContainerScopedDefaults=!0;var b=a[N.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(vm())&&!c.includes(um()))return}zb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}pl.usedContainerScopedDefaults=!0;pl.containerScopedDefaults[d]=e==="granted"?3:2})}
function un(a,b){km();pn=!0;zb(a,function(c,d){var e=Pm(d);ml=!0;nl&&kb("TAGGING",20);il().update(c,e,pl)});vl(b.eventId,b.priorityId)}function vn(a){a.hasOwnProperty("all")&&(pl.selectedAllCorePlatformServices=!0,zb(Nm,function(b){pl.corePlatformServices[b]=a.all==="granted";pl.usedCorePlatformServices=!0}));zb(a,function(b,c){b!=="all"&&(pl.corePlatformServices[b]=c==="granted",pl.usedCorePlatformServices=!0)})}function wn(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return ql(b)})}
function xn(a,b){ul(a,b)}function Jn(a,b){xl(a,b)}function Kn(a,b){wl(a,b)}function Ln(){var a=[N.m.aa,N.m.Ma,N.m.W];il().waitForUpdate(a,500,pl)}function Mn(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;il().clearTimeout(d,void 0,pl)}vl()}function Nn(){if(!Yi)for(var a=xm()?gj(Hi.Ja):gj(Hi.Za),b=0;b<on.length;b++){var c=on[b],d=c,e=a[c]?"granted":"denied";il().implicit(d,e)}};var On=!1;G(218)&&(On=Di(49,On));var Pn=!1,Qn=[];function Rn(){if(!Pn){Pn=!0;for(var a=Qn.length-1;a>=0;a--)Qn[a]();Qn=[]}};var Sn=w.google_tag_manager=w.google_tag_manager||{};function Tn(a,b){return Sn[a]=Sn[a]||b()}function Un(){var a=Fi(5),b=Vn;Sn[a]=Sn[a]||b}function Wn(){var a=Fi(19);return Sn[a]=Sn[a]||{}}function Xn(){var a=Fi(19);return Sn[a]}function Yn(){var a=Sn.sequence||1;Sn.sequence=a+1;return a}w.google_tag_data=w.google_tag_data||{};function Zn(){if(Sn.pscdl!==void 0)Ml(Il.X.Mh)===void 0&&Ll(Il.X.Mh,Sn.pscdl);else{var a=function(c){Sn.pscdl=c;Ll(Il.X.Mh,c)},b=function(){a("error")};try{zc.cookieDeprecationLabel?(a("pending"),zc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var $n=0;function ao(a){lk&&a===void 0&&$n===0&&(Wl("mcc","1"),$n=1)};function bo(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var ja=!1;ja=!0;return ja}();a.push({qa:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,la:0});var e=
Number('')||0,f=Number('0.01')||0;f||(f=e/100);var g=function(){var ja=!1;return ja}();a.push({qa:265,studyId:265,experimentId:115691063,controlId:115691064,controlId2:115691065,
probability:f,active:g,la:0});var h=Number('')||0,m=Number('')||0;m||(m=h/100);var n=function(){var ja=!1;return ja}();a.push({qa:228,studyId:228,experimentId:105177154,controlId:105177155,controlId2:105255245,probability:m,active:n,la:0});var p=Number('')||
0,q=Number('')||0;q||(q=p/100);var r=function(){var ja=!1;return ja}();a.push({qa:244,studyId:244,experimentId:115596674,controlId:115596673,controlId2:0,probability:q,active:r,la:0});var u=Number('')||0,
t=Number('')||0;t||(t=u/100);var v=function(){var ja=!1;return ja}();a.push({qa:256,studyId:256,experimentId:115495938,controlId:115495939,controlId2:115495940,probability:t,active:v,la:0});var x=Number('')||
0,y=Number('')||0;y||(y=x/100);var A=function(){var ja=!1;return ja}();a.push({qa:257,studyId:257,experimentId:115495941,controlId:115495942,controlId2:115495943,probability:y,
active:A,la:0});var D=Number('')||0,E=Number('')||0;E||(E=D/100);var J=function(){var ja=!1;ja=!0;return ja}();a.push({qa:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:E,active:J,la:0});var F=Number('')||0,M=Number('1')||0;M||(M=F/100);var U=function(){var ja=!1;
return ja}();a.push({qa:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:M,active:U,la:0});var ha=Number('')||0,S=Number('0.001')||0;S||(S=ha/100);var aa=function(){var ja=!1;
return ja}();a.push({qa:255,studyId:255,experimentId:105391252,controlId:105391253,controlId2:105446120,probability:S,active:aa,la:0});var ta=Number('')||0,la=Number('')||0;la||(la=ta/100);var ea=function(){var ja=!1;return ja}();a.push({qa:235,studyId:235,experimentId:105357150,controlId:105357151,
controlId2:0,probability:la,active:ea,la:1});var Z=Number('')||0,ka=Number('')||0;ka||(ka=Z/100);var za=function(){var ja=!1;return ja}();a.push({qa:264,studyId:264,experimentId:115752876,controlId:115752874,controlId2:115752875,probability:ka,active:za,la:0});var ua=Number('')||
0,Va=Number('0.5')||0;Va||(Va=ua/100);var $a=function(){var ja=!1;return ja}();a.push({qa:203,studyId:203,experimentId:115480710,controlId:115480709,controlId2:115489982,probability:Va,active:$a,la:0});var cc=Number('')||0,Wb=Number('')||
0;Wb||(Wb=cc/100);var rb=function(){var ja=!1;ja=!0;return ja}();a.push({qa:197,studyId:197,experimentId:105113532,controlId:105113531,controlId2:0,probability:Wb,active:rb,la:0});var ad=Number('')||0,bd=Number('0.2')||0;bd||(bd=ad/100);var Oh=function(){var ja=!1;return ja}();a.push({qa:243,studyId:243,experimentId:115616985,controlId:115616986,controlId2:0,probability:bd,active:Oh,la:0});var ZG=Number('')||0,yn=Number('')||0;yn||(yn=ZG/100);var $G=function(){var ja=!1;
return ja}();a.push({qa:171,studyId:171,experimentId:104967143,controlId:104967140,controlId2:0,probability:yn,active:$G,la:0});var aH=Number('')||0,zn=Number('0')||0;zn||(zn=aH/100);var bH=function(){var ja=!1;return ja}();
a.push({qa:254,studyId:254,experimentId:115583767,controlId:115583768,controlId2:115583769,probability:zn,active:bH,la:0});var cH=Number('')||0,An=Number('')||0;An||(An=cH/100);var dH=function(){var ja=!1;
return ja}();a.push({qa:253,studyId:253,experimentId:115583770,controlId:115583771,controlId2:115583772,probability:An,active:dH,la:0});var eH=Number('')||0,Bn=Number('')||0;Bn||(Bn=eH/100);var fH=function(){var ja=!1;
return ja}();a.push({qa:266,studyId:266,experimentId:115718529,controlId:115718530,controlId2:115718531,probability:Bn,active:fH,la:0});var gH=Number('')||0,Cn=Number('')||0;Cn||(Cn=gH/100);var hH=function(){var ja=!1;
return ja}();a.push({qa:267,studyId:267,experimentId:115718526,controlId:115718527,controlId2:115718528,probability:Cn,active:hH,la:0});var iH=Number('')||0,Dn=Number('0.001')||0;Dn||(Dn=iH/100);var jH=function(){var ja=!1;
return ja}();a.push({qa:259,studyId:259,experimentId:105322302,controlId:105322303,controlId2:105322304,probability:Dn,active:jH,la:0});var kH=Number('')||0,En=Number('')||0;En||(En=kH/100);var lH=function(){var ja=!1;return ja}();a.push({qa:249,studyId:249,experimentId:105440521,controlId:105440522,
controlId2:0,focused:!0,probability:En,active:lH,la:0});var mH=Number('')||0,Fn=Number('0.5')||0;Fn||(Fn=mH/100);var nH=function(){var ja=!1;return ja}();a.push({qa:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:Fn,active:nH,la:1});var oH=Number('')||
0,Gn=Number('0.5')||0;Gn||(Gn=oH/100);var pH=function(){var ja=!1;return ja}();a.push({qa:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:Gn,active:pH,la:0});var qH=Number('')||0,Hn=Number('0.1')||
0;Hn||(Hn=qH/100);var rH=function(){var ja=!1;return ja}();a.push({qa:229,studyId:229,experimentId:105359938,controlId:105359937,controlId2:105359936,probability:Hn,active:rH,la:0});var sH=Number('')||0,In=Number('')||
0;In||(In=sH/100);var tH=function(){var ja=!1;return ja}();a.push({qa:225,studyId:225,experimentId:105476338,controlId:105476339,controlId2:105476599,probability:In,active:tH,la:0});return a};var R={A:{Hh:"accept_by_default",tg:"add_tag_timing",Ih:"ads_event_page_view",Vc:"allow_ad_personalization",Tj:"batch_on_navigation",Xj:"client_id_source",Ke:"consent_event_id",Le:"consent_priority_id",Dr:"consent_state",ia:"consent_updated",Sd:"conversion_linker_enabled",Da:"cookie_options",wg:"create_dc_join",xg:"create_fpm_geo_join",yg:"create_fpm_signals_join",Td:"create_google_join",Oh:"dc_random",Ne:"em_event",Gr:"endpoint_for_debug",sk:"enhanced_client_id_source",Ph:"enhanced_match_result",
pe:"euid_mode_enabled",fb:"event_start_timestamp_ms",yl:"event_usage",ri:"extra_tag_experiment_ids",Pr:"add_parameter",si:"attribution_reporting_experiment",ui:"counting_method",Xg:"send_as_iframe",Qr:"parameter_order",Yg:"parsed_target",Qo:"ga4_collection_subdomain",Nl:"gbraid_cookie_marked",Pl:"handle_internally",ba:"hit_type",xd:"hit_type_override",If:"ignore_hit_success_failure",Tr:"is_config_command",bh:"is_consent_update",Jf:"is_conversion",Ul:"is_ecommerce",yd:"is_external_event",zi:"is_fallback_aw_conversion_ping_allowed",
Kf:"is_first_visit",Vl:"is_first_visit_conversion",eh:"is_fl_fallback_conversion_flow_allowed",zd:"is_fpm_encryption",Ai:"is_fpm_split",Fb:"is_gcp_conversion",Wl:"is_google_signals_allowed",Bd:"is_merchant_center",fh:"is_new_to_site",Bi:"is_personalization",gh:"is_server_side_destination",te:"is_session_start",Yl:"is_session_start_conversion",Ur:"is_sgtm_ga_ads_conversion_study_control_group",Vr:"is_sgtm_prehit",Zl:"is_sgtm_service_worker",Ci:"is_split_conversion",Vo:"is_syn",Lf:"join_id",Di:"join_elapsed",
Mf:"join_timer_sec",xe:"tunnel_updated",Zr:"prehit_for_retry",ds:"promises",es:"record_aw_latency",Lc:"redact_ads_data",ye:"redact_click_ids",jm:"remarketing_only",Ki:"send_ccm_parallel_ping",ks:"send_ccm_parallel_test_ping",Qf:"send_to_destinations",Li:"send_to_targets",kp:"send_user_data_hit",ab:"source_canonical_id",Aa:"speculative",vm:"speculative_in_message",wm:"suppress_script_load",xm:"syn_or_mod",Cm:"transient_ecsid",Rf:"transmission_type",Sa:"user_data",qs:"user_data_from_automatic",rs:"user_data_from_automatic_getter",
Em:"user_data_from_code",pp:"user_data_from_manual",Fm:"user_data_mode",Sf:"user_id_updated"}};var co={};function eo(a){var b=a,c=a=fo[b.studyId]?oa(Object,"assign").call(Object,{},b,{active:!0}):b;c.controlId2&&c.probability<=.25||(c=oa(Object,"assign").call(Object,{},c,{controlId2:0}));qi[c.studyId]=c;a.focused&&(co[a.studyId]=!0);if(a.la===1){var d=a.studyId;go(Nl(Il.X.Ii,{}),d);ho(d)&&C(d)}else if(a.la===0){var e=a.studyId;go(io,e);ho(e)&&C(e)}}
function go(a,b){if(qi[b]){var c=qi[b],d=c.experimentId,e=c.probability;if(!(a.studies||{})[b]){var f=a.studies||{};f[b]=!0;a.studies=f;qi[b].active||(qi[b].probability>.5?ui(a,d,b):e<=0||e>1||ti.ar(a,b))}}if(!co[b]){var g=wi(a,b);g&&Hi.U.H.add(g)}}var io={};function ho(a){return vi(Nl(Il.X.Ii,{}),a)||vi(io,a)}function jo(a){var b=T(a,R.A.ri)||[];return Wj(b)}var fo={};
function ko(){fo={};var a,b,c=((a=w)==null?void 0:(b=a.location)==null?void 0:b.hash)||"";if(c.indexOf("_te=")!==0){var d=c.substring(5);if(d)for(var e=l(d.split("~")),f=e.next();!f.done;f=e.next()){var g=Number(f.value);g&&(fo[g]=!0,C(g))}}for(var h=l(bo()),m=h.next();!m.done;m=h.next())eo(m.value);if(G(264)){for(var n=[],p=l(Ki(56)||[]),q=p.next();!q.done;q=p.next()){var r=q.value,u=r[1],t=!!r[2],v=r[3],x=r[4],y=r[5],A=r[6],D=0;switch(r[7]){case 2:D=1;break;case 1:case 0:D=0}if(t||x&&y){var E;a:switch(u){case 249:E=
!0;break a;default:E=!1}n.push({studyId:u,active:t,la:D,focused:E,probability:v,experimentId:x,controlId:y,controlId2:A})}}for(var J=l(n),F=J.next();!F.done;F=J.next())eo(F.value)}};var lo={Gf:{Gn:"cd",Hn:"ce",In:"cf",Jn:"cpf",Kn:"cu"}};var mo=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,no=/\s/;
function oo(a,b){if(sb(a)){a=Eb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(mo.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||no.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function po(a,b){for(var c={},d=0;d<a.length;++d){var e=oo(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[qo[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var ro={},qo=(ro[0]=0,ro[1]=1,ro[2]=2,ro[3]=0,ro[4]=1,ro[5]=0,ro[6]=0,ro[7]=0,ro);var so=Number(Ji(34,"500")),to={},uo={},vo={initialized:11,complete:12,interactive:13},wo={},xo=Object.freeze((wo[N.m.od]=!0,wo)),yo=void 0;function zo(a,b){if(b.length&&lk){var c;(c=to)[a]!=null||(c[a]=[]);uo[a]!=null||(uo[a]=[]);var d=b.filter(function(e){return!uo[a].includes(e)});to[a].push.apply(to[a],Ba(d));uo[a].push.apply(uo[a],Ba(d));!yo&&d.length>0&&(Xl("tdc",!0),yo=w.setTimeout(function(){$l();to={};yo=void 0},so))}}
function Ao(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Bo(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,u){var t;qd(u)==="object"?t=u[r]:qd(u)==="array"&&(t=u[r]);return t===void 0?xo[r]:t},f=Ao(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=qd(m)==="object"||qd(m)==="array",q=qd(n)==="object"||qd(n)==="array";if(p&&q)Bo(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Co(){Wl("tdc",function(){yo&&(w.clearTimeout(yo),yo=void 0);var a=[],b;for(b in to)to.hasOwnProperty(b)&&a.push(b+"*"+to[b].join("."));return a.length?a.join("!"):void 0},!1)};var Do={T:{Sj:1,Mi:2,Oj:3,lk:4,Pj:5,Wc:6,kk:7,Zo:8,km:9,Qj:10,Rj:11,Zg:12,Fl:13,Cl:14,El:15,Bl:16,Dl:17,Al:18,wn:19,Oo:20,Po:21}};Do.T[Do.T.Sj]="ALLOW_INTEREST_GROUPS";Do.T[Do.T.Mi]="SERVER_CONTAINER_URL";Do.T[Do.T.Oj]="ADS_DATA_REDACTION";Do.T[Do.T.lk]="CUSTOMER_LIFETIME_VALUE";Do.T[Do.T.Pj]="ALLOW_CUSTOM_SCRIPTS";Do.T[Do.T.Wc]="ANY_COOKIE_PARAMS";Do.T[Do.T.kk]="COOKIE_EXPIRES";Do.T[Do.T.Zo]="LEGACY_ENHANCED_CONVERSION_JS_VARIABLE";Do.T[Do.T.km]="RESTRICTED_DATA_PROCESSING";Do.T[Do.T.Qj]="ALLOW_DISPLAY_FEATURES";
Do.T[Do.T.Rj]="ALLOW_GOOGLE_SIGNALS";Do.T[Do.T.Zg]="GENERATED_TRANSACTION_ID";Do.T[Do.T.Fl]="FLOODLIGHT_COUNTING_METHOD_UNKNOWN";Do.T[Do.T.Cl]="FLOODLIGHT_COUNTING_METHOD_STANDARD";Do.T[Do.T.El]="FLOODLIGHT_COUNTING_METHOD_UNIQUE";Do.T[Do.T.Bl]="FLOODLIGHT_COUNTING_METHOD_PER_SESSION";Do.T[Do.T.Dl]="FLOODLIGHT_COUNTING_METHOD_TRANSACTIONS";Do.T[Do.T.Al]="FLOODLIGHT_COUNTING_METHOD_ITEMS_SOLD";Do.T[Do.T.wn]="ADS_OGT_V1_USAGE";Do.T[Do.T.Oo]="FORM_INTERACTION_PERMISSION_DENIED";Do.T[Do.T.Po]="FORM_SUBMIT_PERMISSION_DENIED";var Eo={},Fo=(Eo[N.m.Rh]=Do.T.Sj,Eo[N.m.rd]=Do.T.Mi,Eo[N.m.rc]=Do.T.Mi,Eo[N.m.Ha]=Do.T.Oj,Eo[N.m.bf]=Do.T.lk,Eo[N.m.Ag]=Do.T.Pj,Eo[N.m.zc]=Do.T.Wc,Eo[N.m.Wa]=Do.T.Wc,Eo[N.m.ub]=Do.T.Wc,Eo[N.m.ed]=Do.T.Wc,Eo[N.m.Rb]=Do.T.Wc,Eo[N.m.Bb]=Do.T.Wc,Eo[N.m.wb]=Do.T.kk,Eo[N.m.Sb]=Do.T.km,Eo[N.m.Bg]=Do.T.Qj,Eo[N.m.Pb]=Do.T.Rj,Eo),Go={},Ho=(Go.unknown=Do.T.Fl,Go.standard=Do.T.Cl,Go.unique=Do.T.El,Go.per_session=Do.T.Bl,Go.transactions=Do.T.Dl,Go.items_sold=Do.T.Al,Go);var nb=[];function Io(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Fo)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Fo[f],h=b;h=h===void 0?!1:h;kb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(nb[g]=!0)}}};var Jo=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.U=d;this.H=e;this.R=f;this.M=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Ko=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 2:c.push(a.C);break;case 1:c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 4:c.push(a.C),c.push(a.U),c.push(a.H),c.push(a.R)}return c},Q=function(a,b,c,d){for(var e=l(Ko(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Lo=function(a){for(var b={},c=Ko(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Jo.prototype.getMergedValues=function(a,b,c){function d(n){sd(n)&&zb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Ko(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Mo=function(a){for(var b=[N.m.Ye,N.m.Ue,N.m.Ve,N.m.We,N.m.Xe,N.m.Ze,N.m.af],c=Ko(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},No=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.U={};this.C={};this.M={};this.ja={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Oo=function(a,
b){a.H=b;return a},Po=function(a,b){a.U=b;return a},Qo=function(a,b){a.C=b;return a},Ro=function(a,b){a.M=b;return a},So=function(a,b){a.ja=b;return a},To=function(a,b){a.R=b;return a},Uo=function(a,b){a.eventMetadata=b||{};return a},Vo=function(a,b){a.onSuccess=b;return a},Wo=function(a,b){a.onFailure=b;return a},Xo=function(a,b){a.isGtmEvent=b;return a},Yo=function(a){return new Jo(a.eventId,a.priorityId,a.H,a.U,a.C,a.M,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Zo=new yb,$o={},ap={},dp={name:Fi(19),set:function(a,b){td(Nb(a,b),$o);bp()},get:function(a){return cp(a,2)},reset:function(){Zo=new yb;$o={};bp()}};function cp(a,b){return b!=2?Zo.get(a):ep(a)}function ep(a,b){var c=a.split(".");b=b||[];for(var d=$o,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function fp(a,b){ap.hasOwnProperty(a)||(Zo.set(a,b),td(Nb(a,b),$o),bp())}
function gp(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=cp(c,1);if(Array.isArray(d)||sd(d))d=td(d,null);ap[c]=d}}function bp(a){zb(ap,function(b,c){Zo.set(b,c);td(Nb(b),$o);td(Nb(b,c),$o);a&&delete ap[b]})}function hp(a,b){var c,d=(b===void 0?2:b)!==1?ep(a):Zo.get(a);qd(d)==="array"||qd(d)==="object"?c=td(d,null):c=d;return c};var ip={vn:Number(Ji(3))},jp=[],kp=!1;function lp(a){jp.push(a)}var mp=void 0,np={},op=void 0,pp=new function(){var a=5;ip.vn>0&&(a=ip.vn);this.H=a;this.C=0;this.M=[]},qp=1E3;
function rp(a,b){var c=mp;if(c===void 0)if(b)c=Yn();else return"";for(var d=[ek("https://"+Fi(21)),"/a","?id="+Fi(5)],e=l(jp),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Uc:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function sp(){if(Hi.ja&&(op&&(w.clearTimeout(op),op=void 0),mp!==void 0&&tp)){var a=Hl(hl.Z.Jc);if(Dl(a))kp||(kp=!0,Fl(a,sp));else{var b;if(!(b=np[mp])){var c=pp;b=c.C<c.H?!1:Gb()-c.M[c.C%c.H]<1E3}if(b||qp--<=0)P(1),np[mp]=!0;else{var d=pp,e=d.C++%d.H;d.M[e]=Gb();var f=rp(!0);bl({destinationId:Fi(5),endpoint:56,eventId:mp},f);kp=tp=!1}}}}function up(){if(jk&&Hi.ja){var a=rp(!0,!0);bl({destinationId:Fi(5),endpoint:56,eventId:mp},a)}}var tp=!1;
function vp(a){np[a]||(a!==mp&&(sp(),mp=a),tp=!0,op||(op=w.setTimeout(sp,500)),rp().length>=2022&&sp())}var wp=wb();function xp(){wp=wb()}function yp(){return[["v","3"],["t","t"],["pid",String(wp)]]};var zp={};function Ap(a,b,c){jk&&a!==void 0&&(zp[a]=zp[a]||[],zp[a].push(c+b),vp(a))}function Bp(a){var b=a.eventId,c=a.Uc,d=[],e=zp[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete zp[b];return d};function Cp(a,b,c,d){var e=oo(a,!0);e&&Dp.register(e,b,c,d)}function Ep(a,b,c,d){var e=oo(c,d.isGtmEvent);e&&(Vi&&(d.deferrable=!0),Dp.push("event",[b,a],e,d))}function Fp(a,b,c,d){var e=oo(c,d.isGtmEvent);e&&Dp.push("get",[a,b],e,d)}function Gp(a){var b=oo(a,!0),c;b?c=Hp(Dp,b).M:c={};return c}function Ip(a,b){var c=oo(a,!0);c&&Jp(Dp,c,b)}
var Kp=function(){this.C={};this.M={};this.R={};this.ja=null;this.H={};this.U=!1;this.status=1},Lp=function(a,b,c,d){this.H=Gb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Mp=function(){this.destinations={};this.C={};this.commands=[]},Hp=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Kp},Np=function(a,b,c,d){if(d.C){var e=Hp(a,d.C),f=e.ja;if(f){var g=td(c,null),h=td(e.C[d.C.id],null),m=td(e.H,null),n=td(e.M,null),p=td(a.C,null),q={};if(jk)try{q=
td($o,null)}catch(x){P(72)}var r=d.C.prefix,u=function(x){Ap(d.messageContext.eventId,r,x)},t=Yo(Xo(Wo(Vo(Uo(So(Ro(To(Qo(Po(Oo(new No(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Ap(d.messageContext.eventId,
r,"1");var x=d.type,y=d.C.id;if(lk&&x==="config"){var A,D=(A=oo(y))==null?void 0:A.ids;if(!(D&&D.length>1)){var E,J=Dc("google_tag_data",{});J.td||(J.td={});E=J.td;var F=td(t.R);td(t.C,F);var M=[],U;for(U in E)E.hasOwnProperty(U)&&Bo(E[U],F).length&&M.push(U);M.length&&(zo(y,M),kb("TAGGING",vo[z.readyState]||14));E[y]=F}}f(d.C.id,b,d.H,t)}catch(ha){Ap(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():Fl(e.ma,v)}}};
Mp.prototype.register=function(a,b,c,d){var e=Hp(this,a);e.status!==3&&(e.ja=b,e.status=3,e.ma=Hl(c),Jp(this,a,d||{}),this.flush())};
Mp.prototype.push=function(a,b,c,d){c!==void 0&&(Hp(this,c).status===1&&(Hp(this,c).status=2,this.push("require",[{}],c,{})),Hp(this,c).U&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.A.Qf]||(d.eventMetadata[R.A.Qf]=[c.destinationId]),d.eventMetadata[R.A.Li]||(d.eventMetadata[R.A.Li]=[c.id]));this.commands.push(new Lp(a,c,b,d));d.deferrable||this.flush()};
Mp.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Nc:void 0,qh:void 0,Xi:void 0,Yi:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Hp(this,g).U?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Hp(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];zb(h,function(t,v){td(Nb(t,v),b.C)});Io(h,!0);break;case "config":var m=
Hp(this,g);e.Nc={};zb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Nc)}}(e));var n=!!e.Nc[N.m.sd];delete e.Nc[N.m.sd];var p=g.destinationId===g.id;Io(e.Nc,!0);n||(p?m.H={}:m.C[g.id]={});m.U&&n||Np(this,N.m.na,e.Nc,f);m.U=!0;p?td(e.Nc,m.H):(td(e.Nc,m.C[g.id]),P(70));d=!0;break;case "event":e.qh={};zb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.qh)}}(e));Io(e.qh);Np(this,f.args[1],e.qh,f);break;case "get":var q={},r=(q[N.m.kf]=f.args[0],q[N.m.jf]=f.args[1],q);Np(this,N.m.nc,r,
f);break;case "container_config":e.Xi={};zb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Xi)}}(e));td(e.Xi,Hp(this,g).H);break;case "destination_config":var u=Hp(this,g);e.Yi={};zb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Yi)}}(e));u.C[g.id]||(u.C[g.id]={});td(e.Yi,u.C[g.id])}this.commands.shift();Op(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var Op=function(a,b){if(b.type!=="require")if(b.C)for(var c=Hp(a,b.C).R[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.R)for(var g=f.R[b.type]||[],h=0;h<g.length;h++)g[h]()}},Jp=function(a,b,c){var d=td(c,null);td(Hp(a,b).M,d);Hp(a,b).M=d},Dp=new Mp;function Pp(a){var b=a.location.href;if(a===a.top)return{url:b,Cq:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1],g;f&&((g=b)==null?void 0:g.indexOf(f))===-1&&(c=!1,b=f)}return{url:b,Cq:c}}function Qp(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Hk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}}function Rp(){for(var a=w,b=a;a&&a!=a.parent;)a=a.parent,Qp(a)&&(b=a);return b};var Sp=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Tp=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};function Up(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};var Vp=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Wp=function(a){var b=w;if(b.top==b)return 0;if(a===void 0?0:a){var c=b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Qp(b.top)?1:2},Xp=function(a){a=a===void 0?document:a;return a.createElement("img")};function Yp(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Zp(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function $p(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Xp(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=wc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Zp(e,"load",f);Zp(e,"error",f)};Yp(e,"load",f);Yp(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function aq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Up(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});bq(c,b)}
function bq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else $p(c,a,b===void 0?!1:b,d===void 0?!1:d)};var cq=function(){this.ja=this.ja;this.R=this.R};cq.prototype.ja=!1;cq.prototype.dispose=function(){this.ja||(this.ja=!0,this.M())};cq.prototype[ma.Symbol.dispose]=function(){this.dispose()};cq.prototype.addOnDisposeCallback=function(a,b){this.ja?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};cq.prototype.M=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function dq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var eq=function(a,b){b=b===void 0?{}:b;cq.call(this);this.C=null;this.ma={};this.Kc=0;this.U=null;this.H=a;var c;this.Za=(c=b.timeoutMs)!=null?c:500;var d;this.Ja=(d=b.ys)!=null?d:!1};ya(eq,cq);eq.prototype.M=function(){this.ma={};this.U&&(Zp(this.H,"message",this.U),delete this.U);delete this.ma;delete this.H;delete this.C;cq.prototype.M.call(this)};var gq=function(a){return typeof a.H.__tcfapi==="function"||fq(a)!=null};
eq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ja},d=Tp(function(){return a(c)}),e=0;this.Za!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Za));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=dq(c),c.internalBlockOnErrors=b.Ja,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{hq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};eq.prototype.removeEventListener=function(a){a&&a.listenerId&&hq(this,"removeEventListener",null,a.listenerId)};
var jq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=iq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&iq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?iq(a.purpose.legitimateInterests,
b)&&iq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},iq=function(a,b){return!(!a||!a[b])},hq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(fq(a)){kq(a);var g=++a.Kc;a.ma[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},fq=function(a){if(a.C)return a.C;a.C=Vp(a.H,"__tcfapiLocator");return a.C},kq=function(a){if(!a.U){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.U=b;Yp(a.H,"message",b)}},lq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=dq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(aq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var mq={1:0,3:0,4:0,7:3,9:3,10:3};Ji(32);function nq(){return Tn("tcf",function(){return{}})}var oq=function(){return new eq(w,{timeoutMs:-1})};
function pq(){var a=nq(),b=oq();gq(b)&&!qq()&&!rq()&&P(124);if(!a.active&&gq(b)){qq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,il().active=!0,a.tcString="tcunavailable");Ln();try{b.addEventListener(function(c){if(c.internalErrorState!==0)sq(a),Mn([N.m.aa,N.m.Ma,N.m.W]),il().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,rq()&&(a.active=!0),!tq(c)||qq()||rq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in mq)mq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(tq(c)){var g={},h;for(h in mq)if(mq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={bq:!0};p=p===void 0?{}:p;m=lq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.bq)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?jq(n,"1",0):!0:!1;g["1"]=m}else g[h]=jq(c,h,mq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[N.m.aa]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Mn([N.m.aa,N.m.Ma,N.m.W]),il().active=!0):(r[N.m.Ma]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[N.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Mn([N.m.W]),un(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:uq()||""}))}}else Mn([N.m.aa,N.m.Ma,N.m.W])})}catch(c){sq(a),Mn([N.m.aa,N.m.Ma,N.m.W]),il().active=!0}}}
function sq(a){a.type="e";a.tcString="tcunavailable"}function tq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function qq(){return w.gtag_enable_tcf_support===!0}function rq(){return nq().enableAdvertiserConsentMode===!0}function uq(){var a=nq();if(a.active)return a.tcString}function vq(){var a=nq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function wq(a){if(!mq.hasOwnProperty(String(a)))return!0;var b=nq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var xq=[N.m.aa,N.m.ka,N.m.W,N.m.Ma],yq={},zq=(yq[N.m.aa]=1,yq[N.m.ka]=2,yq);function Aq(a){if(a===void 0)return 0;switch(Q(a,N.m.Ob)){case void 0:return 1;case !1:return 3;default:return 2}}function Bq(){return(G(183)?Ji(16).split("~"):Ji(17).split("~")).indexOf(vm())!==-1&&zc.globalPrivacyControl===!0}function Cq(a){if(Bq())return!1;var b=Aq(a);if(b===3)return!1;switch(rl(N.m.Ma)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Dq(){return tl()||!ql(N.m.aa)||!ql(N.m.ka)}function Eq(){var a={},b;for(b in zq)zq.hasOwnProperty(b)&&(a[zq[b]]=rl(b));return"G1"+jf(a[1]||0)+jf(a[2]||0)}var Fq={},Gq=(Fq[N.m.aa]=0,Fq[N.m.ka]=1,Fq[N.m.W]=2,Fq[N.m.Ma]=3,Fq);function Hq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Iq(a){for(var b="1",c=0;c<xq.length;c++){var d=b,e,f=xq[c],g=pl.delegatedConsentTypes[f];e=g===void 0?0:Gq.hasOwnProperty(g)?12|Gq[g]:8;var h=il();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Hq(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Hq(m.declare)<<4|Hq(m.default)<<2|Hq(m.update)])}var n=b,p=(Bq()?1:0)<<3,q=(tl()?1:0)<<2,r=Aq(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[pl.containerScopedDefaults.ad_storage<<4|pl.containerScopedDefaults.analytics_storage<<2|pl.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(pl.usedContainerScopedDefaults?1:0)<<2|pl.containerScopedDefaults.ad_personalization]}
function Jq(){if(!ql(N.m.W))return"-";for(var a=Object.keys(Nm),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=pl.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Nm[m])}(pl.usedCorePlatformServices?pl.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Kq(){return xm()||(qq()||rq())&&vq()==="1"?"1":"0"}function Lq(){return(xm()?!0:!(!qq()&&!rq())&&vq()==="1")||!ql(N.m.W)}
function Mq(){var a="0",b="0",c;var d=nq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=nq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;xm()&&(h|=1);vq()==="1"&&(h|=2);qq()&&(h|=4);var m;var n=nq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);il().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Nq(){return vm()==="US-CO"};var Oq;function Pq(){if(Cc===null)return 0;var a=hd();if(!a)return 0;var b=a.getEntriesByName(Cc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Qq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Rq(a){a=a===void 0?{}:a;var b=Fi(5).split("-")[0].toUpperCase(),c,d={ctid:Fi(5),ln:Ii(15),on:Fi(14),Eq:Ei(7)?2:1,lr:a.qn,canonicalId:Fi(6),Yq:(c=Lj())==null?void 0:c.canonicalContainerId,mr:a.Qd===void 0?void 0:a.Qd?10:12};if(G(204)){var e;d.Hp=(e=Oq)!=null?e:Oq=Pq()}d.canonicalId!==a.Pa&&(d.Pa=a.Pa);var f=Ij();d.Kq=f?f.canonicalContainerId:void 0;Xi?(d.Fh=Qq[b],d.Fh||(d.Fh=0)):d.Fh=Yi?13:10;Hi.H?(d.Um=0,d.Bp=2):d.Um=Hi.C?1:3;var g={6:!1};Hi.M===2?g[7]=!0:Hi.M===1&&(g[2]=!0);if(Cc){var h=
mj(sj(Cc),"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}d.Ip=g;return mf(d,a.oh)};function Sq(a,b,c,d){var e,f=Number(a.Rc!=null?a.Rc:void 0);f!==0&&(e=new Date((b||Gb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,wc:d}};var Tq=["ad_storage","ad_user_data"];function Uq(a,b){if(!a)return kb("TAGGING",32),10;if(b===null||b===void 0||b==="")return kb("TAGGING",33),11;var c=Vq(!1);if(c.error!==0)return kb("TAGGING",34),c.error;if(!c.value)return kb("TAGGING",35),2;c.value[a]=b;var d=Wq(c);d!==0&&kb("TAGGING",36);return d}
function Xq(a){if(!a)return kb("TAGGING",27),{error:10};var b=Vq();if(b.error!==0)return kb("TAGGING",29),b;if(!b.value)return kb("TAGGING",30),{error:2};if(!(a in b.value))return kb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(kb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Vq(a){a=a===void 0?!0:a;if(!ql(Tq))return kb("TAGGING",43),{error:3};try{if(!w.localStorage)return kb("TAGGING",44),{error:1}}catch(f){return kb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=w.localStorage.getItem("_gcl_ls")}catch(f){return kb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return kb("TAGGING",47),{error:12}}}catch(f){return kb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return kb("TAGGING",49),{error:4};
if(b.version!==1)return kb("TAGGING",50),{error:5};try{var e=Yq(b);a&&e&&Wq({value:b,error:0})}catch(f){return kb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Yq(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,kb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Yq(a[e.value])||c;return c}return!1}
function Wq(a){if(a.error)return a.error;if(!a.value)return kb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return kb("TAGGING",52),6}try{w.localStorage.setItem("_gcl_ls",c)}catch(d){return kb("TAGGING",53),7}return 0};var Zq={lg:"value",hb:"conversionCount",Ah:1},$q={xh:9,Eh:10,lg:"timeouts",hb:"timeouts",Ah:0},ar=[Zq,$q,{xh:11,Eh:12,lg:"eopCount",hb:"endOfPageCount",Ah:0},{xh:11,Eh:12,lg:"errors",hb:"errors",Ah:0}];function br(a){var b;b=b===void 0?1:b;if(!cr(a))return{};var c=dr(ar),d=c[a.hb];if(d===void 0||d===-1)return c;var e={},f=oa(Object,"assign").call(Object,{},c,(e[a.hb]=d+b,e));return er(f)?f:c}
function dr(a){var b;a:{var c=Xq("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&cr(m)){var n=e[m.lg];n===void 0||Number.isNaN(n)?f[m.hb]=-1:f[m.hb]=Number(n)}else f[m.hb]=-1}return f}
function fr(){for(var a=br(Zq),b=[],c=l(ar),d=c.next();!d.done;d=c.next()){var e=d.value,f=a[e.hb];if(f===void 0||f<e.Ah)break;b.push(f.toString())}return b.join("~")}function er(a,b){b=b||{};for(var c=Gb(),d=Sq(b,c,!0),e={},f=l(ar),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.hb];m!==void 0&&m!==-1&&(e[h.lg]=m)}e.creationTimeMs=c;return Uq("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function cr(a){return ql(["ad_storage","ad_user_data"])?!a.Eh||Xa(a.Eh):!1}
function gr(a){return ql(["ad_storage","ad_user_data"])?!a.xh||Xa(a.xh):!1};var hr={O:{jp:0,Nj:1,vg:2,bk:3,Kh:4,Yj:5,Zj:6,dk:7,Lh:8,wl:9,vl:10,oi:11,xl:12,Wg:13,Gl:14,Of:15,hp:16,ze:17,Qi:18,Ri:19,Si:20,zm:21,Ti:22,Nh:23,mk:24}};hr.O[hr.O.jp]="RESERVED_ZERO";hr.O[hr.O.Nj]="ADS_CONVERSION_HIT";hr.O[hr.O.vg]="CONTAINER_EXECUTE_START";hr.O[hr.O.bk]="CONTAINER_SETUP_END";hr.O[hr.O.Kh]="CONTAINER_SETUP_START";hr.O[hr.O.Yj]="CONTAINER_BLOCKING_END";hr.O[hr.O.Zj]="CONTAINER_EXECUTE_END";hr.O[hr.O.dk]="CONTAINER_YIELD_END";hr.O[hr.O.Lh]="CONTAINER_YIELD_START";hr.O[hr.O.wl]="EVENT_EXECUTE_END";
hr.O[hr.O.vl]="EVENT_EVALUATION_END";hr.O[hr.O.oi]="EVENT_EVALUATION_START";hr.O[hr.O.xl]="EVENT_SETUP_END";hr.O[hr.O.Wg]="EVENT_SETUP_START";hr.O[hr.O.Gl]="GA4_CONVERSION_HIT";hr.O[hr.O.Of]="PAGE_LOAD";hr.O[hr.O.hp]="PAGEVIEW";hr.O[hr.O.ze]="SNIPPET_LOAD";hr.O[hr.O.Qi]="TAG_CALLBACK_ERROR";hr.O[hr.O.Ri]="TAG_CALLBACK_FAILURE";hr.O[hr.O.Si]="TAG_CALLBACK_SUCCESS";hr.O[hr.O.zm]="TAG_EXECUTE_END";hr.O[hr.O.Ti]="TAG_EXECUTE_START";hr.O[hr.O.Nh]="CUSTOM_PERFORMANCE_START";hr.O[hr.O.mk]="CUSTOM_PERFORMANCE_END";var ir=[],jr={},kr={};function lr(a){if(Xa(19)&&ir.includes(a)){var b;(b=hd())==null||b.mark(a+"-"+hr.O.Nh+"-"+(kr[a]||0))}}function mr(a){if(Xa(19)&&ir.includes(a)){var b=a+"-"+hr.O.mk+"-"+(kr[a]||0),c={start:a+"-"+hr.O.Nh+"-"+(kr[a]||0),end:b},d;(d=hd())==null||d.mark(b);var e,f,g=(f=(e=hd())==null?void 0:e.measure(b,c))==null?void 0:f.duration;g!==void 0&&(kr[a]=(kr[a]||0)+1,jr[a]=g+(jr[a]||0))}};var nr=["2","3"];function or(a){return a.origin!=="null"};function pr(a,b){var c=Nl(Il.X.Fi,Pa()).get(a);if(c&&(!c.expires||(typeof c.expires==="string"?(new Date(c.expires)).getTime():c.expires.getTime())>=Date.now())&&c.value!==void 0)return b?decodeURIComponent(c.value):c.value}function qr(a,b,c){var d=Nl(Il.X.Fi,Pa());d.set(a,{expires:c,value:b});Ll(Il.X.Fi,d)}var rr=0,sr=0;
function tr(a,b,c,d,e){try{lr("3");var f;Xa(20)&&!e&&(f=pr(a,c));var g,h=(g=ur(function(m){return m===a},b,c,d)[a])!=null?g:[];f!==void 0&&(h.includes(f)?sr++:rr++);return h}finally{mr("3")}}function ur(a,b,c,d){var e;if(vr(d)){for(var f={},g=String(b||wr()).split(";"),h=0;h<g.length;h++){var m=g[h].split("="),n=m[0].trim();if(n&&a(n)){var p=m.slice(1).join("=").trim();p&&c&&(p=decodeURIComponent(p));var q=void 0,r=void 0;((q=f)[r=n]||(q[r]=[])).push(p)}}e=f}else e={};return e}
function xr(a,b,c,d,e){if(vr(e)){var f=yr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=zr(f,function(g){return g.Sp},b);if(f.length===1)return f[0];f=zr(f,function(g){return g.Mq},c);return f[0]}}}function Ar(a,b,c,d){var e=wr(),f=window;or(f)&&(f.document.cookie=a);var g=wr();return e!==g||c!==void 0&&tr(b,g,!1,d,!0).indexOf(c)>=0}
function Br(a,b,c,d){function e(x,y,A){if(A==null)return delete h[y],x;h[y]=A;return x+"; "+y+"="+A}function f(x,y){if(y==null)return x;h[y]=!0;return x+"; "+y}if(!vr(c.wc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Cr(b),g=a+"="+b);var h={};Xa(20)&&qr(a,b,c.xj?new Date(Date.now()+Number(c.xj)*1E3):c.expires);g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,
"expires",m);g=e(g,"max-age",c.xj);g=e(g,"samesite",c.Zq);c.secure&&(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Dr(),q=void 0,r=!1,u=0;u<p.length;++u){var t=p[u]!=="none"?p[u]:void 0,v=e(g,"domain",t);v=f(v,c.flags);try{d&&d(a,h)}catch(x){q=x;continue}r=!0;if(!Er(t,c.path)&&Ar(v,a,b,c.wc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Er(n,c.path)?1:Ar(g,a,b,c.wc)?0:1}
function Fr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");lr("2");var d=Br(a,b,c);mr("2");return d}function zr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}
function yr(a,b,c){for(var d=[],e=tr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Kp:e[f],Lp:g.join("."),Sp:Number(n[0])||1,Mq:Number(n[1])||1})}}}return d}function Cr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}var Gr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Hr=/(^|\.)doubleclick\.net$/i;
function Er(a,b){return a!==void 0&&(Hr.test(window.document.location.hostname)||b==="/"&&Gr.test(a))}function Ir(a){if(!a)return 1;var b=a;Xa(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Jr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}function Kr(a,b){var c=""+Ir(a),d=Jr(b);d>1&&(c+="-"+d);return c}
var wr=function(){return or(window)?window.document.cookie:""},vr=function(a){return a&&Xa(7)?(Array.isArray(a)?a:[a]).every(function(b){return sl(b)&&ql(b)}):!0},Dr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;Hr.test(e)||Gr.test(e)||a.push("none");return a};function Lr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Nh(a)&2147483647):String(b)}function Mr(a){return[Lr(a),Math.round(Gb()/1E3)].join(".")}function Nr(a,b,c,d,e){var f=Ir(b),g;return(g=xr(a,f,Jr(c),d,e))==null?void 0:g.Lp};var Or;function Pr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Qr,d=Rr,e=Sr();if(!e.init){Qc(z,"mousedown",a);Qc(z,"keyup",a);Qc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Tr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Sr().decorators.push(f)}
function Ur(a,b,c){for(var d=Sr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Jb(e,g.callback())}}return e}
function Sr(){var a=Dc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Vr=/(.*?)\*(.*?)\*(.*)/,Wr=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Xr=/^(?:www\.|m\.|amp\.)+/,Yr=/([^?#]+)(\?[^#]*)?(#.*)?/;function Zr(a){var b=Yr.exec(a);if(b)return{Bj:b[1],query:b[2],fragment:b[3]}}function $r(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function as(a,b){var c=[zc.userAgent,(new Date).getTimezoneOffset(),zc.userLanguage||zc.language,Math.floor(Gb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Or)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Or=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Or[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function bs(a){return function(b){var c=sj(w.location.href),d=c.search.replace("?",""),e=jj(d,"_gl",!1,!0)||"";b.query=cs(e)||{};var f=mj(c,"fragment"),g;var h=-1;if(Lb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=cs(g||"")||{};a&&ds(c,d,f)}}function es(a,b){var c=$r(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function ds(a,b,c){function d(g,h){var m=es("_gl",g);m.length&&(m=h+m);return m}if(yc&&yc.replaceState){var e=$r("_gl");if(e.test(b)||e.test(c)){var f=mj(a,"path");b=d(b,"?");c=d(c,"#");yc.replaceState({},"",""+f+b+c)}}}function fs(a,b){var c=bs(!!b),d=Sr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Jb(e,f.query),a&&Jb(e,f.fragment));return e}
var cs=function(a){try{var b=gs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ib(d[e+1]);c[f]=g}kb("TAGGING",6);return c}}catch(h){kb("TAGGING",8)}};function gs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Vr.exec(d);if(f){c=f;break a}d=lj(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===as(h,p)){m=!0;break a}m=!1}if(m)return h;kb("TAGGING",7)}}}
function hs(a,b,c,d,e){function f(p){p=es(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Zr(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Bj+h+m}
function is(a,b){function c(n,p,q){var r;a:{for(var u in n)if(n.hasOwnProperty(u)){r=!0;break a}r=!1}if(r){var t,v=[],x;for(x in n)if(n.hasOwnProperty(x)){var y=n[x];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(x),v.push(hb(String(y))))}var A=v.join("*");t=["1",as(A),A].join("*");d?(Xa(3)||Xa(1)||!p)&&js("_gl",t,a,p,q):ks("_gl",t,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ur(b,1,d),f=Ur(b,2,d),g=Ur(b,4,d),h=Ur(b,3,d);c(e,!1,!1);c(f,!0,!1);Xa(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ls(m,h[m],a)}function ls(a,b,c){c.tagName.toLowerCase()==="a"?ks(a,b,c):c.tagName.toLowerCase()==="form"&&js(a,b,c)}function ks(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Xa(4)||d)){var h=w.location.href,m=Zr(c.href),n=Zr(h);g=!(m&&n&&m.Bj===n.Bj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=hs(a,b,c.href,d,e);oc.test(p)&&(c.href=p)}}
function js(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=hs(a,b,f,d,e);oc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Qr(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||is(e,e.hostname)}}catch(g){}}function Rr(a){try{var b=a.getAttribute("action");if(b){var c=mj(sj(b),"host");is(a,c)}}catch(d){}}function ms(a,b,c,d){Pr();var e=c==="fragment"?2:1;d=!!d;Tr(a,b,e,d,!1);e===2&&kb("TAGGING",23);d&&kb("TAGGING",24)}
function ns(a,b){Pr();Tr(a,[oj(w.location,"host",!0)],b,!0,!0)}function os(){var a=z.location.hostname,b=Wr.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?lj(f[2])||"":lj(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Xr,""),m=e.replace(Xr,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ps(a,b){return a===!1?!1:a||b||os()};var qs=["1"],rs={},ss={};function ts(a,b){b=b===void 0?!0:b;var c=us(a.prefix);if(rs[c])vs(a);else if(ws(c,a.path,a.domain)){var d=ss[us(a.prefix)]||{id:void 0,zh:void 0};b&&xs(a,d.id,d.zh);vs(a)}else{var e=uj("auiddc");if(e)kb("TAGGING",17),rs[c]=e;else if(b){var f=us(a.prefix),g=Mr();ys(f,g,a);ws(c,a.path,a.domain);vs(a,!0)}}}
function vs(a,b){if((b===void 0?0:b)&&cr(Zq)){var c=Vq(!1);c.error!==0?kb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Wq(c)!==0&&kb("TAGGING",41)):kb("TAGGING",40):kb("TAGGING",39)}if(gr(Zq)&&dr([Zq])[Zq.hb]===-1){for(var d={},e=(d[Zq.hb]=0,d),f=l(ar),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Zq&&gr(h)&&(e[h.hb]=0)}er(e,a)}}
function xs(a,b,c){var d=us(a.prefix),e=rs[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Gb()/1E3)));ys(d,h,a,g*1E3)}}}}function ys(a,b,c,d){var e;e=["1",Kr(c.domain,c.path),b].join(".");var f=Sq(c,d);f.wc=zs();Fr(a,e,f)}function ws(a,b,c){var d=Nr(a,b,c,qs,zs());if(!d)return!1;As(a,d);return!0}
function As(a,b){var c=b.split(".");c.length===5?(rs[a]=c.slice(0,2).join("."),ss[a]={id:c.slice(2,4).join("."),zh:Number(c[4])||0}):c.length===3?ss[a]={id:c.slice(0,2).join("."),zh:Number(c[2])||0}:rs[a]=b}function us(a){return(a||"_gcl")+"_au"}function Bs(a){function b(){ql(c)&&a()}var c=zs();wl(function(){b();ql(c)||xl(b,c)},c)}
function Cs(a){var b=fs(!0),c=us(a.prefix);Bs(function(){var d=b[c];if(d){As(c,d);var e=Number(rs[c].split(".")[1])*1E3;if(e){kb("TAGGING",16);var f=Sq(a,e);f.wc=zs();var g=["1",Kr(a.domain,a.path),d].join(".");Fr(c,g,f)}}})}function Ds(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Nr(a,e.path,e.domain,qs,zs());h&&(g[a]=h);return g};Bs(function(){ms(f,b,c,d)})}function zs(){return["ad_storage","ad_user_data"]};function Es(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Lj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Fs(a,b){var c=Es(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Lj]||(d[c[e].Lj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Lj].push(g)}}return d};var Gs={},Hs=(Gs.k={fa:/^[\w-]+$/},Gs.b={fa:/^[\w-]+$/,Ej:!0},Gs.i={fa:/^[1-9]\d*$/},Gs.h={fa:/^\d+$/},Gs.t={fa:/^[1-9]\d*$/},Gs.d={fa:/^[A-Za-z0-9_-]+$/},Gs.j={fa:/^\d+$/},Gs.u={fa:/^[1-9]\d*$/},Gs.l={fa:/^[01]$/},Gs.o={fa:/^[1-9]\d*$/},Gs.g={fa:/^[01]$/},Gs.s={fa:/^.+$/},Gs);var Is={},Ms=(Is[5]={Gh:{2:Js},uj:"2",ph:["k","i","b","u"]},Is[4]={Gh:{2:Js,GCL:Ks},uj:"2",ph:["k","i","b"]},Is[2]={Gh:{GS2:Js,GS1:Ls},uj:"GS2",ph:"sogtjlhd".split("")},Is);function Ns(a,b,c){var d=Ms[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function Js(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(u){}var e={},f=Ms[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Hs[p];r&&(r.Ej?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(u){}}return e}}}function Os(a,b,c){var d=Ms[b];if(d)return[d.uj,c||"1",Ps(a,b)].join(".")}
function Ps(a,b){var c=Ms[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=Hs[g];if(h){var m=a[g];if(m!==void 0)if(h.Ej&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Ks(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Ls(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Qs=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Rs(a,b,c){if(Ms[b]){for(var d=[],e=tr(a,void 0,void 0,Qs.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Ns(g.value,b,c);h&&d.push(Ss(h))}return d}}
function Ts(a){var b=Us;if(Ms[2]){for(var c={},d=ur(a,void 0,void 0,Qs.get(2)),e=Object.keys(d).sort(),f=l(e),g=f.next();!g.done;g=f.next())for(var h=g.value,m=l(d[h]),n=m.next();!n.done;n=m.next()){var p=Ns(n.value,2,b);p&&(c[h]||(c[h]=[]),c[h].push(Ss(p)))}return c}}function Vs(a,b,c,d,e){d=d||{};var f=Kr(d.domain,d.path),g=Os(b,c,f);if(!g)return 1;var h=Sq(d,e,void 0,Qs.get(c));return Fr(a,g,h)}function Ws(a,b){var c=b.fa;return typeof c==="function"?c(a):c.test(a)}
function Ss(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Vf:void 0},c=b.next()){var e=c.value,f=a[e];d.Vf=Hs[e];d.Vf?d.Vf.Ej?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Ws(h,g.Vf)}}(d)):void 0:typeof f==="string"&&Ws(f,d.Vf)||(a[e]=void 0):a[e]=void 0}return a};var Xs=function(){this.value=0};Xs.prototype.set=function(a){return this.value|=1<<a};var Ys=function(a,b){b<=0||(a.value|=1<<b-1)};Xs.prototype.get=function(){return this.value};Xs.prototype.clear=function(a){this.value&=~(1<<a)};Xs.prototype.clearAll=function(){this.value=0};Xs.prototype.equals=function(a){return this.value===a.value};function Zs(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function $s(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function at(){var a=String,b=w.location.hostname,c=w.location.pathname,d=b=Tb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Tb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Nh((""+b+e).toLowerCase()))};var bt={},ct=(bt.gclid=!0,bt.dclid=!0,bt.gbraid=!0,bt.wbraid=!0,bt),dt=/^\w+$/,et=/^[\w-]+$/,ft={},gt=(ft.aw="_aw",ft.dc="_dc",ft.gf="_gf",ft.gp="_gp",ft.gs="_gs",ft.ha="_ha",ft.ag="_ag",ft.gb="_gb",ft),ht=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,it=/^www\.googleadservices\.com$/;function jt(){return["ad_storage","ad_user_data"]}function kt(a){return!Xa(7)||ql(a)}function lt(a,b){function c(){var d=kt(b);d&&a();return d}wl(function(){c()||xl(c,b)},b)}
function mt(a){return nt(a).map(function(b){return b.gclid})}function ot(a){return pt(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function pt(a){var b=qt(a.prefix),c=rt("gb",b),d=rt("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=nt(c).map(e("gb")),g=st(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function tt(a,b,c,d,e){var f=vb(a,function(g){return g.gclid===b});f?(f.timestamp<c&&(f.timestamp=c,f.Qc=e),f.labels=ut(f.labels||[],d||[])):a.push({version:"2",gclid:b,timestamp:c,labels:d,Qc:e})}function st(a){for(var b=Rs(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=vt(f);h&&tt(c,g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function nt(a){for(var b=[],c=tr(a,z.cookie,void 0,jt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=wt(e.value);f!=null&&(f.Qc=void 0,f.Ca=new Xs,f.cb=[1],xt(b,f))}b.sort(function(g,h){return h.timestamp-g.timestamp});return zt(b)}function At(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function xt(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ca&&b.Ca&&h.Ca.equals(b.Ca)&&(e=h)}if(d){var m,n,p=(m=d.Ca)!=null?m:new Xs,q=(n=b.Ca)!=null?n:new Xs;p.value|=q.value;d.Ca=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Qc=b.Qc);d.labels=At(d.labels||[],b.labels||[]);d.cb=At(d.cb||[],b.cb||[])}else c&&e?oa(Object,"assign").call(Object,e,b):a.push(b)}
function Bt(a){if(!a)return new Xs;var b=new Xs;if(a===1)return Ys(b,2),Ys(b,3),b;Ys(b,a);return b}
function Ct(){var a=Xq("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(et))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Xs;typeof e==="number"?g=Bt(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ca:g,cb:[2]}}catch(h){return null}}
function Dt(){var a=Xq("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(et))return b;var f=new Xs,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ca:f,cb:[2]});return b},[])}catch(b){return null}}
function Et(a){for(var b=[],c=tr(a,z.cookie,void 0,jt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=wt(e.value);f!=null&&(f.Qc=void 0,f.Ca=new Xs,f.cb=[1],xt(b,f))}var g=Ct();g&&(g.Qc=void 0,g.cb=g.cb||[2],xt(b,g));if(Xa(14)){var h=Dt();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Qc=void 0;p.cb=p.cb||[2];xt(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return zt(b)}
function ut(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function qt(a){return a&&typeof a==="string"&&a.match(dt)?a:"_gcl"}function Ft(a,b){if(a){var c={value:a,Ca:new Xs};Ys(c.Ca,b);return c}}
function Gt(a,b,c){var d=sj(a),e=mj(d,"query",!1,void 0,"gclsrc"),f=Ft(mj(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Ft(jj(g,"gclid",!1),3));e||(e=jj(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Ht(a,b){var c=sj(a),d=mj(c,"query",!1,void 0,"gclid"),e=mj(c,"query",!1,void 0,"gclsrc"),f=mj(c,"query",!1,void 0,"wbraid");f=Rb(f);var g=mj(c,"query",!1,void 0,"gbraid"),h=mj(c,"query",!1,void 0,"gad_source"),m=mj(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||jj(n,"gclid",!1);e=e||jj(n,"gclsrc",!1);f=f||jj(n,"wbraid",!1);g=g||jj(n,"gbraid",!1);h=h||jj(n,"gad_source",!1)}return It(d,e,m,f,g,h)}function Jt(){return Ht(w.location.href,!0)}
function It(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(et))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&et.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&et.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&et.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Kt(a){for(var b=Jt(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Ht(w.document.referrer,!1),b.gad_source=void 0);Lt(b,!1,a)}
function Mt(a){Kt(a);var b=Gt(w.location.href,!0,!1);b.length||(b=Gt(w.document.referrer,!1,!0));a=a||{};Nt(a);if(b.length){var c=b[0],d=Gb(),e=Sq(a,d,!0),f=jt(),g=function(){kt(f)&&e.expires!==void 0&&Uq("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ca.get()},expires:Number(e.expires)})};wl(function(){g();kt(f)||xl(g,f)},f)}}
function Nt(a){var b;if(b=Xa(15)){var c=Ot();b=ht.test(c)||it.test(c)||Pt()}if(b){var d;a:{for(var e=sj(w.location.href),f=kj(mj(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!ct[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Zs(n),r;if(q)c:{var u=q;if(u&&u.length!==0){var t=0;try{for(var v=10;t<u.length&&!(v--<=0);){var x=$s(u,t);if(x===void 0)break;var y=l(x),A=y.next().value,D=y.next().value,E=A,J=D,F=E&7;if(E>>3===16382){if(F!==0)break;
var M=$s(u,J);if(M===void 0)break;r=l(M).next().value===1;break c}var U;d:{var ha=void 0,S=u,aa=J;switch(F){case 0:U=(ha=$s(S,aa))==null?void 0:ha[1];break d;case 1:U=aa+8;break d;case 2:var ta=$s(S,aa);if(ta===void 0)break;var la=l(ta),ea=la.next().value;U=la.next().value+ea;break d;case 5:U=aa+4;break d}U=void 0}if(U===void 0||U>u.length||U<=t)break;t=U}}catch(ka){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Z=d;Z&&Qt(Z,7,a)}}
function Qt(a,b,c){c=c||{};var d=Gb(),e=Sq(c,d,!0),f=jt(),g=function(){if(kt(f)&&e.expires!==void 0){var h=Dt()||[];xt(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ca:Bt(b)},!0);Uq("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ca?m.Ca.get():0},expires:Number(m.expires)}}))}};wl(function(){kt(f)?g():xl(g,f)},f)}
function Lt(a,b,c,d,e){c=c||{};e=e||[];var f=qt(c.prefix),g=d||Gb(),h=Math.round(g/1E3),m=jt(),n=!1,p=!1,q=function(){if(kt(m)){var r=Sq(c,g,!0);r.wc=m;for(var u=function(U,ha){var S=rt(U,f);S&&(Fr(S,ha,r),U!=="gb"&&(n=!0))},t=function(U){var ha=["GCL",h,U];e.length>0&&ha.push(e.join("."));return ha.join(".")},v=l(["aw","dc","gf","ha","gp"]),x=v.next();!x.done;x=v.next()){var y=x.value;a[y]&&u(y,t(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],D=rt("gb",f);!b&&nt(D).some(function(U){return U.gclid===A&&U.labels&&
U.labels.length>0})||u("gb",t(A))}}if(!p&&a.gbraid&&kt("ad_storage")&&(p=!0,!n)){var E=a.gbraid,J=rt("ag",f);if(b||!st(J).some(function(U){return U.gclid===E&&U.labels&&U.labels.length>0})){var F={},M=(F.k=E,F.i=""+h,F.b=e,F);Vs(J,M,5,c,g)}}Rt(a,f,g,c)};wl(function(){q();kt(m)||xl(q,m)},m)}
function Rt(a,b,c,d){if(a.gad_source!==void 0&&kt("ad_storage")){var e=gd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=rt("gs",b);if(g){var h=Math.floor((Gb()-(fd()||0))/1E3),m,n=at(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Vs(g,m,5,d,c)}}}}
function St(a,b){var c=fs(!0);lt(function(){for(var d=qt(b.prefix),e=0;e<a.length;++e){var f=a[e];if(gt[f]!==void 0){var g=rt(f,d),h=c[g];if(h){var m=Math.min(Tt(h),Gb()),n;b:{for(var p=m,q=tr(g,z.cookie,void 0,jt()),r=0;r<q.length;++r)if(Tt(q[r])>p){n=!0;break b}n=!1}if(!n){var u=Sq(b,m,!0);u.wc=jt();Fr(g,h,u)}}}}Lt(It(c.gclid,c.gclsrc),!1,b)},jt())}
function Ut(a){var b=["ag"],c=fs(!0),d=qt(a.prefix);lt(function(){for(var e=0;e<b.length;++e){var f=rt(b[e],d);if(f){var g=c[f];if(g){var h=Ns(g,5);if(h){var m=vt(h);m||(m=Gb());var n;a:{for(var p=m,q=Rs(f,5),r=0;r<q.length;++r)if(vt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Vs(f,h,5,a,m)}}}}},["ad_storage"])}function rt(a,b){var c=gt[a];if(c!==void 0)return b+c}function Tt(a){return Vt(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function vt(a){return a?(Number(a.i)||0)*1E3:0}function wt(a){var b=Vt(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Vt(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!et.test(a[2])?[]:a}
function Wt(a,b,c,d,e){if(Array.isArray(b)&&or(w)){var f=qt(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=rt(a[m],f);if(n){var p=tr(n,z.cookie,void 0,jt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};lt(function(){ms(g,b,c,d)},jt())}}
function Xt(a,b,c,d){if(Array.isArray(a)&&or(w)){var e=["ag"],f=qt(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=rt(e[m],f);if(!n)return{};var p=Rs(n,5);if(p.length){var q=p.sort(function(r,u){return vt(u)-vt(r)})[0];h[n]=Os(q,5)}}return h};lt(function(){ms(g,a,b,c)},["ad_storage"])}}function zt(a){return a.filter(function(b){return et.test(b.gclid)})}
function Yt(a,b){if(or(w)){for(var c=qt(b.prefix),d={},e=0;e<a.length;e++)gt[a[e]]&&(d[a[e]]=gt[a[e]]);lt(function(){zb(d,function(f,g){var h=tr(c+g,z.cookie,void 0,jt());h.sort(function(u,t){return Tt(t)-Tt(u)});if(h.length){var m=h[0],n=Tt(m),p=Vt(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Vt(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Lt(q,!0,b,n,p)}})},jt())}}
function Zt(a){var b=["ag"],c=["gbraid"];lt(function(){for(var d=qt(a.prefix),e=0;e<b.length;++e){var f=rt(b[e],d);if(!f)break;var g=Rs(f,5);if(g.length){var h=g.sort(function(q,r){return vt(r)-vt(q)})[0],m=vt(h),n=h.b,p={};p[c[e]]=h.k;Lt(p,!0,a,m,n)}}},["ad_storage"])}function $t(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function au(a){function b(h,m,n){n&&(h[m]=n)}if(tl()){var c=Jt(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:fs(!1)._gs);if($t(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ns(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ns(function(){return g},1)}}}function Pt(){var a=sj(w.location.href);return mj(a,"query",!1,void 0,"gad_source")}
function bu(a){if(!Xa(1))return null;var b=fs(!0).gad_source;if(b!=null)return w.location.hash="",b;if(Xa(2)){b=Pt();if(b!=null)return b;var c=Jt();if($t(c,a))return"0"}return null}function cu(a){var b=bu(a);b!=null&&ns(function(){var c={};return c.gad_source=b,c},4)}function du(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function eu(a,b,c,d){var e=[];c=c||{};if(!kt(jt()))return e;var f=nt(a),g=du(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Sq(c,p,!0);r.wc=jt();Fr(a,q,r)}return e}
function fu(a,b){var c=[];b=b||{};var d=pt(b),e=du(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=qt(b.prefix),n=rt(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,u=p.labels,t=p.timestamp,v=Math.round(t/1E3);if(h.type==="ag"){var x={},y=(x.k=r,x.i=""+v,x.b=(u||[]).concat([a]),x);Vs(n,y,5,b,t)}else if(h.type==="gb"){var A=[q,v,r].concat(u||[],[a]).join("."),D=Sq(b,t,!0);D.wc=jt();Fr(n,A,D)}}return c}
function gu(a,b){var c=qt(b),d=rt(a,c);if(!d)return 0;var e;e=a==="ag"?st(d):nt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function hu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function iu(a){var b=Math.max(gu("aw",a),hu(kt(jt())?Fs():{})),c=Math.max(gu("gb",a),hu(kt(jt())?Fs("_gac_gb",!0):{}));c=Math.max(c,gu("ag",a));return c>b}
function Ot(){return z.referrer?mj(sj(z.referrer),"host"):""};
var ju=function(a,b){b=b===void 0?!1:b;var c=Tn("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},ku=function(a){return tj(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},qu=function(a,b,c,d,e){var f=qt(a.prefix);if(ju(f,!0)){var g=Jt(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=lu(),r=q.De,u=q.Qm;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Yb:p});n&&h.push({gclid:n,Yb:"ds"});h.length===2&&P(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Yb:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Yb:"aw.ds"});mu(function(){var t=wn(nu());if(t){ts(a);var v=[],x=t?rs[us(a.prefix)]:void 0;x&&v.push("auid="+x);if(wn(N.m.W)){e&&v.push("userId="+e);var y=Ml(Il.X.rm);if(y===void 0)Ll(Il.X.sm,!0);else{var A=Ml(Il.X.kh);v.push("ga_uid="+A+"."+y)}}var D=Ot(),E=t||!d?h:[];E.length===0&&(ht.test(D)||it.test(D))&&E.push({gclid:"",Yb:""});if(E.length!==0||r!==void 0){D&&v.push("ref="+encodeURIComponent(D));var J=ou();v.push("url="+
encodeURIComponent(J));v.push("tft="+Gb());var F=fd();F!==void 0&&v.push("tfd="+Math.round(F));var M=Wp(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));u!==void 0&&v.push("gad_source_src="+encodeURIComponent(u.toString()));if(!c){var U={};c=Yo(Oo(new No(0),(U[N.m.Ob]=Dp.C[N.m.Ob],U)))}v.push("gtm="+Rq({Pa:b}));Dq()&&v.push("gcs="+Eq());v.push("gcd="+Iq(c));Lq()&&v.push("dma_cps="+Jq());v.push("dma="+Kq());Cq(c)?v.push("npa=0"):v.push("npa=1");Nq()&&v.push("_ng=1");gq(oq())&&
v.push("tcfd="+Mq());var ha=vq();ha&&v.push("gdpr="+ha);var S=uq();S&&v.push("gdpr_consent="+S);G(23)&&v.push("apve=0");G(123)&&fs(!1)._up&&v.push("gtm_up=1");var aa=Wj();aa&&v.push("tag_exp="+aa);if(E.length>0)for(var ta=0;ta<E.length;ta++){var la=E[ta],ea=la.gclid,Z=la.Yb;if(!pu(a.prefix,Z+"."+ea,x!==void 0)){var ka=Fi(36)+"?"+v.join("&");ea!==""?ka=Z==="gb"?ka+"&wbraid="+ea:ka+"&gclid="+ea+"&gclsrc="+Z:Z==="aw.ds"&&(ka+="&gclsrc=aw.ds");Xc(ka)}}else if(r!==void 0&&!pu(a.prefix,"gad",x!==void 0)){var za=
Fi(36)+"?"+v.join("&");Xc(za)}}}})}},pu=function(a,b,c){var d=Tn("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},lu=function(){var a=sj(w.location.href),b=void 0,c=void 0,d=mj(a,"query",!1,void 0,"gad_source"),e=mj(a,"query",!1,void 0,"gad_campaignid"),f,g=a.hash.replace("#","").match(ru);f=g?g[1]:void 0;d&&f?(b=d,c=1):d?(b=d,c=2):f&&(b=f,c=3);return{De:b,Qm:c,rh:e}},ou=function(){var a=Wp(!1)===1?w.top.location.href:w.location.href;return a=a.replace(/[\?#].*$/,
"")},su=function(a){var b=[];zb(a,function(c,d){d=zt(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},tu=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=uj("gcl"+a);if(d)return d.split(".")}var e=qt(b);if(e==="_gcl"){var f=!wn(nu())&&c,g;g=Jt()[a]||[];if(g.length>0)return f?["0"]:g}var h=rt(a,e);return h?mt(h):[]},mu=function(a){var b=nu();Kn(function(){a();wn(b)||xl(a,b)},b)},nu=function(){return[N.m.aa,N.m.W]},ru=/^gad_source[_=](\d+)$/;
function uu(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function vu(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function wu(){return["ad_storage","ad_user_data"]}function xu(a){if(G(38)&&!Ml(Il.X.dm)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{uu(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Ll(Il.X.dm,function(d){d.gclid&&Qt(d.gclid,5,a)}),vu(c)||P(178))})}catch(c){P(177)}};wl(function(){kt(wu())?b():xl(b,wu())},wu())}};var yu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function zu(a){return a.data.action!=="gcl_transfer"?(P(173),!0):a.data.gadSource?a.data.gclid?!1:(P(181),!0):(P(180),!0)}
function Au(a,b){if(G(a)){if(Ml(Il.X.we))return P(176),Il.X.we;if(Ml(Il.X.gm))return P(170),Il.X.we;var c=Rp();if(!c)P(171);else if(c.opener){var d=function(g){if(!yu.includes(g.origin))P(172);else if(!zu(g)){var h={gadSource:g.data.gadSource};G(229)&&(h.gclid=g.data.gclid);Ll(Il.X.we,h);a===200&&g.data.gclid&&Qt(String(g.data.gclid),6,b);var m;(m=g.stopImmediatePropagation)==null||m.call(g);Zp(c,"message",d)}};if(Yp(c,"message",d)){Ll(Il.X.gm,!0);for(var e=l(yu),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},
f.value);P(174);return Il.X.we}P(175)}}};
var Bu=function(a){var b={prefix:Q(a.D,N.m.tb)||Q(a.D,N.m.Wa),domain:Q(a.D,N.m.ub),Rc:Q(a.D,N.m.wb),flags:Q(a.D,N.m.Bb)};a.D.isGtmEvent&&(b.path=Q(a.D,N.m.Rb));return b},Du=function(a,b){var c,d,e,f,g,h,m,n;c=a.Ae;d=a.Fe;e=a.Je;f=a.Pa;g=a.D;h=a.Ge;m=a.As;n=a.un;Cu({Ae:c,Fe:d,Je:e,Oc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,qu(b,f,g,h,n))},Fu=function(a,b){if(!T(a,R.A.xe)){var c=Au(119);if(c){var d=Ml(c),e=function(g){V(a,R.A.xe,!0);var h=Eu(a,N.m.Qe),m=Eu(a,N.m.Re);W(a,N.m.Qe,String(g.gadSource));
W(a,N.m.Re,6);V(a,R.A.ia);V(a,R.A.Sf);W(a,N.m.ia);b();W(a,N.m.Qe,h);W(a,N.m.Re,m);V(a,R.A.xe,!1)};if(d)e(d);else{var f=void 0;f=Ol(c,function(g,h){e(h);Pl(c,f)})}}}},Cu=function(a){var b,c,d,e;b=a.Ae;c=a.Fe;d=a.Je;e=a.Oc;b&&(ps(c[N.m.rf],!!c[N.m.oa])&&(St(Gu,e),Ut(e),Cs(e)),Wp()!==2?(Mt(e),xu(e),Au(200,e)):Kt(e),Yt(Gu,e),Zt(e));c[N.m.oa]&&(Wt(Gu,c[N.m.oa],c[N.m.kd],!!c[N.m.Hc],e.prefix),Xt(c[N.m.oa],c[N.m.kd],!!c[N.m.Hc],e.prefix),Ds(us(e.prefix),c[N.m.oa],c[N.m.kd],!!c[N.m.Hc],e),Ds("FPAU",c[N.m.oa],
c[N.m.kd],!!c[N.m.Hc],e));d&&(G(101)?au(Hu):au(Iu));cu(Iu)},Ju=function(a,b){Array.isArray(b)||(b=[b]);var c=T(a,R.A.ba);return b.indexOf(c)>=0},Gu=["aw","dc","gb"],Iu=["aw","dc","gb","ag"],Hu=["aw","dc","gb","ag","gad_source"];function Ku(a){var b=Q(a.D,N.m.Gc),c=Q(a.D,N.m.Fc);b&&!c?(a.eventName!==N.m.na&&a.eventName!==N.m.Yd&&P(131),a.isAborted=!0):!b&&c&&(P(132),a.isAborted=!0)}
function Lu(a){var b=wn(N.m.aa)?Sn.pscdl:"denied";b!=null&&W(a,N.m.Gg,b)}function Mu(a){var b=Wp(!0);W(a,N.m.Ec,b)}function Nu(a){Nq()&&W(a,N.m.je,1)}function Ou(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&lj(a.substring(0,b))===void 0;)b--;return lj(a.substring(0,b))||""}function Pu(a){Qu(a,lo.Gf.Hn,Q(a.D,N.m.wb))}function Qu(a,b,c){Eu(a,N.m.vd)||W(a,N.m.vd,{});Eu(a,N.m.vd)[b]=c}function Ru(a){V(a,R.A.Rf,hl.Z.Ga)}
function Su(a){var b=a.D.getMergedValues(N.m.Dc);b&&a.mergeHitDataForKey(N.m.Dc,b)}function Tu(a,b){b=b===void 0?!1:b;var c=T(a,R.A.Qf),d=Uu(a,"custom_event_accept_rules",!1)&&!b;if(c){var e=c.indexOf(a.target.destinationId)>=0,f=!0;G(240)&&T(a,R.A.Pl)&&(f=T(a,R.A.ab)===Fj());e&&f?V(a,R.A.Hh,!0):(V(a,R.A.Hh,!1),d||(a.isAborted=!0));G(240)&&(a.hasBeenAccepted()?a.isAborted=!0:T(a,R.A.Hh)&&a.accept())}}
function Vu(a){lk&&(gm=!0,a.eventName===N.m.na?mm(a.D,a.target.id):(T(a,R.A.Ne)||(jm[a.target.id]=!0),ao(T(a,R.A.ab))))}function Wu(a){};var Xu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Yu=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Zu=/^\d+\.fls\.doubleclick\.net$/,$u=/;gac=([^;?]+)/,av=/;gacgb=([^;?]+)/;
function bv(a,b){if(Zu.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(Xu)?lj(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function cv(a,b,c){for(var d=kt(jt())?Fs("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=eu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{Zp:f?e.join(";"):"",Yp:bv(d,av)}}function dv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Yu)?b[1]:void 0}
function ev(a){var b={},c,d,e;Zu.test(z.location.host)&&(c=dv("gclgs"),d=dv("gclst"),e=dv("gcllp"));if(c&&d&&e)b.Yf=c,b.th=d,b.sh=e;else{var f=Gb(),g=st((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Qc});h.length>0&&m.length>0&&n.length>0&&(b.Yf=h.join("."),b.th=m.join("."),b.sh=n.join("."))}return b}
function fv(a,b,c,d){d=d===void 0?!1:d;if(Zu.test(z.location.host)){var e=dv(c);if(e){if(d){var f=new Xs;Ys(f,2);Ys(f,3);return e.split(".").map(function(h){return{gclid:h,Ca:f,cb:[1]}})}return e.split(".").map(function(h){return{gclid:h,Ca:new Xs,cb:[1]}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Et(g):nt(g)}if(b==="wbraid")return nt((a||"_gcl")+"_gb");if(b==="braids")return pt({prefix:a})}return[]}function gv(a){return Zu.test(z.location.host)?!(dv("gclaw")||dv("gac")):iu(a)}
function hv(a,b,c){var d;d=c?fu(a,b):eu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function nv(){return Tn("dedupe_gclid",function(){return Mr()})};function tv(a,b,c,d){var e=Mc(),f;if(e===1)a:{var g=Fi(3);g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==w.location.protocol?a:b)+c};var Fv=[N.m.aa,N.m.W];var Kv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Lv=/^www.googleadservices.com$/;function Mv(a){a||(a=Nv());return a.Ar?!1:a.oq||a.qq||a.sq||a.rq||a.De||a.rh||a.aq||a.Yb==="aw.ds"||G(235)&&a.Yb==="aw.dv"||a.gq?!0:!1}
function Nv(){var a={},b=fs(!0);a.Ar=!!b._up;var c=Jt(),d=lu();a.oq=c.aw!==void 0;a.qq=c.dc!==void 0;a.sq=c.wbraid!==void 0;a.rq=c.gbraid!==void 0;a.Yb=typeof c.gclsrc==="string"?c.gclsrc:void 0;a.De=d.De;a.rh=d.rh;var e=z.referrer?mj(sj(z.referrer),"host"):"";a.gq=Kv.test(e);a.aq=Lv.test(e);return a};var Ov=function(a,b,c){var d={};a.mergeHitDataForKey(N.m.Ni,(d[b]=c,d))},Pv=function(a,b){var c=Uu(a,N.m.Lg,a.D.M[N.m.Lg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},Qv=function(a){var b=T(a,R.A.Sa);if(sd(b))return b},Rv=function(a){if(T(a,R.A.Bd)||!dk(a.D))return!1;if(!Q(a.D,N.m.rd)){var b=Q(a.D,N.m.fe);return b===!0||b==="true"}return!0},Sv=function(a){return Uu(a,N.m.ke,Q(a.D,N.m.ke))||!!Uu(a,"google_ng",!1)};var lg;function Tv(){var a=data.permissions||{};lg=new rg(Fi(5),a)};var Uv=Number(Ji(57))||5,Vv=Number(Ji(58))||50,Wv=wb();
var Yv=function(a,b){a&&(Xv("sid",a.targetId,b),Xv("cc",a.clientCount,b),Xv("tl",a.totalLifeMs,b),Xv("hc",a.heartbeatCount,b),Xv("cl",a.clientLifeMs,b))},Xv=function(a,b,c){b!=null&&c.push(a+"="+b)},Zv=function(){var a=z.referrer;if(a){var b;return mj(sj(a),"host")===((b=w.location)==null?void 0:b.host)?1:2}return 0},$v="https://"+Fi(21)+"/a?",bw=function(){this.U=aw;this.M=0};bw.prototype.H=function(a,b,c,d){var e=Zv(),f,g=[];f=w===w.top&&e!==0&&
b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&Xv("si",a.ig,g);Xv("m",0,g);Xv("iss",f,g);Xv("if",c,g);Yv(b,g);d&&Xv("fm",encodeURIComponent(d.substring(0,Vv)),g);this.R(g);};bw.prototype.C=function(a,b,c,d,e){var f=[];Xv("m",1,f);Xv("s",a,f);Xv("po",Zv(),f);b&&(Xv("st",b.state,f),Xv("si",b.ig,f),Xv("sm",b.pg,f));Yv(c,f);Xv("c",d,f);e&&Xv("fm",encodeURIComponent(e.substring(0,Vv)),f);this.R(f);
};bw.prototype.R=function(a){a=a===void 0?[]:a;!jk||this.M>=Uv||(Xv("pid",Wv,a),Xv("bc",++this.M,a),a.unshift("ctid="+Fi(5)+"&t=s"),this.U(""+$v+a.join("&")))};function cw(a){return a.performance&&a.performance.now()||Date.now()}
var dw=function(a,b){var c=w,d;var e=function(f,g,h){h=h===void 0?{Ym:function(){},Zm:function(){},Xm:function(){},onFailure:function(){}}:h;this.tp=f;this.C=g;this.M=h;this.ja=this.ma=this.heartbeatCount=this.rp=0;this.ih=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.ig=cw(this.C);this.pg=cw(this.C);this.U=10};e.prototype.init=function(){this.R(1);this.Ja()};e.prototype.getState=function(){return{state:this.state,
ig:Math.round(cw(this.C)-this.ig),pg:Math.round(cw(this.C)-this.pg)}};e.prototype.R=function(f){this.state!==f&&(this.state=f,this.pg=cw(this.C))};e.prototype.ym=function(){return String(this.rp++)};e.prototype.Ja=function(){var f=this;this.heartbeatCount++;this.Za({type:0,clientId:this.id,requestId:this.ym(),maxDelay:this.jh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ja++,g.isDead||f.ja>20){var m=g.isDead&&g.failure.failureType;
f.U=m||10;f.R(4);f.qp();var n,p;(p=(n=f.M).Xm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.R(3),f.Dm();else{if(f.heartbeatCount>g.stats.heartbeatCount+20){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.M).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var u=f.state;f.R(2);if(u!==2)if(f.ih){var t,v;(v=(t=f.M).Zm)==null||v.call(t)}else{f.ih=!0;var x,y;(y=(x=f.M).Ym)==null||y.call(x)}f.ja=0;f.up();f.Dm()}}})};e.prototype.jh=function(){return this.state===2?
5E3:500};e.prototype.Dm=function(){var f=this;this.C.setTimeout(function(){f.Ja()},Math.max(0,this.jh()-(cw(this.C)-this.ma)))};e.prototype.yp=function(f,g,h){var m=this;this.Za({type:1,clientId:this.id,requestId:this.ym(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,u={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},t,v;(v=(t=m.M).onFailure)==null||v.call(t,u);h(u)}})};e.prototype.Za=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.U},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var u=h.H[n];u&&h.Nf(u,7)},(p=f.maxDelay)!=null?p:5E3),r={request:f,nn:g,hn:m,Gq:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ma=cw(this.C);f.hn=!1;this.tp(f.request)};e.prototype.up=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.hn&&this.sendRequest(h)}};e.prototype.qp=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Nf(this.H[g.value],this.U)};e.prototype.Nf=function(f,g){this.Kc(f);var h=f.request;h.failure={failureType:g};f.nn(h)};e.prototype.Kc=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Gq)};e.prototype.mq=function(f){this.ma=cw(this.C);var g=this.H[f.requestId];if(g)this.Kc(g),g.nn(f);else{var h,m;(m=(h=this.M).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var ew;
var fw=function(){ew||(ew=new bw);return ew},aw=function(a){Fl(Hl(hl.Z.Jc),function(){Pc(a)})},gw=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},hw=function(a){var b=a,c=Hi.ma;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},iw=function(a){var b=Ml(Il.X.lm);return b&&b[a]},jw=function(a,
b,c,d,e){var f=this;this.H=d;this.U=this.R=!1;this.ja=null;this.initTime=c;this.C=15;this.M=this.Np(a);w.setTimeout(function(){f.initialize()},1E3);Sc(function(){f.xq(a,b,e)})};k=jw.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),ig:this.initTime,pg:Math.round(Gb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.M.yp(a,b,c)};k.getState=function(){return this.M.getState().state};k.xq=function(a,b,c){var d=w.location.origin,e=this,
f=Nc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?gw(h):"",p;G(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Nc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ja=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.M.mq(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Np=function(a){var b=this,c=dw(function(d){var e;(e=b.ja)==null||e.postMessage(d,a.origin)},{Ym:function(){b.R=!0;b.H.H(c.getState(),c.stats)},Zm:function(){},Xm:function(d){b.R?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.U||this.M.init();this.U=!0};function kw(){var a=og(lg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function lw(a,b){var c=Math.round(Gb());b=b===void 0?!1:b;var d=w.location.origin;if(!d||!kw()||G(168))return;fj()&&!a&&(a=""+d+ej()+"/_/service_worker");var e=hw(a);if(e===null||iw(e.origin))return;if(!Ac()){fw().H(void 0,void 0,6);return}var f=new jw(e,!!a,c||Math.round(Gb()),fw(),b);Nl(Il.X.lm,{})[e.origin]=f;}
var mw=function(a,b,c,d){var e;if((e=iw(a))==null||!e.delegate){var f=Ac()?16:6;fw().C(f,void 0,void 0,b.commandType);d({failureType:f});return}iw(a).delegate(b,c,d);};
function nw(a,b,c,d,e){var f=hw();if(f===null){d(Ac()?16:6);return}var g,h=(g=iw(f.origin))==null?void 0:g.initTime,m=Math.round(Gb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);mw(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function ow(a,b,c,d){var e=hw(a);if(e===null){d("_is_sw=f"+(Ac()?16:6)+"te");return}var f=b?1:0,g=Math.round(Gb()),h,m=(h=iw(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;G(169)&&(p=!0);mw(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:w.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,u,t=(u=iw(e.origin))==
null?void 0:u.getState();t!==void 0&&(r+="s"+t);d(n?r+("t"+n):r+"te")});};function pw(a){if(G(10))return;var b=fj()||Hi.C||!!dk(a.D);G(245)&&(b=Hi.C||!!dk(a.D));if(b||G(168))return;lw(void 0,G(131));};function qw(){var a;a=a===void 0?document:a;var b;return!((b=a.featurePolicy)==null||!b.allowedFeatures().includes("attribution-reporting"))};var Cw=function(a){this.C=1;this.C>0||(this.C=1);this.onSuccess=a.D.onSuccess},Dw=function(a,b){return Qb(function(){a.C--;if(qb(a.onSuccess)&&a.C===0)a.onSuccess()},b>0?b:1)};var Ew=function(){var a;G(90)&&ym()!==""&&(a=ym());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Fw=function(){var a="www";G(90)&&ym()&&(a=ym());return"https://"+a+".google-analytics.com/g/collect"};function Gw(a,b){var c=!!fj();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ej()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?G(90)&&ym()?Ew():""+ej()+"/ag/g/c":Ew();case 16:return c?G(90)&&ym()?Fw():""+ej()+"/ga/g/c":Fw();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
ej()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ej()+"/d/pagead/form-data":G(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.zp+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 66:return"https://www.google.com/pagead/uconversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 63:return"https://www.googleadservices.com/pagead/conversion";case 64:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 65:return"https://www.google.com/pagead/1p-conversion";case 22:return c?ej()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ej()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";
case 23:return c?ej()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?ej()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return G(205)?"https://www.google.com/measurement/conversion/":c?ej()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?ej()+"/d/ccm/form-data":G(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:rc(a,"Unknown endpoint")}};function Iw(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var Jw="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Kw="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Lw(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Mw(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Mw(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Nw(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Ow(){this.blockSize=-1};function Pw(a,b){this.blockSize=-1;this.blockSize=64;this.M=Ga.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.H=0;this.C=[];this.ja=a;this.U=b;this.ma=Ga.Int32Array?new Int32Array(64):Array(64);Qw===void 0&&(Ga.Int32Array?Qw=new Int32Array(Rw):Qw=Rw);this.reset()}Ha(Pw,Ow);for(var Sw=[],Tw=0;Tw<63;Tw++)Sw[Tw]=0;var Uw=[].concat(128,Sw);
Pw.prototype.reset=function(){this.R=this.H=0;var a;if(Ga.Int32Array)a=new Int32Array(this.U);else{var b=this.U,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Vw=function(a){for(var b=a.M,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,u=a.C[5]|0,t=a.C[6]|0,v=a.C[7]|0,x=0;x<64;x++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&u^~r&t)+(Qw[x]|0)|0)+(c[x]|0)|0)|0;v=t;t=u;u=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+u|0;a.C[6]=a.C[6]+t|0;a.C[7]=a.C[7]+v|0};
Pw.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.M[d++]=a.charCodeAt(c++),d==this.blockSize&&(Vw(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.M[d++]=g;d==this.blockSize&&(Vw(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.R+=b};Pw.prototype.digest=function(){var a=[],b=this.R*8;this.H<56?this.update(Uw,56-this.H):this.update(Uw,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.M[c]=b&255,b/=256;Vw(this);for(var d=0,e=0;e<this.ja;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Rw=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Qw;function Ww(){Pw.call(this,8,Xw)}Ha(Ww,Pw);var Xw=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Yw=/^[0-9A-Fa-f]{64}$/;function Zw(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function $w(a){var b=w;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Yw.test(a))return Promise.resolve(a);try{var d=Zw(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return ax(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function ax(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};
var dx=function(a,b){var c=G(178),d=["tv.1"],e=["tvd.1"],f=bx(a);if(f)return d.push(f),{jb:!1,Jj:d.join("~"),qg:{},Kd:c?e.join("~"):void 0};var g={},h=0;var m=0,n=cx(a,function(u,t,v){m++;var x=u.value,y;if(v){var A=t+"__"+h++;y="${userData."+A+"|sha256}";g[A]=x}else y=encodeURIComponent(encodeURIComponent(x));u.index!==void 0&&(t+=u.index);d.push(t+"."+y);if(c){var D=Nw(m,t,u.metadata);D&&e.push(D)}}).jb,p=e.join("~");
var q=d.join("~"),r={userData:g};return b===2?{jb:n,Jj:q,qg:r,Tp:"tv.1~${"+(q+"|encrypt}"),encryptionKeyString:Fi(43),Kd:c?p:void 0}:{jb:n,Jj:q,qg:r,Kd:c?p:void 0}},fx=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=ex(a);return cx(b,function(){}).jb},cx=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=gx[g.name];if(h){var m=hx(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{jb:d,mj:c}},hx=function(a){var b=ix(a.name),
c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(jx.test(e)||Yw.test(e))}return d},ix=function(a){return kx.indexOf(a)!==-1},nx=function(a){if(w.Promise){var b=void 0;return b}},rx=function(a,b,c){if(w.Promise)try{var d=ex(a),e=ox(d).then(px);return e}catch(g){}},tx=function(a){try{return px(sx(ex(a)))}catch(b){}},mx=function(a){var b=void 0;
return b},px=function(a){var b=G(178),c=a.Sc,d=["tv.1"],e=["tvd.1"],f=bx(c);if(f)return d.push(f),{fc:d.join("~"),mj:!1,jb:!1,lj:!0,Kd:b?e.join("~"):void 0};var g=c.filter(function(q){return!hx(q)}),h=0,m=cx(g,function(q,r){h++;var u=q.value,t=q.index;t!==void 0&&(r+=t);d.push(r+"."+u);if(b){var v=Nw(h,r,q.metadata);v&&e.push(v)}}),n=m.mj,p=m.jb;return{fc:encodeURIComponent(d.join("~")),mj:n,jb:p,lj:!1,Kd:b?e.join("~"):void 0}},bx=function(a){if(a.length===1&&a[0].name==="error_code")return gx.error_code+
"."+a[0].value},qx=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(gx[d.name]&&d.value)return!0}return!1},ex=function(a){function b(u,t,v,x,y){var A=ux(u);if(A!=="")if(Yw.test(A)){y&&(y.isPreHashed=!0);var D={name:t,value:A,index:x};y&&(D.metadata=y);m.push(D)}else{var E=v(A),J={name:t,value:E,index:x};y&&(J.metadata=y,E&&(y.rawLength=String(A).length,y.normalizedLength=E.length));m.push(J)}}function c(u,t){var v=u;if(sb(v)||
Array.isArray(v)){v=ub(u);for(var x=0;x<v.length;++x){var y=ux(v[x]),A=Yw.test(y);t&&!A&&P(89);!t&&A&&P(88)}}}function d(u,t){var v=u[t];c(v,!1);var x=vx[t];u[x]&&(u[t]&&P(90),v=u[x],c(v,!0));return v}function e(u,t,v,x){var y=u._tag_metadata||{},A=u[t],D=y[t];c(A,!1);var E=vx[t];if(E){var J=u[E],F=y[E];J&&(A&&P(90),A=J,D=F,c(A,!0))}if(x!==void 0)b(A,t,v,x,D);else{A=ub(A);D=ub(D);for(var M=0;M<A.length;++M)b(A[M],t,v,void 0,D[M])}}function f(u,t,v){if(G(178))e(u,t,v,void 0);else for(var x=ub(d(u,
t)),y=0;y<x.length;++y)b(x[y],t,v)}function g(u,t,v,x){if(G(178))e(u,t,v,x);else{var y=d(u,t);b(y,t,v,x)}}function h(u){return function(t){P(64);return u(t)}}var m=[];if(w.location.protocol!=="https:")return m.push({name:"error_code",value:"e3",index:void 0}),m;f(a,"email",wx);f(a,"phone_number",xx);f(a,"first_name",h(yx));f(a,"last_name",h(yx));var n=a.home_address||{};f(n,"street",h(zx));f(n,"city",h(zx));f(n,"postal_code",h(Ax));f(n,"region",h(zx));f(n,"country",h(Ax));for(var p=ub(a.address||
{}),q=0;q<p.length;q++){var r=p[q];g(r,"first_name",yx,q);g(r,"last_name",yx,q);g(r,"street",zx,q);g(r,"city",zx,q);g(r,"postal_code",Ax,q);g(r,"region",zx,q);g(r,"country",Ax,q)}return m},Bx=function(a){var b=a?ex(a):[];return px({Sc:b})},Cx=function(a){return a&&a!=null&&Object.keys(a).length>0&&w.Promise?ex(a).some(function(b){return b.value&&ix(b.name)&&!Yw.test(b.value)}):!1},ux=function(a){return a==null?"":sb(a)?Eb(String(a)):"e0"},Ax=function(a){return a.replace(Dx,"")},yx=function(a){return zx(a.replace(/\s/g,
""))},zx=function(a){return Eb(a.replace(Ex,"").toLowerCase())},xx=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Fx.test(a)?a:"e0"},wx=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Gx.test(c))return c}return"e0"},sx=function(a){try{return a.forEach(function(b){if(b.value&&ix(b.name)){var c;var d=b.value,e=w;if(d===""||d==="e0"||Yw.test(d))c=d;else try{var f=new Ww;
f.update(Zw(d));c=ax(f.digest(),e)}catch(g){c="e2"}b.value=c}}),{Sc:a}}catch(b){return{Sc:[]}}},ox=function(a){return a.some(function(b){return b.value&&ix(b.name)})?w.Promise?Promise.all(a.map(function(b){return b.value&&ix(b.name)?$w(b.value).then(function(c){b.value=c}):Promise.resolve()})).then(function(){return{Sc:a}}).catch(function(){return{Sc:[]}}):Promise.resolve({Sc:[]}):Promise.resolve({Sc:a})},Ex=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Gx=/^\S+@\S+\.\S+$/,Fx=/^\+\d{10,15}$/,Dx=/[.~]/g,
jx=/^[0-9A-Za-z_-]{43}$/,Hx={},gx=(Hx.email="em",Hx.phone_number="pn",Hx.first_name="fn",Hx.last_name="ln",Hx.street="sa",Hx.city="ct",Hx.region="rg",Hx.country="co",Hx.postal_code="pc",Hx.error_code="ec",Hx),Ix={},vx=(Ix.email="sha256_email_address",Ix.phone_number="sha256_phone_number",Ix.first_name="sha256_first_name",Ix.last_name="sha256_last_name",Ix.street="sha256_street",Ix);var kx=Object.freeze(["email","phone_number","first_name","last_name","street"]);
function Jx(a,b){b&&zb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};function Kx(a,b){var c=Eu(a,N.m.Dc);if(c&&typeof c==="object")for(var d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value,g=c[f];g!==void 0&&(g===null&&(g=""),b["gap."+f]=String(g))}};
var Mx=function(a){for(var b={},c=function(p,q){b[p]=q===!0?"1":q===!1?"0":encodeURIComponent(String(q))},d=l(Object.keys(a.C)),e=d.next();!e.done;e=d.next()){var f=e.value,g=Eu(a,f);if(f.indexOf("_&")===0)c(f.substring(2),g);else{var h=Lx[f];h&&g!==void 0&&g!==""&&(!T(a,R.A.ye)||f!==N.m.Zc&&f!==N.m.gd&&f!==N.m.ce&&f!==N.m.Se||(g="0"),c(h,g))}}c("gtm",Rq({Pa:T(a,R.A.ab),Qd:a.D.isGtmEvent}));Dq()&&c("gcs",Eq());c("gcd",Iq(a.D));Lq()&&c("dma_cps",Jq());c("dma",Kq());gq(oq())&&c("tcfd",Mq());var m=jo(a);
m&&c("tag_exp",m);Xj()&&c("ptag_exp",Xj());if(T(a,R.A.tg)){c("tft",Gb());var n=fd();n!==void 0&&c("tfd",Math.round(n))}G(24)&&c("apve","1");(G(25)||G(26))&&c("apvf",cd()?G(26)?"f":"sb":"nf");zl[hl.Z.Ga]!==gl.Ka.ue||Cl[hl.Z.Ga].isConsentGranted()||c("limited_ads",!0);Kx(a,b);return b},Nx=function(a,b,c){var d=b.D;hn({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},ib:{eventId:d.eventId,priorityId:d.priorityId},Zi:{eventId:T(b,R.A.Ke),priorityId:T(b,R.A.Le)}})},Ox=function(a,
b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,priorityId:b.D.priorityId};Nx(a,b,c);cl(d,a,void 0,{Ch:!0,method:"GET"},function(){},function(){bl(d,a+"&img=1")})},Px=function(a){var b=Hc()||Fc()?"www.google.com":"www.googleadservices.com",c=[];zb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Qx=function(a){if(T(a,R.A.ba)===O.N.Oa){var b=
Mx(a),c=[];zb(b,function(r,u){c.push(r+"="+u)});var d=wn([N.m.aa,N.m.W])?45:46,e=Gw(d)+"?"+c.join("&");Nx(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(G(26)&&cd()){cl(g,e,void 0,{Ch:!0},function(){},function(){bl(g,e+"&img=1")});var h=wn([N.m.aa,N.m.W]),m=Eu(a,N.m.jd)==="1",n=Eu(a,N.m.Vh)==="1";if(h&&m&&!n){var p=Px(b),q=Hc()||Fc()?58:57;Ox(p,a,q)}}else al(g,e)||bl(g,e+"&img=1");if(qb(a.D.onSuccess))a.D.onSuccess()}},Rx={},Lx=(Rx[N.m.ia]=
"gcu",Rx[N.m.oc]="gclgb",Rx[N.m.sb]="gclaw",Rx[N.m.Qe]="gad_source",Rx[N.m.Re]="gad_source_src",Rx[N.m.Zc]="gclid",Rx[N.m.yk]="gclsrc",Rx[N.m.Se]="gbraid",Rx[N.m.ce]="wbraid",Rx[N.m.bd]="auid",Rx[N.m.Bk]="rnd",Rx[N.m.Vh]="ncl",Rx[N.m.Hg]="gcldc",Rx[N.m.gd]="dclid",Rx[N.m.Ac]="edid",Rx[N.m.Bc]="en",Rx[N.m.ie]="gdpr",Rx[N.m.Cc]="gdid",Rx[N.m.je]="_ng",Rx[N.m.lf]="gpp_sid",Rx[N.m.nf]="gpp",Rx[N.m.pf]="_tu",Rx[N.m.Tk]="gtm_up",Rx[N.m.Ec]="frm",Rx[N.m.jd]="lps",Rx[N.m.Qg]="did",Rx[N.m.Wk]="navt",Rx[N.m.za]=
"dl",Rx[N.m.Ya]="dr",Rx[N.m.Cb]="dt",Rx[N.m.il]="scrsrc",Rx[N.m.wf]="ga_uid",Rx[N.m.oe]="gdpr_consent",Rx[N.m.ki]="u_tz",Rx[N.m.Vg]="tid",Rx[N.m.Na]="uid",Rx[N.m.Ff]="us_privacy",Rx[N.m.Dd]="npa",Rx);var Sx={};Sx.O=hr.O;var Tx={Wr:"L",lp:"S",us:"Y",Cr:"B",Or:"E",Sr:"I",ns:"TC",Rr:"HTC"},Ux={lp:"S",Nr:"V",Fr:"E",ls:"tag"},Vx={},Wx=(Vx[Sx.O.Ri]="6",Vx[Sx.O.Si]="5",Vx[Sx.O.Qi]="7",Vx);function Xx(){function a(c,d){var e=ob(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Yx=!1;
function qy(a){}function ry(a){}
function sy(){}function ty(a){}
function uy(a){}function vy(a){}
function wy(){}
function xy(a,b){}
function yy(a,b,c){}
function zy(){};var Ay=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function By(a,b,c,d,e,f,g,h){var m=oa(Object,"assign").call(Object,{},Ay);c&&(m.body=c,m.method="POST");oa(Object,"assign").call(Object,m,e);h==null||Sk(h);w.fetch(b,m).then(function(n){h==null||Tk(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function u(){p.read().then(function(t){var v;v=t.done;var x=q.decode(t.value,{stream:!v});Cy(d,x);v?(f==null||f(),r()):u()}).catch(function(){r()})}u()})}}).catch(function(){h==null||Tk(h);
g?g():G(128)&&(b+="&_z=retryFetch",c?al(a,b,c):$k(a,b))})};var Dy=function(a){this.M=a;this.C=""},Ey=function(a,b){a.H=b;return a},Cy=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}Fy(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},Gy=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c={};Fy(a,(c[b.fallback_url_method]=
[b.fallback_url],c.options={},c))}}},Fy=function(a,b){b&&(Hy(b.send_pixel,b.options,a.M),Hy(b.create_iframe,b.options,a.R),Hy(b.fetch,b.options,a.H))};function Iy(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Hy(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=sd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var Jy=function(a,b){this.Jq=a;this.timeoutMs=b;this.Ua=void 0},Sk=function(a){a.Ua||(a.Ua=setTimeout(function(){a.Jq();a.Ua=void 0},a.timeoutMs))},Tk=function(a){a.Ua&&(clearTimeout(a.Ua),a.Ua=void 0)};var sz=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),tz={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},uz={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},vz="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function wz(){var a=cp("gtm.allowlist")||cp("gtm.whitelist");a&&P(9);Xi&&!G(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:G(212)&&(a=void 0);sz.test(w.location&&w.location.hostname)&&(Xi?P(116):(P(117),xz&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Kb(Db(a),tz),c=cp("gtm.blocklist")||cp("gtm.blacklist");c||(c=cp("tagTypeBlacklist"))&&P(3);c?P(8):c=[];sz.test(w.location&&w.location.hostname)&&(c=Db(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));Db(c).indexOf("google")>=0&&P(2);var d=c&&Kb(Db(c),uz),e={};return function(f){var g=f&&f[nf.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=cj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Xi&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){P(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var u=xb(d,h||[]);u&&
P(10);q=u}}var t=!m||q;!t&&(h.indexOf("sandboxedScripts")===-1?0:Xi&&h.indexOf("cmpPartners")>=0?!yz():b&&b.indexOf("sandboxedScripts")!==-1?0:xb(d,vz))&&(t=!0);return e[g]=t}}function yz(){var a=og(lg.C,Fi(5),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var xz=!1;xz=!0;G(218)&&(xz=Di(48,xz));function zz(a,b,c,d,e){if(!Pj(a)){d.loadExperiments=Pi();Rj(a,d,e);var f=Az(a),g=function(){Aj().container[a]&&(Aj().container[a].state=3);Bz()},h={destinationId:a,endpoint:0};if(fj())dl(h,ej()+"/"+f,void 0,g);else{var m=Lb(a,"GTM-"),n=ck(),p=c?"/gtag/js":"/gtm.js",q=bk(b,p+f);if(!q){var r=Fi(3)+p;n&&Cc&&m&&(r=Cc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=tv("https://","http://",r+f)}dl(h,q,void 0,g)}}}function Bz(){Sj()||zb(Tj(),function(a,b){Cz(a,b.transportUrl,b.context);P(92)})}
function Cz(a,b,c,d){if(!Qj(a))if(c.loadExperiments||(c.loadExperiments=Pi()),Sj()){var e=Aj(),f=e.destination[a];f?Array.isArray(f)||(f.state=0):(f={state:0,transportUrl:b,context:c,parent:Kj()},e.destination[a]=f);zj({ctid:a,isDestination:!0},d);P(91)}else{var g=Aj(),h=g.destination[a];h?Array.isArray(h)||(h.state=1):(h={context:c,state:1,parent:Kj()},g.destination[a]=h);zj({ctid:a,isDestination:!0},d);var m={destinationId:a,endpoint:0};if(fj())dl(m,ej()+("/gtd"+Az(a,!0)));else{var n="/gtag/destination"+
Az(a,!0),p=bk(b,n);p||(p=tv("https://","http://",Fi(3)+n));dl(m,p)}}}function Az(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a),d=Fi(19);d!=="dataLayer"&&(c+="&l="+d);if(!Lb(a,"GTM-")||b)c=G(130)?c+(fj()?"&sc=1":"&cx=c"):c+"&cx=c";var e=c,f,g={ln:Ii(15),on:Fi(14)};f=mf(g);c=e+("&gtm="+f);ck()&&(c+="&sign="+Ri.Oi);var h=Hi.M;h===1?c+="&fps=fc":h===2&&(c+="&fps=fe");return c};var Dz=function(){this.H=0;this.C={}};Dz.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ie:c};return d};Dz.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var Fz=function(a,b){var c=[];zb(Ez.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ie===void 0||b.indexOf(e.Ie)>=0)&&c.push(e.listener)});return c};function Gz(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:Fi(5)}};function Hz(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var Jz=function(a,b){this.C=!1;this.R=[];this.eventData={tags:[]};this.U=!1;this.H=this.M=0;Iz(this,a,b)},Kz=function(a,b,c,d){if(Ti.hasOwnProperty(b)||b==="__zone")return-1;var e={};sd(d)&&(e=td(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},Lz=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},Mz=function(a){if(!a.C){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.C=!0;a.R.length=0}},Iz=function(a,b,c){b!==void 0&&a.Tf(b);c&&w.setTimeout(function(){Mz(a)},
Number(c))};Jz.prototype.Tf=function(a){var b=this,c=Ib(function(){Sc(function(){a(Fi(5),b.eventData)})});this.C?c():this.R.push(c)};var Nz=function(a){a.M++;return Ib(function(){a.H++;a.U&&a.H>=a.M&&Mz(a)})},Oz=function(a){a.U=!0;a.H>=a.M&&Mz(a)};var Pz={};function Qz(){return w[Rz()]}
function Rz(){return w.GoogleAnalyticsObject||"ga"}function Uz(){var a=Fi(5);}
function Vz(a,b){return function(){var c=Qz(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var aA=["es","1"],bA={},cA={};function dA(a,b){if(jk){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";bA[a]=[["e",c],["eid",a]];vp(a)}}function eA(a){var b=a.eventId,c=a.Uc;if(!bA[b])return[];var d=[];cA[b]||d.push(aA);d.push.apply(d,Ba(bA[b]));c&&(cA[b]=!0);return d};var fA={},gA={},hA={};function iA(a,b,c,d){jk&&G(120)&&((d===void 0?0:d)?(hA[b]=hA[b]||0,++hA[b]):c!==void 0?(gA[a]=gA[a]||{},gA[a][b]=Math.round(c)):(fA[a]=fA[a]||{},fA[a][b]=(fA[a][b]||0)+1))}function jA(a){var b=a.eventId,c=a.Uc,d=fA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete fA[b];return e.length?[["md",e.join(".")]]:[]}
function kA(a){var b=a.eventId,c=a.Uc,d=gA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete gA[b];return e.length?[["mtd",e.join(".")]]:[]}function lA(){for(var a=[],b=l(Object.keys(hA)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+hA[d])}return a.length?[["mec",a.join(".")]]:[]};var mA={},nA={};function oA(a,b,c){if(jk&&b){var d=gk(b);mA[a]=mA[a]||[];mA[a].push(c+d);var e=b[nf.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Pf[e]?"1":"2")+d;nA[a]=nA[a]||[];nA[a].push(f);vp(a)}}function pA(a){var b=a.eventId,c=a.Uc,d=[],e=mA[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=nA[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete mA[b],delete nA[b]);return d};function qA(a,b,c){c=c===void 0?!1:c;rA().addRestriction(0,a,b,c)}function sA(a,b,c){c=c===void 0?!1:c;rA().addRestriction(1,a,b,c)}function tA(){var a=Fj();return rA().getRestrictions(1,a)}var uA=function(){this.container={};this.C={}},vA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
uA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=vA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
uA.prototype.getRestrictions=function(a,b){var c=vA(this,b);if(a===0){var d,e;return[].concat(Ba((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),Ba((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(Ba((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),Ba((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
uA.prototype.getExternalRestrictions=function(a,b){var c=vA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};uA.prototype.removeExternalRestrictions=function(a){var b=vA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function rA(){return Tn("r",function(){return new uA})};function wA(a,b,c,d){var e=Nf[a],f=xA(a,b,c,d);if(!f)return null;var g=ag(e[nf.om],c,[]);if(g&&g.length){var h=g[0];f=wA(h.index,{onSuccess:f,onFailure:h.Om===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function xA(a,b,c,d){function e(){function x(){nm(3);var M=Gb()-F;Gz(1,a,Nf[a][nf.ah]);oA(c.id,f,"7");Lz(c.Mc,E,"exception",M);G(109)&&yy(c,f,Sx.O.Qi);J||(J=!0,h())}if(f[nf.bp])h();else{var y=$f(f,c,[]),A=y[nf.En];if(A!=null)for(var D=0;D<A.length;D++)if(!wn(A[D])){h();return}var E=Kz(c.Mc,String(f[nf.Ra]),Number(f[nf.mh]),y[nf.METADATA]),J=!1;y.vtp_gtmOnSuccess=function(){if(!J){J=!0;var M=Gb()-F;oA(c.id,Nf[a],"5");Lz(c.Mc,E,"success",M);G(109)&&yy(c,f,Sx.O.Si);g()}};y.vtp_gtmOnFailure=function(){if(!J){J=
!0;var M=Gb()-F;oA(c.id,Nf[a],"6");Lz(c.Mc,E,"failure",M);G(109)&&yy(c,f,Sx.O.Ri);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);oA(c.id,f,"1");G(109)&&xy(c,f);var F=Gb();try{bg(y,{event:c,index:a,type:1})}catch(M){x(M)}G(109)&&yy(c,f,Sx.O.zm)}}var f=Nf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=ag(f[nf.Am],c,[]);if(n&&n.length){var p=n[0],q=wA(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;
g=q;h=p.Om===2?m:q}if(f[nf.fm]||f[nf.ep]){var r=f[nf.fm]?Of:c.rr,u=g,t=h;if(!r[a]){var v=yA(a,r,Ib(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](u,t)}}return e}function yA(a,b,c){var d=[],e=[];b[a]=zA(d,e,c);return{onSuccess:function(){b[a]=AA;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=BA;for(var f=0;f<e.length;f++)e[f]()}}}function zA(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function AA(a){a()}function BA(a,b){b()};var EA=function(a,b){for(var c=[],d=0;d<Nf.length;d++)if(a[d]){var e=Nf[d];var f=Nz(b.Mc);try{var g=wA(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[nf.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Pf[h];c.push({tn:d,priorityOverride:(m?m.priorityOverride||0:0)||Hz(e[nf.Ra],1)||0,execute:g})}else CA(d,b),f()}catch(p){f()}}c.sort(DA);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function FA(a,b){if(!Ez)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=Fz(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=Nz(b);try{d[e](a,f)}catch(g){f()}}return!0}function DA(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.tn,h=b.tn;f=g>h?1:g<h?-1:0}return f}
function CA(a,b){if(jk){var c=function(d){var e=b.isBlocked(Nf[d])?"3":"4",f=ag(Nf[d][nf.om],b,[]);f&&f.length&&c(f[0].index);oA(b.id,Nf[d],e);var g=ag(Nf[d][nf.Am],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var GA=!1,Ez;function HA(){Ez||(Ez=new Dz);return Ez}
function IA(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(G(109)){}if(d==="gtm.js"){if(GA)return!1;GA=!0}var e=!1,f=tA(),g=td(a,null);if(!f.every(function(u){return u({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}dA(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:JA(g,e),rr:[],logMacroError:function(u,t,v){P(6);nm(0);Gz(2,t,v)},cachedModelValues:KA(),Mc:new Jz(function(){if(G(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,
0))},m),originalEventData:g};G(120)&&jk&&(n.reportMacroDiscrepancy=iA);G(109)&&uy(n.id);var p=gg(n);G(109)&&vy(n.id);e&&(p=LA(p));G(109)&&ty(b);var q=EA(p,n),r=FA(a,n.Mc);Oz(n.Mc);d!=="gtm.js"&&d!=="gtm.sync"||Uz();return MA(p,q)||r}function KA(){var a={};a.event=hp("event",1);a.ecommerce=hp("ecommerce",1);a.gtm=hp("gtm");a.eventModel=hp("eventModel");return a}
function JA(a,b){var c=wz();return function(d){if(c(d))return!0;var e=d&&d[nf.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Fj();f=rA().getRestrictions(0,g);var h=a;b&&(h=td(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=cj[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function LA(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Nf[c][nf.Ra]);if(Si[d]||Nf[c][nf.fp]!==void 0||Hz(d,2))b[c]=!0}return b}function MA(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Nf[c]&&!Ti[String(Nf[c][nf.Ra])])return!0;return!1};function NA(){HA().addListener("gtm.init",function(a,b){Hi.ja=!0;$l();b()})};var OA=!1,PA=0,QA=[];function RA(a){if(!OA){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){OA=!0;for(var e=0;e<QA.length;e++)Sc(QA[e])}QA.push=function(){for(var f=Fa.apply(0,arguments),g=0;g<f.length;g++)Sc(f[g]);return 0}}}function SA(){if(!OA&&PA<140){PA++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");RA()}catch(c){w.setTimeout(SA,50)}}}
function TA(){var a=w;OA=!1;PA=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")RA();else{Qc(z,"DOMContentLoaded",RA);Qc(z,"readystatechange",RA);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&SA()}Qc(a,"load",RA)}}function UA(a){OA?a():QA.push(a)};function VA(a,b){return arguments.length===1?WA("set",a):WA("set",a,b)}function XA(a,b){return arguments.length===1?WA("config",a):WA("config",a,b)}function YA(a,b,c){c=c||{};c[N.m.pd]=a;return WA("event",b,c)}function WA(){return arguments};var ZA=function(){this.messages=[];this.C=[]};ZA.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=oa(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};ZA.prototype.listen=function(a){this.C.push(a)};
ZA.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};ZA.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function $A(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.A.ab]=Fi(6);aB().enqueue(a,b,c)}function bB(){var a=cB;aB().listen(a)}
function aB(){return Tn("mb",function(){return new ZA})};var dB={},eB={};function fB(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Dj:void 0,kj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Dj=oo(g,b),e.Dj){var h=Ej();vb(h,function(r){return function(u){return r.Dj.destinationId===u}}(e))?c.push(g):d.push(g)}}else{var m=dB[g]||[];e.kj={};m.forEach(function(r){return function(u){r.kj[u]=!0}}(e));for(var n=Gj(),p=0;p<n.length;p++)if(e.kj[n[p]]){c=c.concat(Ej());break}var q=eB[g]||[];q.length&&(c=c.concat(q))}}return{wj:c,Hq:d}}
function gB(a){zb(dB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function hB(a){zb(eB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var iB=!1,jB=!1;function kB(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=td(b,null),b[N.m.ff]&&(d.eventCallback=b[N.m.ff]),b[N.m.Mg]&&(d.eventTimeout=b[N.m.Mg]));return d}function lB(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Yn()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function mB(a,b){var c=a&&a[N.m.pd];c===void 0&&(c=cp(N.m.pd,2),c===void 0&&(c="default"));if(sb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?sb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=fB(d,b.isGtmEvent),f=e.wj,g=e.Hq;if(g.length)for(var h=nB(a),m=0;m<g.length;m++){var n=oo(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=void 0,r=n.destinationId,u=Aj().destination[r];((q=Hj(u))==null?void 0:q.state)===0||Cz(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);
return{wj:po(f,b.isGtmEvent),Ap:po(t,b.isGtmEvent)}}}var oB=void 0,pB=void 0;function qB(a,b,c){var d=td(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&P(136);var e=td(b,null);td(c,e);$A(XA(Gj()[0],e),a.eventId,d)}function nB(a){for(var b=l([N.m.rd,N.m.rc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Dp.C[d];if(e)return e}}
var rB={config:function(a,b){var c=lB(a,b);if(!(a.length<2)&&sb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!sd(a[2])||a.length>3)return;d=a[2]}var e=oo(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Ei(7)){var m=Jj(Kj());if(Uj(m)){var n=m.parent,p=n.isDestination;h={Lq:Jj(n),Fq:p};break a}}h=void 0}var q=h;q&&(f=q.Lq,g=q.Fq);dA(c.eventId,"gtag.config");var r=e.destinationId,u=e.id!==r;if(u?Ej().indexOf(r)===-1:Gj().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[N.m.Gc]){var t=nB(d);if(u)Cz(r,t,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;oB?qB(b,v,oB):pB||(pB=td(v,null))}else zz(r,t,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(P(128),g&&P(130),b.inheritParentConfig)){var x;var y=d;pB?(qB(b,pB,y),x=!1):(!y[N.m.sd]&&Ei(11)&&oB||(oB=td(y,null)),x=!0);x&&f.containers&&f.containers.join(",");return}lk&&($n===1&&(Tl.mcc=!1),$n=2);if(Ei(11)&&!u&&!d[N.m.sd]){var A=jB;jB=!0;if(A)return}iB||P(43);if(!b.noTargetGroup)if(u){hB(e.id);
var D=e.id,E=d[N.m.Pg]||"default";E=String(E).split(",");for(var J=0;J<E.length;J++){var F=eB[E[J]]||[];eB[E[J]]=F;F.indexOf(D)<0&&F.push(D)}}else{gB(e.id);var M=e.id,U=d[N.m.Pg]||"default";U=U.toString().split(",");for(var ha=0;ha<U.length;ha++){var S=dB[U[ha]]||[];dB[U[ha]]=S;S.indexOf(M)<0&&S.push(M)}}delete d[N.m.Pg];var aa=b.eventMetadata||{};aa.hasOwnProperty(R.A.yd)||(aa[R.A.yd]=!b.fromContainerExecution);b.eventMetadata=aa;delete d[N.m.ff];for(var ta=u?[e.id]:Ej(),la=0;la<ta.length;la++){var ea=
d,Z=ta[la],ka=td(b,null),za=oo(Z,ka.isGtmEvent);za&&Dp.push("config",[ea],za,ka)}}}}},consent:function(a,b){if(a.length===3){P(39);var c=lB(a,b),d=a[1],e={},f=Om(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===N.m.ug?Array.isArray(h)?NaN:Number(h):g===N.m.hc?(Array.isArray(h)?h:[h]).map(Pm):Qm(h)}b.fromContainerExecution||(e[N.m.W]&&P(139),e[N.m.Ma]&&P(140));d==="default"?sn(e):d==="update"?un(e,c):d==="declare"&&b.fromContainerExecution&&rn(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&sb(c)){var d=void 0;if(a.length>2){if(!sd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=kB(c,d),f=lB(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=mB(d,b);if(m){for(var n=m.wj,p=m.Ap,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),u=n.map(function(M){return M.id}),t=l(Ej()),v=t.next();!v.done;v=t.next()){var x=v.value;r.indexOf(x)<0&&u.push(x)}dA(g,
c);for(var y=l(u),A=y.next();!A.done;A=y.next()){var D=A.value,E=td(b,null),J=td(d,null);delete J[N.m.ff];var F=E.eventMetadata||{};F.hasOwnProperty(R.A.yd)||(F[R.A.yd]=!E.fromContainerExecution);F[R.A.Li]=q.slice();F[R.A.Qf]=r.slice();E.eventMetadata=F;Ep(c,J,D,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[N.m.pd]=q.join(","):delete e.eventModel[N.m.pd];iB||P(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[R.A.xm]&&(b.noGtmEvent=!0);e.eventModel[N.m.Fc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){P(53);if(a.length===4&&sb(a[1])&&sb(a[2])&&qb(a[3])){var c=oo(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){iB||P(43);var f=nB();if(vb(Ej(),function(h){return c.destinationId===h})){lB(a,b);var g={};td((g[N.m.kf]=d,g[N.m.jf]=e,g),null);Fp(d,function(h){Sc(function(){e(h)})},c.id,b)}else Cz(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){iB=!0;var c=lB(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&sb(a[1])&&qb(a[2])){if(mg(a[1],a[2]),P(74),a[1]==="all"){P(75);var b=!1;try{b=a[2](Fi(5),"unknown",{})}catch(c){}b||P(76)}}else P(73)},set:function(a,b){var c=void 0;a.length===2&&sd(a[1])?c=td(a[1],null):a.length===3&&sb(a[1])&&(c={},sd(a[2])||Array.isArray(a[2])?c[a[1]]=td(a[2],null):c[a[1]]=a[2]);if(c){var d=lB(a,b),e=d.eventId,f=d.priorityId;
td(c,null);Fi(5);var g=td(c,null);Dp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},sB={policy:!0};var uB=function(a){if(tB(a))return a;this.value=a};uB.prototype.getUntrustedMessageValue=function(){return this.value};var tB=function(a){return!a||qd(a)!=="object"||sd(a)?!1:"getUntrustedMessageValue"in a};uB.prototype.getUntrustedMessageValue=uB.prototype.getUntrustedMessageValue;var vB=!1,wB=[];function xB(){if(!vB){vB=!0;for(var a=0;a<wB.length;a++)Sc(wB[a])}}function yB(a){vB?Sc(a):wB.push(a)};var zB=0,AB={},BB=[],CB=[],DB=!1,EB=!1;function FB(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function GB(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return HB(a)}function IB(a,b){if(!tb(b)||b<0)b=0;var c=Xn(),d=0,e=!1,f=void 0;f=w.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(w.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function JB(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(Ab(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function KB(){var a;if(CB.length)a=CB.shift();else if(BB.length)a=BB.shift();else return;var b;var c=a;if(DB||!JB(c.message))b=c;else{DB=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Yn(),f=Yn(),c.message["gtm.uniqueEventId"]=Yn());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};BB.unshift(n,c);b=h}return b}
function LB(){for(var a=!1,b;!EB&&(b=KB());){EB=!0;delete $o.eventModel;bp();var c=b,d=c.message,e=c.messageContext;if(d==null)EB=!1;else{e.fromContainerExecution&&gp();try{if(qb(d))try{d.call(dp)}catch(J){}else if(Array.isArray(d)){if(sb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=cp(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(J){}}}else{var n=void 0;if(Ab(d))a:{if(d.length&&sb(d[0])){var p=rB[d[0]];if(p&&(!e.fromContainerExecution||!sB[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,u=r._clear||e.overwriteModelFields,t=l(Object.keys(r)),v=t.next();!v.done;v=t.next()){var x=v.value;x!=="_clear"&&(u&&fp(x),fp(x,r[x]))}$i||($i=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Yn(),r["gtm.uniqueEventId"]=y,fp("gtm.uniqueEventId",y)),q=IA(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&bp(!0);var A=d["gtm.uniqueEventId"];if(typeof A==="number"){for(var D=AB[String(A)]||[],E=0;E<D.length;E++)CB.push(MB(D[E]));D.length&&CB.sort(FB);
delete AB[String(A)];A>zB&&(zB=A)}EB=!1}}}return!a}
function NB(){if(G(109)){var a=!Hi.R;}var c=LB();if(G(109)){}try{var e=w[Fi(19)],f=Fi(5),g=e.hide;if(g&&g[f]!==void 0&&
g.end){g[f]=!1;var h=!0,m;for(m in g)if(g.hasOwnProperty(m)&&g[m]===!0){h=!1;break}h&&(g.end(),g.end=null)}}catch(n){Fi(5)}return c}function cB(a){if(zB<a.notBeforeEventId){var b=String(a.notBeforeEventId);AB[b]=AB[b]||[];AB[b].push(a)}else CB.push(MB(a)),CB.sort(FB),Sc(function(){EB||LB()})}function MB(a){return{message:a.message,messageContext:a.messageContext}}
function OB(){function a(f){var g={};if(tB(f)){var h=f;f=tB(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=Dc(Fi(19),[]),c=Wn();c.pruned===!0&&P(83);AB=aB().get();bB();UA(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});yB(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Sn.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new uB(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});BB.push.apply(BB,h);var m=d.apply(b,f),n=Math.max(100,Number(Ji(1,"300")));if(this.length>n)for(P(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return LB()&&p};var e=b.slice(0).map(function(f){return a(f)});BB.push.apply(BB,e);if(!Hi.R){if(G(109)){}Sc(NB)}}
var HB=function(a){return w[Fi(19)].push(a)};function PB(a){HB(a)};function QB(){var a,b=sj(w.location.href);(a=b.hostname+b.pathname)&&Wl("dl",encodeURIComponent(a));var c;var d=Fi(5);if(d){var e=Ei(7)?1:0,f,g=Kj(),h=Jj(g),m=(f=h&&h.context)&&f.fromContainerExecution?1:0,n=f&&f.source||0,p=Fi(6);c=d+";"+p+";"+m+";"+n+";"+e}else c=void 0;var q=c;q&&Wl("tdp",q);var r=Wp(!0);r!==void 0&&Wl("frm",String(r))};var RB={},SB=void 0;
function TB(){if(an()||lk)Wl("csp",function(){return Object.keys(RB).join("~")||void 0},!1),w.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){P(179);var b=Zk(a.effectiveDirective);if(b){var c;var d=Xk(b,a.blockedURI);c=d?Vk[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=
n.value;if(!p.mn){p.mn=!0;if(G(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if(an()){var r=q,u={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(an()){var t=gn("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});t.tagDiagnostics=u;$m(t)}}}UB(p.endpoint)}}Yk(b,a.blockedURI)}}}}})}
function UB(a){var b=String(a);RB.hasOwnProperty(b)||(RB[b]=!0,Xl("csp",!0),SB===void 0&&G(171)&&(SB=w.setTimeout(function(){if(G(171)){var c=Tl.csp;Tl.csp=!0;Tl.seq=!1;var d=Yl(!1);Tl.csp=c;Tl.seq=!0;Lc(d+"&script=1")}SB=void 0},500)))};var VB=void 0;function WB(){G(236)&&w.addEventListener("pageshow",function(a){a&&(Wl("bfc",function(){return VB?"1":"0"}),a.persisted?(VB=!0,Xl("bfc",!0),$l()):VB=!1)})};function XB(){var a;var b=Ij();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Wl("pcid",e)};var YB=/^(https?:)?\/\//;
function ZB(){var a=Lj();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=hd())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(YB,"")===d.replace(YB,""))){b=g;break a}}P(146)}else P(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Wl("rtg",String(a.canonicalContainerId)),Wl("slo",String(p)),Wl("hlo",a.htmlLoadOrder||"-1"),
Wl("lst",String(a.loadScriptType||"0")))}else P(144)};

function tC(){};var uC=function(){};uC.prototype.toString=function(){return"undefined"};var vC=new uC;function CC(){G(212)&&Xi&&(mg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),qA(Fj(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return Hz(d,5)||!(!Pf[d]||!Pf[d][5])||c.includes("cmpPartners")}))};function DC(a,b){function c(g){var h=sj(g),m=mj(h,"protocol"),n=mj(h,"host",!0),p=mj(h,"port"),q=mj(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function EC(a){return FC(a)?1:0}
function FC(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=td(a,{});td({arg1:c[d],any_of:void 0},e);if(EC(e))return!0}return!1}switch(a["function"]){case "_cn":return Tg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Og.length;g++){var h=Og[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Pg(b,c);case "_eq":return Ug(b,c);case "_ge":return Vg(b,c);case "_gt":return Xg(b,c);case "_lc":return Qg(b,c);case "_le":return Wg(b,
c);case "_lt":return Yg(b,c);case "_re":return Sg(b,c,a.ignore_case);case "_sw":return Zg(b,c);case "_um":return DC(b,c)}return!1};var GC=function(){this.C=this.gppString=void 0};GC.prototype.reset=function(){this.C=this.gppString=void 0};var HC=new GC;[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var IC=function(a,b,c,d){cq.call(this);this.ih=b;this.Nf=c;this.Kc=d;this.Za=new Map;this.jh=0;this.ma=new Map;this.Ja=new Map;this.U=void 0;this.H=a};ya(IC,cq);IC.prototype.M=function(){delete this.C;this.Za.clear();this.ma.clear();this.Ja.clear();this.U&&(Zp(this.H,"message",this.U),delete this.U);delete this.H;delete this.Kc;cq.prototype.M.call(this)};
var JC=function(a){if(a.C)return a.C;a.Nf&&a.Nf(a.H)?a.C=a.H:a.C=Vp(a.H,a.ih);var b;return(b=a.C)!=null?b:null},LC=function(a,b,c){if(JC(a))if(a.C===a.H){var d=a.Za.get(b);d&&d(a.C,c)}else{var e=a.ma.get(b);if(e&&e.vj){KC(a);var f=++a.jh;a.Ja.set(f,{Dh:e.Dh,Rp:e.Vm(c),persistent:b==="addEventListener"});a.C.postMessage(e.vj(c,f),"*")}}},KC=function(a){a.U||(a.U=function(b){try{var c;c=a.Kc?a.Kc(b):void 0;if(c){var d=c.Oq,e=a.Ja.get(d);if(e){e.persistent||a.Ja.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Rp,c.payload)}}}catch(g){}},Yp(a.H,"message",a.U))};var MC=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},NC=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},OC={Vm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},PC={Vm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function QC(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Oq:b.__gppReturn.callId}}
var RC=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;cq.call(this);this.caller=new IC(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},QC);this.caller.Za.set("addEventListener",MC);this.caller.ma.set("addEventListener",OC);this.caller.Za.set("removeEventListener",NC);this.caller.ma.set("removeEventListener",PC);this.timeoutMs=c!=null?c:500};ya(RC,cq);RC.prototype.M=function(){this.caller.dispose();cq.prototype.M.call(this)};
RC.prototype.addEventListener=function(a){var b=this,c=Tp(function(){a(SC,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);LC(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(TC,!0);return}a(UC,!0)}}})};
RC.prototype.removeEventListener=function(a){LC(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var UC={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},SC={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},TC={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function VC(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){HC.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");HC.C=d}}function WC(){try{var a=new RC(w,{timeoutMs:-1});JC(a.caller)&&a.addEventListener(VC)}catch(b){}};function XC(){var a=[["cv",Fi(1)],["rv",Fi(14)],["tc",Nf.filter(function(c){return c}).length]],b=Ii(15);b&&a.push(["x",b]);Wj()&&a.push(["tag_exp",Wj()]);return a};function YC(a){a.Uc&&(sr=rr=0);return[["cd",""+rr],["cd",""+sr]]};var ZC={};function Mi(a){ZC[a]=(ZC[a]||0)+1}function $C(){for(var a=ZC,b=[],c=l(Object.keys(a)),d=c.next();!d.done;d=c.next()){var e=d.value;b.push(e+"."+a[e])}return b.length===0?[]:[["bdm",b.join("~")]]};var aD={},bD={};function cD(a){var b=a.eventId,c=a.Uc,d=[],e=aD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=bD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete aD[b],delete bD[b]);return d};function dD(){return!1}function eD(){var a={};return function(b,c,d){}};function fD(){var a=gD;return function(b,c,d){var e=d&&d.event;hD(c);var f=Dh(b)?void 0:1,g=new cb;zb(c,function(r,u){var t=Id(u,void 0,f);t===void 0&&u!==void 0&&P(44);g.set(r,t)});a.Mb(eg());var h={Im:sg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Tf:e!==void 0?function(r){e.Mc.Tf(r)}:void 0,Jb:function(){return b},log:function(){},Wp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},Wq:!!Hz(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(dD()){var m=eD(),n,p;h.qb={Kj:[],Uf:{},ac:function(r,u,t){u===1&&(n=r);u===7&&(p=t);m(r,u,t)},Bh:Yh()};h.log=function(r){var u=Fa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:u})}}var q=df(a,h,[b,g]);a.Mb();q instanceof Ia&&(q.type==="return"?q=q.data:q=void 0);return B(q,void 0,f)}}function hD(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;qb(b)&&(a.gtmOnSuccess=function(){Sc(b)});qb(c)&&(a.gtmOnFailure=function(){Sc(c)})};function iD(a){}iD.K="internal.addAdsClickIds";function jD(a,b){var c=this;}jD.publicName="addConsentListener";var kD=!1;function lD(a){for(var b=0;b<a.length;++b)if(kD)try{a[b]()}catch(c){P(77)}else a[b]()}function mD(a,b,c){var d=this,e;if(!K(a)||!lh(b)||!ph(c))throw I(this.getName(),["string","function","string|undefined"],arguments);lD([function(){L(d,"listen_data_layer",a)}]);e=HA().addListener(a,B(b),c===null?void 0:c);return e}mD.K="internal.addDataLayerEventListener";function nD(a,b,c){}nD.publicName="addDocumentEventListener";function oD(a,b,c,d){}oD.publicName="addElementEventListener";function pD(a){return a.J.ob()};function qD(a){}qD.publicName="addEventCallback";
var rD=function(a){return typeof a==="string"?a:String(Yn())},uD=function(a,b){sD(a,"init",!1)||(tD(a,"init",!0),b())},sD=function(a,b,c){var d=vD(a);return Hb(d,b,c)},wD=function(a,b,c,d){var e=vD(a),f=Hb(e,b,d);e[b]=c(f)},tD=function(a,b,c){vD(a)[b]=c},vD=function(a){var b=Tn("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},xD=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":ed(a,"className"),"gtm.elementId":a.for||Tc(a,"id")||"","gtm.elementTarget":a.formTarget||
ed(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||ed(a,"href")||a.src||a.code||a.codebase||"";return d};
var AD=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e],h=g.tagName.toLowerCase();if(!(yD.indexOf(h)<0||h==="input"&&zD.indexOf(g.type.toLowerCase())>=0)){if(g.dataset[c]===d)return f;f++}}return 0},BD=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:z.getElementById(a.form)}return Wc(a,["form"],100)},yD=["input","select","textarea"],zD=["button","hidden","image","reset","submit"];
function FD(a){}FD.K="internal.addFormAbandonmentListener";function GD(a,b,c,d){}
GD.K="internal.addFormData";var HD={},ID=[],JD={},KD=0,LD=0;
var ND=function(){Qc(z,"change",function(a){for(var b=0;b<ID.length;b++)ID[b](a)});Qc(w,"pagehide",function(){MD()})},MD=function(){zb(JD,function(a,b){var c=HD[a];c&&zb(b,function(d,e){OD(e,c)})})},RD=function(a,b){var c=""+a;if(HD[c])HD[c].push(b);else{var d=[b];HD[c]=d;var e=JD[c];e||(e={},JD[c]=e);ID.push(function(f){var g=f.target;if(g){var h=BD(g);if(h){var m=PD(h,"gtmFormInteractId",function(){return KD++}),n=PD(g,"gtmFormInteractFieldId",function(){return LD++}),p=e[m];p?(p.Ua&&(w.clearTimeout(p.Ua),
p.bc.dataset.gtmFormInteractFieldId!==n&&OD(p,d)),p.bc=g,QD(p,d,a)):(e[m]={form:h,bc:g,sequenceNumber:0,Ua:null},QD(e[m],d,a))}}})}},OD=function(a,b){var c=a.form,d=a.bc,e=xD(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=AD(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.Ua=null},QD=function(a,b,c){c?a.Ua=w.setTimeout(function(){OD(a,b)},c):OD(a,b)},PD=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function SD(a,b){if(!lh(a)||!jh(b))throw I(this.getName(),["function","Object|undefined"],arguments);var c=B(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=B(a),f;sD("pix.fil","init")?f=sD("pix.fil","reg"):(ND(),f=RD,tD("pix.fil","reg",RD),tD("pix.fil","init",!0));f(d,e);}SD.K="internal.addFormInteractionListener";
var UD=function(a,b,c){var d=xD(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&TD(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},VD=function(a,b){var c=sD("pix.fsl",a?"nv.mwt":"mwt",0);w.setTimeout(b,c)},WD=function(a,b,c,d,e){var f=sD("pix.fsl",c?"nv.mwt":"mwt",0),g=sD("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var h=UD(a,c,e);P(121);if(h["gtm.elementUrl"]==="https://www.facebook.com/tr/")return P(122),!0;if(d&&f){for(var m=Qb(b,g.length),n=0;n<g.length;++n)g[n](h,m);return m.done}for(var p=0;p<g.length;++p)g[p](h,function(){});return!0},XD=function(){var a=[],b=function(c){return vb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
TD=function(a){var b=ed(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},YD=function(){var a=XD(),b=HTMLFormElement.prototype.submit;Qc(z,"click",function(c){var d=c.target;if(d){var e=Wc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&Tc(e,"value")){var f=BD(e);f&&a.store(f,e)}}},!1);Qc(z,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=TD(d)&&!e,g=a.get(d),h=!0,m=function(){if(h){var n,
p={};g&&(n=z.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),qc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&qc(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(WD(d,m,e,f,g))return h=!1,c.returnValue;VD(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};WD(c,e,!1,TD(c))?(b.call(c),d=!1):VD(!1,e)}};
function ZD(a,b){if(!lh(a)||!jh(b))throw I(this.getName(),["function","Object|undefined"],arguments);var c=B(b,this.J,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=B(a,this.J,1);if(d){var h=function(n){return Math.max(e,n)};wD("pix.fsl","mwt",h,0);f||wD("pix.fsl","nv.mwt",h,0)}var m=function(n){n.push(g);return n};wD("pix.fsl","runIfUncanceled",m,[]);f||wD("pix.fsl","runIfCanceled",
m,[]);sD("pix.fsl","init")||(YD(),tD("pix.fsl","init",!0));}ZD.K="internal.addFormSubmitListener";
function dE(a){}dE.K="internal.addGaSendListener";function eE(a){if(!a)return{};var b=a.Wp;return Gz(b.type,b.index,b.name)}function fE(a){return a?{originatingEntity:eE(a)}:{}};function nE(a){var b=Sn.zones;return b?b.getIsAllowedFn(Gj(),a):function(){return!0}}function oE(){var a=Sn.zones;a&&a.unregisterChild(Gj())}
function pE(){sA(Fj(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Sn.zones;return c?c.isActive(Gj(),b):!0});qA(Fj(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return nE(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var qE=function(a,b){this.tagId=a;this.canonicalId=b};
function rE(a,b){var c=this;return a}rE.K="internal.loadGoogleTag";function sE(a){return new Ad("",function(b){var c=this.evaluate(b);if(c instanceof Ad)return new Ad("",function(){var d=Fa.apply(0,arguments),e=this,f=td(pD(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.J.mb();h.Od(f);return c.Kb.apply(c,[h].concat(Ba(g)))})})};function tE(a,b,c){var d=this;}tE.K="internal.addGoogleTagRestriction";var uE={},vE=[];
function CE(a,b){}
CE.K="internal.addHistoryChangeListener";function DE(a,b,c){}DE.publicName="addWindowEventListener";function EE(a,b){return!0}EE.publicName="aliasInWindow";function FE(a,b,c){}FE.K="internal.appendRemoteConfigParameter";function GE(a){var b;return b}
GE.publicName="callInWindow";function HE(a){}HE.publicName="callLater";function IE(a){}IE.K="callOnDomReady";function JE(a){}JE.K="callOnWindowLoad";function KE(a,b){var c;return c}KE.K="internal.computeGtmParameter";function LE(a,b){var c=this;}LE.K="internal.consentScheduleFirstTry";function ME(a,b){var c=this;}ME.K="internal.consentScheduleRetry";function NE(a){var b;return b}NE.K="internal.copyFromCrossContainerData";function OE(a,b){var c;var d=Id(c,this.J,Dh(pD(this).Jb())?2:1);d===void 0&&c!==void 0&&P(45);return d}OE.publicName="copyFromDataLayer";
function PE(a){var b=void 0;return b}PE.K="internal.copyFromDataLayerCache";function QE(a){var b;return b}QE.publicName="copyFromWindow";function RE(a){var b=void 0;return Id(b,this.J,1)}RE.K="internal.copyKeyFromWindow";var SE=function(a){return a===hl.Z.Ga&&zl[a]===gl.Ka.ue&&!wn(N.m.aa)};var TE=function(){return"0"},UE=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];G(102)&&b.push("gbraid");return tj(a,b,"0")};var VE={},WE={},XE={},YE={},ZE={},$E={},aF={},bF={},cF={},dF={},eF={},fF={},gF={},hF={},iF={},jF={},kF={},lF={},mF={},nF={},oF={},pF={},qF={},rF={},sF={},tF={},uF=(tF[N.m.Na]=(VE[2]=[SE],VE),tF[N.m.wf]=(WE[2]=[SE],WE),tF[N.m.hf]=(XE[2]=[SE],XE),tF[N.m.ml]=(YE[2]=[SE],YE),tF[N.m.nl]=(ZE[2]=[SE],ZE),tF[N.m.ol]=($E[2]=[SE],$E),tF[N.m.pl]=(aF[2]=[SE],aF),tF[N.m.ql]=(bF[2]=[SE],bF),tF[N.m.Eb]=(cF[2]=[SE],cF),tF[N.m.xf]=(dF[2]=[SE],dF),tF[N.m.yf]=(eF[2]=[SE],eF),tF[N.m.zf]=(fF[2]=[SE],fF),tF[N.m.Af]=(gF[2]=
[SE],gF),tF[N.m.Bf]=(hF[2]=[SE],hF),tF[N.m.Cf]=(iF[2]=[SE],iF),tF[N.m.Df]=(jF[2]=[SE],jF),tF[N.m.Ef]=(kF[2]=[SE],kF),tF[N.m.sb]=(lF[1]=[SE],lF),tF[N.m.Zc]=(mF[1]=[SE],mF),tF[N.m.gd]=(nF[1]=[SE],nF),tF[N.m.ce]=(oF[1]=[SE],oF),tF[N.m.Se]=(pF[1]=[function(a){return G(102)&&SE(a)}],pF),tF[N.m.hd]=(qF[1]=[SE],qF),tF[N.m.za]=(rF[1]=[SE],rF),tF[N.m.Ya]=(sF[1]=[SE],sF),tF),vF={},wF=(vF[N.m.sb]=TE,vF[N.m.Zc]=TE,vF[N.m.gd]=TE,vF[N.m.ce]=TE,vF[N.m.Se]=TE,vF[N.m.hd]=function(a){if(!sd(a))return{};var b=td(a,
null);delete b.match_id;return b},vF[N.m.za]=UE,vF[N.m.Ya]=UE,vF),xF={},yF={},zF=(yF[R.A.Sa]=(xF[2]=[SE],xF),yF),AF={};var BF=function(a,b,c,d){this.C=a;this.M=b;this.R=c;this.U=d};BF.prototype.getValue=function(a){a=a===void 0?hl.Z.Gb:a;if(!this.M.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.U(this.C):this.C};BF.prototype.H=function(){return qd(this.C)==="array"||sd(this.C)?td(this.C,null):this.C};
var CF=function(){},DF=function(a,b){this.conditions=a;this.C=b},EF=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new BF(c,e,g,a.C[b]||CF)},FF,GF;var HF,IF=!1;function JF(){IF=!0;if(G(218)&&Di(52,!1))HF=productSettings,productSettings=void 0;else{}HF=HF||{}}function KF(a){IF||JF();return HF[a]};var LF=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;V(this,g,d[g])}},Eu=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,T(a,R.A.Rf))},W=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(FF!=null||(FF=new DF(uF,wF)),e=EF(FF,b,c));d[b]=e};
LF.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return W(this,a,b),!0;if(!sd(c))return!1;W(this,a,oa(Object,"assign").call(Object,c,b));return!0};var MF=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
LF.prototype.copyToHitData=function(a,b,c){var d=Q(this.D,a);d===void 0&&(d=b);if(sb(d)&&c!==void 0&&G(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var T=function(a,b){var c=a.metadata[b];if(b===R.A.Rf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,T(a,R.A.Rf))},V=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(GF!=null||(GF=new DF(zF,AF)),e=EF(GF,b,c));d[b]=e},NF=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},Uu=function(a,b,c){var d=KF(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c},OF=function(a){for(var b=new LF(a.target,a.eventName,a.D),c=MF(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}for(var g=NF(a),h=l(Object.keys(g)),m=h.next();!m.done;m=h.next()){var n=m.value;V(b,n,g[n])}b.isAborted=a.isAborted;return b},PF=function(a){var b=a.D,c=b.eventId,d=b.priorityId;return d?c+"_"+d:String(c)};
LF.prototype.accept=function(){var a=Nl(Il.X.ni,{}),b=PF(this),c=this.target.destinationId;a[b]||(a[b]={});a[b][c]=Fj();var d=Il.X.ni;if(Jl(d)){var e;(e=Kl(d))==null||e.notify()}};LF.prototype.hasBeenAccepted=function(a){var b=Ml(Il.X.ni);if(!b)return!1;var c=b[PF(this)];return c?c[a!=null?a:this.target.destinationId]!==void 0:!1};function QF(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Eu(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Eu(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return T(a,b)},setMetadata:function(b,c){V(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return Q(a.D,b)},nb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return sd(c)?a.mergeHitDataForKey(b,c):!1},accept:function(){a.accept()},hasBeenAccepted:function(b){return a.hasBeenAccepted(b)}}};function RF(a,b){var c;if(!ih(a)||!jh(b))throw I(this.getName(),["Object","Object|undefined"],arguments);var d=B(b)||{},e=B(a,this.J,1).nb(),f=e.D;d.omitEventContext&&(f=Yo(new No(e.D.eventId,e.D.priorityId)));var g=new LF(e.target,e.eventName,f);if(!d.omitHitData)for(var h=MF(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;W(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=NF(e),r=l(Object.keys(q)),u=r.next();!u.done;u=
r.next()){var t=u.value;V(g,t,q[t])}g.isAborted=e.isAborted;c=Id(QF(g),this.J,1);return c}RF.K="internal.copyPreHit";function SF(a,b){var c=null;return Id(c,this.J,2)}SF.publicName="createArgumentsQueue";function TF(a){return Id(function(c){var d=Qz();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
Qz(),n=m&&m.getByName&&m.getByName(f);return(new w.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}TF.K="internal.createGaCommandQueue";function UF(a){return Id(function(){if(!qb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
Dh(pD(this).Jb())?2:1)}UF.publicName="createQueue";function VF(a,b){var c=null;return c}VF.K="internal.createRegex";function WF(a){}WF.K="internal.declareConsentState";function XF(a){var b="";return b}XF.K="internal.decodeUrlHtmlEntities";function YF(a,b,c){var d;return d}YF.K="internal.decorateUrlWithGaCookies";function ZF(){}ZF.K="internal.deferCustomEvents";function $F(a){return aG?z.querySelector(a):null}
function bG(a,b){if(!aG)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var cG=!1;
if(z.querySelectorAll)try{var dG=z.querySelectorAll(":root");dG&&dG.length==1&&dG[0]==z.documentElement&&(cG=!0)}catch(a){}var aG=cG;function eG(){var a=w.screen;return{width:a?a.width:0,height:a?a.height:0}}
function fG(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!w.getComputedStyle)return!0;var c=w.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=w.getComputedStyle(d,null))}return!1}
var hG=function(a){var b=gG(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},gG=function(){var a=z.body,b=z.documentElement||a&&a.parentElement,c,d;if(z.compatMode&&z.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var kG=function(a){if(iG){if(a>=0&&a<jG.length&&jG[a]){var b;(b=jG[a])==null||b.disconnect();jG[a]=void 0}}else w.clearInterval(a)},nG=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(iG){var e=!1;Sc(function(){e||lG(a,b,c)()});return mG(function(f){e=!0;for(var g={eg:0};g.eg<f.length;g={eg:g.eg},g.eg++)Sc(function(h){return function(){a(f[h.eg])}}(g))},
b,c)}return w.setInterval(lG(a,b,c),1E3)},lG=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:Gb()};Sc(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=hG(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),
f[h]++;else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},mG=function(a,b,c){for(var d=new w.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<jG.length;f++)if(!jG[f])return jG[f]=d,f;return jG.push(d)-1},jG=[],iG=!(!w.IntersectionObserver||!w.IntersectionObserverEntry);
var AG=function(a){a=a||{fg:!0,gg:!0,Hj:void 0};a.Xb=a.Xb||{email:!0,phone:!1,address:!1};var b=oG(a),c=pG[b];if(c&&Gb()-c.timestamp<200)return c.result;var d=qG(),e=d.status,f=[],g,h,m=[];if(!G(33)){if(a.Xb&&a.Xb.email){var n=rG(d.elements);f=sG(n,a&&a.Wf);g=tG(f);n.length>10&&(e="3")}!a.Hj&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(uG(f[p],!!a.fg,!!a.gg));m=m.slice(0,10)}else if(a.Xb){}g&&(h=uG(g,!!a.fg,!!a.gg));var J={elements:m,fn:h,status:e};pG[b]={timestamp:Gb(),result:J};
return J},BG=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},DG=function(a){var b=CG(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},CG=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():void 0}},uG=function(a,b,c){var d=a.element,
e={sa:a.sa,type:a.wa,tagName:d.tagName};b&&(e.querySelector=EG(d));c&&(e.isVisible=!fG(d));return e},oG=function(a){var b=!(a==null||!a.fg)+"."+!(a==null||!a.gg);a&&a.Wf&&a.Wf.length&&(b+="."+a.Wf.join("."));a&&a.Xb&&(b+="."+a.Xb.email+"."+a.Xb.phone+"."+a.Xb.address);return b},tG=function(a){if(a.length!==0){var b;b=FG(a,function(c){return!GG.test(c.sa)});b=FG(b,function(c){return c.element.tagName.toUpperCase()==="INPUT"});b=FG(b,function(c){return!fG(c.element)});return b[0]}},sG=function(a,b){b&&
b.length!==0||(b=[]);for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&bG(a[d].element,g)){e=!1;break}}a[d].wa===zG.Nb&&G(227)&&(GG.test(a[d].sa)||a[d].element.tagName.toUpperCase()==="A"&&a[d].element.hasAttribute("href")&&a[d].element.getAttribute("href").indexOf("mailto:")!==-1)&&(e=!1);e&&c.push(a[d])}return c},FG=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},EG=function(a){var b;if(a===z.body)b="body";else{var c;if(a.id)c="#"+
a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===a){e=g+1;break a}e=-1}else e=1}d=EG(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},rG=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(HG);if(f){var g=f[0],h;if(w.location){var m=oj(w.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=
!1;h||b.push({element:d,sa:g,wa:zG.Nb})}}}return b},qG=function(){var a=[],b=z.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(IG.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(JG.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||G(33)&&KG.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},HG=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,
GG=/support|noreply/i,IG="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),JG=["BR"],LG=Ai(Ji(36),2),zG={Nb:"1",Ed:"2",ud:"3",Cd:"4",Me:"5",Pf:"6",hh:"7",Pi:"8",Jh:"9",Ji:"10"},pG={},KG=["INPUT","SELECT"],MG=CG(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
function GH(a){var b;L(this,"detect_user_provided_data","auto");var c=B(a)||{},d=AG({fg:!!c.includeSelector,gg:!!c.includeVisibility,Wf:c.excludeElementSelectors,Xb:c.fieldFilters,Hj:!!c.selectMultipleElements});b=new cb;var e=new wd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(HH(f[g]));d.fn!==void 0&&b.set("preferredEmailElement",HH(d.fn));b.set("status",d.status);if(G(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(zc&&
zc.userAgent||"")){}return b}
var IH=function(a){switch(a){case zG.Nb:return"email";case zG.Ed:return"phone_number";case zG.ud:return"first_name";case zG.Cd:return"last_name";case zG.Pi:return"street";case zG.Jh:return"city";case zG.Ji:return"region";case zG.Pf:return"postal_code";case zG.Me:return"country"}},HH=function(a){var b=new cb;b.set("userData",a.sa);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(G(33)){}else switch(a.type){case zG.Nb:b.set("type","email")}return b};GH.K="internal.detectUserProvidedData";
function LH(a,b){return f}LH.K="internal.enableAutoEventOnClick";var OH=function(a){if(!MH){var b=function(){var c=z.body;if(c)if(NH)(new MutationObserver(function(){for(var e=0;e<MH.length;e++)Sc(MH[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Qc(c,"DOMNodeInserted",function(){d||(d=!0,Sc(function(){d=!1;for(var e=0;e<MH.length;e++)Sc(MH[e])}))})}};MH=[];z.body?b():Sc(b)}MH.push(a)},NH=!!w.MutationObserver,MH;
function TH(a,b){return p}TH.K="internal.enableAutoEventOnElementVisibility";function UH(){}UH.K="internal.enableAutoEventOnError";var VH={},WH=[],XH={},YH=0,ZH=0;
var aI=function(){zb(XH,function(a,b){var c=VH[a];c&&zb(b,function(d,e){$H(e,c)})})},dI=function(a,b){var c=""+b;if(VH[c])VH[c].push(a);else{var d=[a];VH[c]=d;var e=XH[c];e||(e={},XH[c]=e);WH.push(function(f){var g=f.target;if(g){var h=BD(g);if(h){var m=bI(h,"gtmFormInteractId",function(){return YH++}),n=bI(g,"gtmFormInteractFieldId",function(){return ZH++});if(m!==null&&n!==null){var p=e[m];p?(p.Ua&&(w.clearTimeout(p.Ua),p.bc.getAttribute("data-gtm-form-interact-field-id")!==n&&$H(p,d)),p.bc=g,cI(p,
d,b)):(e[m]={form:h,bc:g,sequenceNumber:0,Ua:null},cI(e[m],d,b))}}}})}},$H=function(a,b){var c=a.form,d=a.bc,e=xD(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
AD(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;HB(e);a.sequenceNumber++;a.Ua=null},cI=function(a,b,c){c?a.Ua=w.setTimeout(function(){$H(a,b)},c):$H(a,b)},bI=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function eI(a,b){var c=this;if(!jh(a))throw I(this.getName(),["Object|undefined","any"],arguments);lD([function(){L(c,"detect_form_interaction_events")}]);var d=rD(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(sD("fil","init",!1)){var f=sD("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else Qc(z,"change",function(g){for(var h=0;h<WH.length;h++)WH[h](g)}),Qc(w,"pagehide",function(){aI()}),
dI(d,e),tD("fil","reg",dI),tD("fil","init",!0);return d}eI.K="internal.enableAutoEventOnFormInteraction";
var fI=function(a,b,c,d,e){var f=sD("fsl",c?"nv.mwt":"mwt",0),g;g=c?sD("fsl","nv.ids",[]):sD("fsl","ids",[]);if(!g.length)return!0;var h=xD(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);P(121);if(m==="https://www.facebook.com/tr/")return P(122),!0;h["gtm.elementUrl"]=m;h["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(h["gtm.interactedFormName"]=a.getAttribute("name"));e&&(h["gtm.formSubmitElement"]=e,h["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!GB(h,IB(b,
f),f))return!1}else GB(h,function(){},f||2E3);return!0},gI=function(){var a=[],b=function(c){return vb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},hI=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},iI=function(){var a=gI(),b=HTMLFormElement.prototype.submit;Qc(z,"click",function(c){var d=c.target;if(d){var e=Wc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&Tc(e,"value")){var f=BD(e);f&&a.store(f,e)}}},!1);Qc(z,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=hI(d)&&!e,g=a.get(d),h=!0;if(fI(d,function(){if(h){var m=null,n={};g&&(m=z.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),qc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
qc(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))h=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;fI(c,function(){d&&b.call(c)},!1,hI(c))&&(b.call(c),d=
!1)}};
function jI(a,b){var c=this;if(!jh(a))throw I(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");lD([function(){L(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=rD(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var h=function(n){return Math.max(g,n)};wD("fsl","mwt",h,0);e||wD("fsl","nv.mwt",h,0)}var m=function(n){n.push(f);
return n};wD("fsl","ids",m,[]);e||wD("fsl","nv.ids",m,[]);sD("fsl","init",!1)||(iI(),tD("fsl","init",!0));return f}jI.K="internal.enableAutoEventOnFormSubmit";
function oI(){var a=this;}oI.K="internal.enableAutoEventOnGaSend";var pI={},qI=[];
var sI=function(a,b){var c=""+b;if(pI[c])pI[c].push(a);else{var d=[a];pI[c]=d;var e=rI("gtm.historyChange-v2"),f=-1;qI.push(function(g){f>=0&&w.clearTimeout(f);b?f=w.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},rI=function(a){var b=w.location.href,c={source:null,state:w.history.state||null,url:pj(sj(b)),eb:mj(sj(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.eb!==d.eb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.eb,
"gtm.newUrlFragment":d.eb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;HB(h)}}},tI=function(a,b){var c=w.history,d=c[a];if(qb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=w.location.href;b({source:a,state:e,url:pj(sj(h)),eb:mj(sj(h),"fragment")})}}catch(e){}},vI=function(a){w.addEventListener("popstate",function(b){var c=uI(b);a({source:"popstate",state:b.state,url:pj(sj(c)),eb:mj(sj(c),
"fragment")})})},wI=function(a){w.addEventListener("hashchange",function(b){var c=uI(b);a({source:"hashchange",state:null,url:pj(sj(c)),eb:mj(sj(c),"fragment")})})},uI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||w.location.href};
function xI(a,b){var c=this;if(!jh(a))throw I(this.getName(),["Object|undefined","any"],arguments);lD([function(){L(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!sD(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<qI.length;n++)qI[n](m)},f=rD(b),sI(f,e),tD(d,"reg",sI)):g=rI("gtm.historyChange");wI(g);vI(g);tI("pushState",
g);tI("replaceState",g);tD(d,"init",!0)}else if(d==="ehl"){var h=sD(d,"reg");h&&(f=rD(b),h(f,e))}d==="hl"&&(f=void 0);return f}xI.K="internal.enableAutoEventOnHistoryChange";var yI=["http://","https://","javascript:","file://"];
var zI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=ed(b,"href");if(c.indexOf(":")!==-1&&!yI.some(function(h){return Lb(c,h)}))return!1;var d=c.indexOf("#"),e=ed(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=pj(sj(c)),g=pj(sj(w.location.href));return f!==g}return!0},AI=function(a,b){for(var c=mj(sj((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||ed(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},BI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Wc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=sD("lcl",e?"nv.mwt":"mwt",0),g;g=e?sD("lcl","nv.ids",[]):sD("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=sD("lcl","aff.map",{})[n];p&&!AI(p,d)||h.push(n)}if(h.length){var q=zI(c,d),r=xD(d,"gtm.linkClick",
h);r["gtm.elementText"]=Uc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var u=!!vb(String(ed(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),t=w[(ed(d,"target")||"_self").substring(1)],v=!0,x=IB(function(){var y;if(y=v&&t){var A;a:if(u){var D;try{D=new MouseEvent(c.type,{bubbles:!0})}catch(E){if(!z.createEvent){A=!1;break a}D=z.createEvent("MouseEvents");D.initEvent(c.type,!0,!0)}D.C=!0;c.target.dispatchEvent(D);A=!0}else A=!1;y=!A}y&&(t.location.href=ed(d,
"href"))},f);if(GB(r,x,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else GB(r,function(){},f||2E3);return!0}}}var b=0;Qc(z,"click",a,!1);Qc(z,"auxclick",a,!1)};
function CI(a,b){var c=this;if(!jh(a))throw I(this.getName(),["Object|undefined","any"],arguments);var d=B(a);lD([function(){L(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=rD(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};wD("lcl","mwt",n,0);f||wD("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};wD("lcl","ids",p,[]);f||wD("lcl","nv.ids",p,[]);g&&wD("lcl","aff.map",function(q){q[h]=g;return q},{});sD("lcl","init",!1)||(BI(),tD("lcl","init",!0));return h}CI.K="internal.enableAutoEventOnLinkClick";var DI,EI;
var FI=function(a){return sD("sdl",a,{})},GI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];wD("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},JI=function(){function a(){HI();II(a,!0)}return a},KI=function(){function a(){f?e=w.setTimeout(a,c):(e=0,HI(),II(b));f=!1}function b(){d&&DI();e?f=!0:(e=w.setTimeout(a,c),tD("sdl","pending",!0))}var c=250,d=!1;z.scrollingElement&&z.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
II=function(a,b){sD("sdl","init",!1)&&!LI()&&(b?Rc(w,"scrollend",a):Rc(w,"scroll",a),Rc(w,"resize",a),tD("sdl","init",!1))},HI=function(){var a=DI(),b=a.depthX,c=a.depthY,d=b/EI.scrollWidth*100,e=c/EI.scrollHeight*100;MI(b,"horiz.pix","PIXELS","horizontal");MI(d,"horiz.pct","PERCENT","horizontal");MI(c,"vert.pix","PIXELS","vertical");MI(e,"vert.pct","PERCENT","vertical");tD("sdl","pending",!1)},MI=function(a,b,c,d){var e=FI(b),f={},g;for(g in e)if(f={He:f.He},f.He=g,e.hasOwnProperty(f.He)){var h=
Number(f.He);if(!(a<h)){var m={};PB((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.He].join(","),m));wD("sdl",b,function(n){return function(p){delete p[n.He];return p}}(f),{})}}},OI=function(){wD("sdl","scr",function(a){a||(a=z.scrollingElement||z.body&&z.body.parentNode);return EI=a},!1);wD("sdl","depth",function(a){a||(a=NI());return DI=a},!1)},NI=function(){var a=0,b=0;return function(){var c=gG(),d=c.height;
a=Math.max(EI.scrollLeft+c.width,a);b=Math.max(EI.scrollTop+d,b);return{depthX:a,depthY:b}}},LI=function(){return!!(Object.keys(FI("horiz.pix")).length||Object.keys(FI("horiz.pct")).length||Object.keys(FI("vert.pix")).length||Object.keys(FI("vert.pct")).length)};
function PI(a,b){var c=this;if(!ih(a))throw I(this.getName(),["Object","any"],arguments);lD([function(){L(c,"detect_scroll_events")}]);OI();if(!EI)return;var d=rD(b),e=B(a);switch(e.horizontalThresholdUnits){case "PIXELS":GI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":GI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":GI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":GI(e.verticalThresholds,
d,"vert.pct")}sD("sdl","init",!1)?sD("sdl","pending",!1)||Sc(function(){HI()}):(tD("sdl","init",!0),tD("sdl","pending",!0),Sc(function(){HI();if(LI()){var f=KI();"onscrollend"in w?(f=JI(),Qc(w,"scrollend",f)):Qc(w,"scroll",f);Qc(w,"resize",f)}else tD("sdl","init",!1)}));return d}PI.K="internal.enableAutoEventOnScroll";function QI(a){return function(){if(a.limit&&a.zj>=a.limit)a.yh&&w.clearInterval(a.yh);else{a.zj++;var b=Gb();HB({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.zj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.sn,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.sn,"gtm.triggers":a.yr})}}}
function RI(a,b){
return f}RI.K="internal.enableAutoEventOnTimer";
var SI=function(a,b,c){function d(){var g=a();f+=e?(Gb()-e)*g.playbackRate/1E3:0;e=Gb()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.Lm,q=m?Math.round(m):h?Math.round(n.Lm*h):Math.round(n.Pp),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),u=z.hidden?!1:hG(c)>=.5;d();var t=void 0;b!==void 0&&(t=[b]);var v=xD(c,"gtm.video",t);v["gtm.videoProvider"]="youtube";v["gtm.videoStatus"]=g;v["gtm.videoUrl"]=n.url;v["gtm.videoTitle"]=n.title;v["gtm.videoDuration"]=Math.round(p);v["gtm.videoCurrentTime"]=
Math.round(q);v["gtm.videoElapsedTime"]=Math.round(f);v["gtm.videoPercent"]=r;v["gtm.videoVisible"]=u;return v},Xq:function(){e=Gb()},Ui:function(){d()}}};var tc=Da(["data-gtm-yt-inspected-"]),TI=["www.youtube.com","www.youtube-nocookie.com"],UI,VI=!1;
var WI=function(a,b,c){var d=a.map(function(g){return{Nd:g,pn:g,dn:void 0}});if(!b.length)return d;var e=b.map(function(g){return{Nd:g*c,pn:void 0,dn:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.Nd-h.Nd});return f},XI=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},YI=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},ZI=function(a,b){var c,d;function e(){u=SI(function(){return{url:x,title:y,Lm:v,Pp:a.getCurrentTime(),playbackRate:A}},b.Ie,a.getIframe());v=0;y=x="";A=1;return f}function f(F){switch(F){case 1:v=Math.round(a.getDuration());x=a.getVideoUrl();if(a.getVideoData){var M=a.getVideoData();y=M?M.title:""}A=a.getPlaybackRate();if(b.Gp){var U=u.createEvent("start");HB(U)}else u.Ui();t=WI(b.Uq,b.Tq,a.getDuration());return g(F);default:return f}}function g(){D=a.getCurrentTime();E=Fb().getTime();
u.Xq();r();return h}function h(F){var M;switch(F){case 0:return n(F);case 2:M="pause";case 3:var U=a.getCurrentTime()-D;M=Math.abs((Fb().getTime()-E)/1E3*A-U)>1?"seek":M||"buffering";if(a.getCurrentTime())if(b.Fp){var ha=u.createEvent(M);HB(ha)}else u.Ui();q();return m;case -1:return e(F);default:return h}}function m(F){switch(F){case 0:return n(F);case 1:return g(F);case -1:return e(F);default:return m}}function n(){for(;d;){var F=c;w.clearTimeout(d);F()}if(b.Ep){var M=u.createEvent("complete",1);
HB(M)}return e(-1)}function p(){}function q(){d&&(w.clearTimeout(d),d=0,c=p)}function r(){if(t.length&&A!==0){var F=-1,M;do{M=t[0];if(M.Nd>a.getDuration())return;F=(M.Nd-a.getCurrentTime())/A;if(F<0&&(t.shift(),t.length===0))return}while(F<0);c=function(){d=0;c=p;if(t.length>0&&t[0].Nd===M.Nd){t.shift();var U=u.createEvent("progress",M.dn,M.pn);HB(U)}r()};d=w.setTimeout(c,F*1E3)}}var u,t=[],v,x,y,A,D,E,J=e(-1);d=0;c=p;return{onStateChange:function(F){J=J(F)},onPlaybackRateChange:function(F){D=a.getCurrentTime();
E=Fb().getTime();u.Ui();A=F;q();r()}}},aJ=function(a){Sc(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)$I(d[f],a)}var c=z;b();OH(b)})},$I=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.Ie)&&(vc(a,"data-gtm-yt-inspected-"+b.Ie),bJ(a,b.Pm))){a.id||(a.id=cJ());var c=w.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=ZI(d,b),f={},g;for(g in e)f={kg:f.kg},f.kg=g,e.hasOwnProperty(f.kg)&&d.addEventListener(f.kg,function(h){return function(m){return e[h.kg](m.data)}}(f))}},
bJ=function(a,b){var c=a.getAttribute("src");if(dJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(UI||(UI=z.location.protocol+"//"+z.location.hostname,z.location.port&&(UI+=":"+z.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(UI));var f;f=ac(d);a.src=bc(f).toString();return!0}}return!1},dJ=function(a,b){if(!a)return!1;for(var c=0;c<TI.length;c++)if(a.indexOf("//"+TI[c]+"/"+b)>=0)return!0;
return!1},cJ=function(){var a=""+Math.round(Math.random()*1E9);return z.getElementById(a)?cJ():a};
function eJ(a,b){var c=this;var d=function(){aJ(q)};if(!ih(a))throw I(this.getName(),["Object","any"],arguments);lD([function(){L(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=rD(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=YI(B(a.get("progressThresholdsPercent"))),n=XI(B(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={Gp:f,Ep:g,Fp:h,Tq:m,Uq:n,Pm:p,Ie:e},r=w.YT;if(r)return r.ready&&r.ready(d),e;var u=w,t=u.onYouTubeIframeAPIReady;u.onYouTubeIframeAPIReady=function(){t&&t();d()};Sc(function(){for(var v=z.getElementsByTagName("script"),x=v.length,y=0;y<x;y++){var A=v[y].getAttribute("src");if(dJ(A,"iframe_api")||dJ(A,"player_api"))return e}for(var D=z.getElementsByTagName("iframe"),E=D.length,J=0;J<E;J++)if(!VI&&bJ(D[J],q.Pm))return Lc("https://www.youtube.com/iframe_api"),
VI=!0,e});return e}eJ.K="internal.enableAutoEventOnYouTubeActivity";VI=!1;function fJ(a,b){if(!K(a)||!jh(b))throw I(this.getName(),["string","Object|undefined"],arguments);var c=b?B(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Kh(f,c);return e}fJ.K="internal.evaluateBooleanExpression";var gJ;function hJ(a){var b=!1;return b}hJ.K="internal.evaluateMatchingRules";var sJ=function(a,b){var c=a.D;if(b===void 0?0:b){var d=c.getMergedValues(N.m.Ea);Pb(d)&&W(a,N.m.Qg,Pb(d))}var e=c.getMergedValues(N.m.Ea,1,Om(Dp.C[N.m.Ea])),f=c.getMergedValues(N.m.Ea,2),g=Pb(e,"."),h=Pb(f,".");g&&W(a,N.m.Cc,g);h&&W(a,N.m.Ac,h)};var tJ="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function uJ(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function vJ(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=oa(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function wJ(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function xJ(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function yJ(a){if(!xJ(a))return null;var b=uJ(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(tJ).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var zJ=function(a){var b={};b[N.m.xf]=a.architecture;b[N.m.yf]=a.bitness;a.fullVersionList&&(b[N.m.zf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[N.m.Af]=a.mobile?"1":"0";b[N.m.Bf]=a.model;b[N.m.Cf]=a.platform;b[N.m.Df]=a.platformVersion;b[N.m.Ef]=a.wow64?"1":"0";return b},AJ=function(a){var b=0,c=function(h,m){try{a(h,m)}catch(n){}},d=w,e=vJ(d);if(e)c(e);else{var f=wJ(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),
1E3);var g=d.setTimeout(function(){c.jg||(c.jg=!0,P(106),c(null,Error("Timeout")))},b);f.then(function(h){c.jg||(c.jg=!0,P(104),d.clearTimeout(g),c(h))}).catch(function(h){c.jg||(c.jg=!0,P(105),d.clearTimeout(g),c(null,h))})}else c(null)}},CJ=function(){var a=w;if(xJ(a)&&(BJ=Gb(),!wJ(a))){var b=yJ(a);b&&(b.then(function(){P(95)}),b.catch(function(){P(96)}))}},BJ;var DJ=function(a){if(!xJ(w))P(87);else if(BJ!==void 0){P(85);var b=vJ(w);if(b){if(b)for(var c=zJ(b),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(a,f,c[f])}}else P(86)}};var FJ=function(a){var b=EJ[a.target.destinationId];if(!a.isAborted&&b)for(var c=QF(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},GJ=function(a,b){var c=EJ[a];c||(c=EJ[a]=[]);c.push(b)},EJ={};var HJ=function(a){FJ(a);};function IJ(){var a=w.__uspapi;if(qb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var JJ=function(a){if(a.eventName===N.m.na||G(268))if(G(24)){var b=wn(Fv);V(a,R.A.ye,Q(a.D,N.m.Ha)!=null&&Q(a.D,N.m.Ha)!==!1&&!b);var c=T(a,R.A.gh),d=Q(a.D,N.m.kb)!==!1,e=Bu(a);d||W(a,N.m.Vh,"1");var f=qt(e.prefix);if(a.eventName===N.m.na&&!T(a,R.A.ia)&&!T(a,R.A.Sf)&&!T(a,R.A.xe)){var g=Q(a.D,N.m.Db),h=Q(a.D,N.m.Xa)||{};Cu({Ae:d,Fe:h,Je:g,Oc:e});if(!c)if(ju(f))G(268)&&(V(a,R.A.Ih,!0),W(a,"_&apvc","1"));else if(!G(268)){a.isAborted=!0;return}}if(c)a.isAborted=!0;else{a.target.destinationId&&W(a,N.m.Vg,
a.target.destinationId);W(a,N.m.Bc,a.eventName);a.eventName===N.m.na&&W(a,N.m.Bc,N.m.Yc);if(T(a,R.A.ia))W(a,N.m.Bc,N.m.Wn),W(a,N.m.ia,"1");else if(T(a,R.A.Sf))W(a,N.m.Bc,N.m.io);else if(T(a,R.A.xe))W(a,N.m.Bc,N.m.eo);else{var m=Jt();W(a,N.m.Zc,m.gclid);W(a,N.m.gd,m.dclid);W(a,N.m.yk,m.gclsrc);Eu(a,N.m.Zc)||Eu(a,N.m.gd)||(W(a,N.m.ce,m.wbraid),W(a,N.m.Se,m.gbraid));W(a,N.m.Ya,Ot());W(a,N.m.za,ou());if(G(27)&&Cc){var n=mj(sj(Cc),"host");n&&W(a,N.m.il,n)}if(!T(a,R.A.xe)){var p=lu();W(a,N.m.Qe,p.De);W(a,
N.m.Re,p.Qm)}W(a,N.m.Ec,Wp(!0));var q=Nv();Mv(q)&&W(a,N.m.jd,"1");W(a,N.m.Bk,nv());fs(!1)._up==="1"&&W(a,N.m.Tk,"1")}gm=!0;W(a,N.m.Cb);W(a,N.m.bd);b&&(W(a,N.m.Cb,Ou()),d&&(ts(e),W(a,N.m.bd,rs[us(e.prefix)])));W(a,N.m.oc);W(a,N.m.sb);if(!Eu(a,N.m.Zc)&&!Eu(a,N.m.gd)&&gv(f)){var r=ot(e);r.length>0&&W(a,N.m.oc,r.join("."))}else if(!Eu(a,N.m.ce)&&b){var u=mt(f+"_aw");u.length>0&&W(a,N.m.sb,u.join("."))}W(a,N.m.Wk,gd());a.D.isGtmEvent&&(a.D.C[N.m.Ob]=Dp.C[N.m.Ob]);Cq(a.D)?W(a,N.m.Dd,!1):W(a,N.m.Dd,!0);
V(a,R.A.tg,!0);var t=IJ();t!==void 0&&W(a,N.m.Ff,t||"error");var v=vq();v&&W(a,N.m.ie,v);if(G(137))try{var x=Intl.DateTimeFormat().resolvedOptions().timeZone;W(a,N.m.ki,x||"-")}catch(E){W(a,N.m.ki,"e")}var y=uq();y&&W(a,N.m.oe,y);var A=HC.gppString;A&&W(a,N.m.nf,A);var D=HC.C;D&&W(a,N.m.lf,D);V(a,R.A.Aa,!1)}}else a.isAborted=!0;else a.isAborted=!0};
var KJ=function(a,b,c){b=b===void 0?!0:b;c=c===void 0?{}:c;if(a.eventName===N.m.nc&&!a.D.isGtmEvent){var d=Q(a.D,N.m.jf);if(typeof d==="function"&&!T(a,R.A.ia)){var e=String(Q(a.D,N.m.kf)),f=e;c[e]&&(f=c[e]);var g=Eu(a,f)||Q(a.D,e);if(b){if(typeof d==="function")if(e===N.m.sb&&g!==void 0){var h=g.split(".");h.length===0?d(void 0):h.length===1?d(h[0]):d(h)}else if(e===N.m.Lo&&G(258)){var m,n={};wn(Fv)&&(n.auid=Eu(a,N.m.bd));var p=Nv();if(Mv(p))n.gad_source=p.De,n.gad_campaignid=p.rh,n.session_start_time_usec=
(Date.now()*1E3).toString(),n.landing_page_url=w.location.href,n.landing_page_referrer=z.referrer,n.landing_page_user_agent=zc.userAgent;else{var q=T(a,R.A.Da);n.gad_source=ev(q.prefix).Yf}m=btoa(JSON.stringify(n)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");d(m)}else d(g)}else d(g)}a.isAborted=!0}};var LJ=function(a){if(T(a,R.A.Sd)&&wn(Fv)&&(T(a,R.A.ba)!==O.N.Hb||!G(4))){var b=T(a,R.A.Da),c=T(a,R.A.ba)!==O.N.Hb&&T(a,R.A.ba)!==O.N.Vb&&T(a,R.A.ba)!==O.N.zb&&a.eventName!==N.m.nc;ts(b,c);W(a,N.m.bd,rs[us(b.prefix)])}};var MJ=function(a){V(a,R.A.Sd,Q(a.D,N.m.kb)!==!1);V(a,R.A.Da,Bu(a));V(a,R.A.Lc,Q(a.D,N.m.Ha)!=null&&Q(a.D,N.m.Ha)!==!1);V(a,R.A.Vc,Cq(a.D))};function NJ(a){var b=ob("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,N.m.pf,b),mb())};var OJ=function(a){T(a,R.A.Vc)?W(a,N.m.Dd,"0"):W(a,N.m.Dd,"1")};var PJ=function(a,b){if(b===void 0||b){var c=IJ();c!==void 0&&W(a,N.m.Ff,c||"error")}var d=vq();d&&W(a,N.m.ie,d);var e=uq();e&&W(a,N.m.oe,e)};var QJ=function(a){fs(!1)._up==="1"&&W(a,N.m.gi,"1")};
var RJ=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},SJ=function(a,b,c){if(a!==void 0)return Array.isArray(a)?a.map(function(){return{mode:"m",location:b,selector:c}}):{mode:"m",location:b,selector:c}},TJ=function(a,b,c,d,e){if(!c)return!1;for(var f=String(c.value),g,h=void 0,m=f.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(E){return E.trim()}).filter(function(E){return E&&!Lb(E,"#")&&!Lb(E,".")}),n=0;n<m.length;n++){var p=m[n];if(Lb(p,"dataLayer."))g=cp(p.substring(10)),
h=SJ(g,"d",p);else{var q=p.split(".");g=w[q.shift()];for(var r=0;r<q.length;r++)g=g&&g[q[r]];h=SJ(g,"j",p)}if(g!==void 0)break}if(g===void 0&&aG)try{var u=aG?z.querySelectorAll(f):null;if(u&&u.length>0){g=[];for(var t=0;t<u.length&&t<(b==="email"||b==="phone_number"?5:1);t++)g.push(Uc(u[t])||Eb(u[t].value));g=g.length===1?g[0]:g;h=SJ(g,"c",f)}}catch(E){P(149)}if(G(60)){for(var v,x,y=0;y<m.length;y++){var A=m[y];v=cp(A);if(v!==void 0){x=SJ(v,"d",A);break}}var D=g!==void 0;e[b]=RJ(v!==void 0,D);D||
(g=v,h=x)}return g?(a[b]=g,d&&h&&(d[b]=h),!0):!1},UJ={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};
var VJ=function(a,b){b=b===void 0?!1:b;if(Uu(a,"ccd_add_1p_data",!1)&&wn(Fv)){var c=a.D.M[N.m.rl];if(sd(c)&&c.enable_code){var d=Q(a.D,N.m.yb);if(d===null)V(a,R.A.Em,null);else{if(c.enable_code&&sd(d)){if(G(178)&&d){Lw(Jw,d);for(var e=ub(d.address),f=0;f<e.length;f++){var g=e[f];g&&Lw(Kw,g)}var h=d.home_address;h&&Lw(Kw,h)}V(a,R.A.Em,d)}if(sd(c.selectors)){var m={},n=R.A.pp,p;var q=c.selectors,r=b?m:void 0,u=G(178);r=r===void 0?{}:r;u=u===void 0?!1:u;if(q){var t={},v=!1,x={};v=TJ(t,"email",q.email,
x,r)||v;v=TJ(t,"phone_number",q.phone,x,r)||v;t.address=[];for(var y=q.name_and_address||[],A=0;A<y.length;A++){var D={},E={};v=TJ(D,"first_name",y[A].first_name,E,r)||v;v=TJ(D,"last_name",y[A].last_name,E,r)||v;v=TJ(D,"street",y[A].street,E,r)||v;v=TJ(D,"city",y[A].city,E,r)||v;v=TJ(D,"region",y[A].region,E,r)||v;v=TJ(D,"country",y[A].country,E,r)||v;v=TJ(D,"postal_code",y[A].postal_code,E,r)||v;t.address.push(D);u&&(D._tag_metadata=E)}u&&(t._tag_metadata=x);p=v?t:void 0}else p=void 0;V(a,n,p);if(b){for(var J=
a.mergeHitDataForKey,F=N.m.Dc,M,U=[],ha=Object.keys(UJ),S=0;S<ha.length;S++){var aa=ha[S],ta=UJ[aa],la=void 0,ea=(la=m[aa])!=null?la:"0";U.push(ta+"-"+ea)}M=U.join("~");J.call(a,F,{ec_data_layer:M})}}}}}};function wK(){return wq(7)&&wq(9)&&wq(10)};function rL(a,b,c,d){}rL.K="internal.executeEventProcessor";function sL(a){var b;return Id(b,this.J,1)}sL.K="internal.executeJavascriptString";function tL(a){var b;return b};function uL(a){var b="";return b}uL.K="internal.generateClientId";function vL(a){var b={};return Id(b)}vL.K="internal.getAdsCookieWritingOptions";function wL(a,b){var c=!1;return c}wL.K="internal.getAllowAdPersonalization";function xL(){var a;return a}xL.K="internal.getAndResetEventUsage";function yL(a,b){b=b===void 0?!0:b;var c;return c}yL.K="internal.getAuid";var zL=null;function AL(){var a=new cb;L(this,"read_container_data"),G(49)&&zL?a=zL:(a.set("containerId",Fi(5)),a.set("version",Fi(1)),a.set("environmentName",Fi(20)),a.set("debugMode",tg),a.set("previewMode",Ei(29)),a.set("environmentMode",Ei(28)),a.set("firstPartyServing",fj()||Hi.C),a.set("containerUrl",Cc),a.Ta(),G(49)&&(zL=a));return a}AL.publicName="getContainerVersion";function BL(a,b){b=b===void 0?!0:b;var c;return c}BL.publicName="getCookieValues";function CL(){var a="";return a}CL.K="internal.getCorePlatformServicesParam";function DL(){return um()}DL.K="internal.getCountryCode";function EL(){var a=[];a=Ej();return Id(a)}EL.K="internal.getDestinationIds";function FL(a){var b=new cb;return b}FL.K="internal.getDeveloperIds";function GL(a){var b;return b}GL.K="internal.getEcsidCookieValue";function HL(a,b){var c=null;return c}HL.K="internal.getElementAttribute";function IL(a){var b=null;return b}IL.K="internal.getElementById";function JL(a){var b="";return b}JL.K="internal.getElementInnerText";function KL(a,b){var c=null;return Id(c)}KL.K="internal.getElementProperty";function LL(a){var b;return b}LL.K="internal.getElementValue";function ML(a){var b=0;return b}ML.K="internal.getElementVisibilityRatio";function NL(a){var b=null;return b}NL.K="internal.getElementsByCssSelector";
function OL(a){var b;if(!K(a))throw I(this.getName(),["string"],arguments);L(this,"read_event_data",a);var c;a:{var d=a,e=pD(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),u=0;u<r.length;u++){for(var t=r[u].split("."),v=0;v<t.length;v++)n.push(t[v]),v!==t.length-1&&n.push(m);u!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var x=[],y="",A=l(n),D=A.next();!D.done;D=
A.next()){var E=D.value;E===m?(x.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&x.push(y);for(var J=l(x),F=J.next();!F.done;F=J.next()){if(f==null){c=void 0;break a}f=f[F.value]}c=f}else c=void 0}b=Id(c,this.J,1);return b}OL.K="internal.getEventData";function PL(a){var b=null;if(!K(a))throw I(this.getName(),["string"],arguments);L(this,"read_dom_elements","css",a);try{var c=$F(a);c&&(b=new Fd(c))}catch(d){return null}return b}PL.K="internal.getFirstElementByCssSelector";var QL={};QL.disableUserDataWithoutCcd=G(223);QL.enableDecodeUri=G(92);QL.enableGaAdsConversions=G(122);QL.enableGaAdsConversionsClientId=G(121);QL.enableOverrideAdsCps=G(170);QL.enableUrlDecodeEventUsage=G(139);function RL(){return Id(QL)}RL.K="internal.getFlags";function SL(){var a;return a}SL.K="internal.getGsaExperimentId";function TL(){return new Fd(vC)}TL.K="internal.getHtmlId";function UL(a){var b;return b}UL.K="internal.getIframingState";function VL(a,b){var c={};return Id(c)}VL.K="internal.getLinkerValueFromLocation";function WL(){var a=new cb;return a}WL.K="internal.getPrivacyStrings";function XL(a,b){var c;if(!K(a)||!K(b))throw I(this.getName(),["string","string"],arguments);var d=KF(a)||{};c=Id(d[b],this.J);return c}XL.K="internal.getProductSettingsParameter";function YL(a,b){var c;if(!K(a)||!sh(b))throw I(this.getName(),["string","boolean|undefined"],arguments);L(this,"get_url","query",a);var d=mj(sj(w.location.href),"query"),e=jj(d,a,b);c=Id(e,this.J);return c}YL.publicName="getQueryParameters";function ZL(a,b){var c;return c}ZL.publicName="getReferrerQueryParameters";function $L(a){var b="";return b}$L.publicName="getReferrerUrl";function aM(){return vm()}aM.K="internal.getRegionCode";function bM(a,b){var c;if(!K(a)||!K(b))throw I(this.getName(),["string","string"],arguments);var d=Gp(a);c=Id(d[b],this.J);return c}bM.K="internal.getRemoteConfigParameter";function cM(){var a=new cb;a.set("width",0);a.set("height",0);return a}cM.K="internal.getScreenDimensions";function dM(){var a="";return a}dM.K="internal.getTopSameDomainUrl";function eM(){var a="";return a}eM.K="internal.getTopWindowUrl";function fM(a){var b="";if(!ph(a))throw I(this.getName(),["string|undefined"],arguments);L(this,"get_url",a);b=mj(sj(w.location.href),a);return b}fM.publicName="getUrl";function gM(){L(this,"get_user_agent");return zc.userAgent}gM.K="internal.getUserAgent";function hM(){var a;return a?Id(zJ(a)):a}hM.K="internal.getUserAgentClientHints";var jM=function(a){var b=a.eventName===N.m.Yc&&tl()&&Rv(a),c=T(a,R.A.Zl),d=T(a,R.A.Tj),e=T(a,R.A.Jf),f=T(a,R.A.te),g=T(a,R.A.wg),h=T(a,R.A.Td),m=T(a,R.A.xg),n=T(a,R.A.yg),p=!!Qv(a)||!!T(a,R.A.Ph);return!(!cd()&&zc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&iM)},iM=!1;
var kM=function(a){var b=0,c=0;return{start:function(){b=Gb()},stop:function(){c=this.get()},get:function(){var d=0;a.qj()&&(d=Gb()-b);return d+c}}},lM=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.M=!1;this.U=this.R=void 0};k=lM.prototype;k.Xo=function(a){var b=this;if(!this.C){this.M=z.hasFocus();this.isVisible=!z.hidden;this.isActive=!0;var c=function(e,f,g){Qc(e,f,function(h){b.C.stop();g(h);b.qj()&&b.C.start()})},d=w;c(d,"focus",function(){b.M=!0});c(d,"blur",function(){b.M=
!1});c(d,"pageshow",function(e){b.isActive=!0;e.persisted&&P(56);b.U&&b.U()});c(d,"pagehide",function(){b.isActive=!1;b.R&&b.R()});c(z,"visibilitychange",function(){b.isVisible=!z.hidden});Rv(a)&&!Fc()&&c(d,"beforeunload",function(){iM=!0});this.Fj(!0);this.H=0}};k.Fj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.uh(),this.C=kM(this),this.qj()&&this.C.start()};k.xr=function(a){var b=this.uh();b>0&&W(a,N.m.Jg,b)};k.nq=function(a){W(a,N.m.Jg);this.Fj();this.H=0};k.qj=function(){return this.M&&
this.isVisible&&this.isActive};k.fq=function(){return this.H+this.uh()};k.uh=function(){return this.C&&this.C.get()||0};k.Vq=function(a){this.R=a};k.kn=function(a){this.U=a};var mM=function(a){kb("GA4_EVENT",a)};var nM=function(a){var b=T(a,R.A.yl);if(Array.isArray(b))for(var c=0;c<b.length;c++)mM(b[c]);var d=ob("GA4_EVENT");d&&W(a,"_eu",d)};function oM(){var a=w;return a.gaGlobal=a.gaGlobal||{}}function pM(){var a=oM();a.hid=a.hid||wb();return a.hid}function qM(a,b){var c=oM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var rM=["GA1"];
var sM=function(a,b,c){var d=T(a,R.A.Xj);if(d===void 0||c<=d)W(a,N.m.Qb,b),V(a,R.A.Xj,c)},uM=function(a,b){var c=Eu(a,N.m.Qb);if(Q(a.D,N.m.Gc)&&Q(a.D,N.m.Fc)||b&&c===b)return c;if(c){c=""+c;if(!tM(c,a))return P(31),a.isAborted=!0,"";qM(c,wn(N.m.ka));return c}P(32);a.isAborted=!0;return""},vM=function(a){var b=T(a,R.A.Da),c=b.prefix+"_ga",d=Nr(b.prefix+"_ga",b.domain,b.path,rM,N.m.ka);if(!d){var e=String(Q(a.D,N.m.ed,""));e&&e!==c&&(d=Nr(e,b.domain,b.path,rM,N.m.ka))}return d},tM=function(a,b){var c;
var d=T(b,R.A.Da),e=d.prefix+"_ga",f=Sq(d,void 0,void 0,N.m.ka);if(Q(b.D,N.m.zc)===!1&&vM(b)===a)c=!0;else{var g;g=[rM[0],Kr(d.domain,d.path),a].join(".");c=Fr(e,g,f)!==1}return c};
var yM=function(a){var b=new RegExp("^"+(((a==null?void 0:a.prefix)||"")+"_ga_\\w+$")),c=Ts(function(p){return b.test(p)}),d={},e;for(e in c)if(c.hasOwnProperty(e)){var f=wM(c[e]);if(f){var g=Ps(f,2);if(g){var h=xM(g);if(h){var m=void 0,n=(((m=a)==null?void 0:m.prefix)||"").length+4;d["G-"+e.substring(n)]=h}}}}return d},zM=function(a){if(a){var b;a:{var c=(Lb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=Ns(c,2);break a}catch(d){}b=void 0}return b}},wM=function(a){if(a&&a.length!==0){for(var b,
c=-Infinity,d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;if(f.t!==void 0){var g=Number(f.t);!isNaN(g)&&g>c&&(c=g,b=f)}}return b}},Us=function(a){a&&(a==="GS1"?mM(H.P.Kl):a==="GS2"&&mM(H.P.Ll))},xM=function(a){var b=zM(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||mM(H.P.Sl);d||mM(H.P.Rl);isNaN(e)&&mM(H.P.Ql);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",
m.h=g,m}}};
var BM=function(a,b,c){if(!b)return a;if(!a)return b;var d=xM(a);if(!d)return b;var e,f=Bb((e=Q(c.D,N.m.vf))!=null?e:30),g=T(c,R.A.fb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=xM(b);if(!h)return a;h.o=d.o+1;var m;return(m=AM(h))!=null?m:b},DM=function(a,b){var c=T(b,R.A.Da),d=CM(b,c),e=zM(a);if(!e)return!1;var f=Sq(c||{},void 0,void 0,Qs.get(2));Fr(d,void 0,f);return Vs(d,e,2,c)!==1},EM=function(a){var b=T(a,R.A.Da),c;var d=CM(a,b),e;b:{var f=Us,g=Ms[2];if(g){var h,m=Ir(b.domain),n=Jr(b.path),
p=Object.keys(g.Gh),q=Qs.get(2),r;if(h=(r=xr(d,m,n,p,q))==null?void 0:r.Kp){var u=Ns(h,2,f);e=u?Ss(u):void 0;break b}}e=void 0}if(e){var t=Rs(d,2,Us);if(t&&t.length>1){mM(H.P.Jl);var v=wM(t);v&&v.t!==e.t&&(mM(H.P.Ml),e=v)}c=Ps(e,2)}else c=void 0;return c},FM=function(a){var b=T(a,R.A.fb),c={};c.s=Eu(a,N.m.Tb);c.o=Eu(a,N.m.Ug);var d;d=Eu(a,N.m.Tg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=T(a,R.A.Lf),c.j=T(a,R.A.Mf)||0,c.l=!!T(a,N.m.bi),c.h=Eu(a,N.m.Kg),c);return AM(e)},AM=function(a){if(a.s&&a.o){var b=
{},c=(b.s=a.s,b.o=String(a.o),b.g=Bb(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Ps(c,2)}},CM=function(a,b){return b.prefix+"_ga_"+a.target.ids[qo[6]]};
var GM=function(a){var b=Q(a.D,N.m.Xa),c=a.D.M[N.m.Xa];if(c===b)return c;var d=td(b,null);c&&c[N.m.oa]&&(d[N.m.oa]=(d[N.m.oa]||[]).concat(c[N.m.oa]));return d},HM=function(a,b){var c=fs(!0);return c._up!=="1"?{}:{clientId:c[a],pb:c[b]}},IM=function(a,b,c){var d=fs(!0),e=d[b];e&&(sM(a,e,2),tM(e,a));var f=d[c];f&&DM(f,a);return{clientId:e,pb:f}},JM=function(){var a=oj(w.location,"host"),b=oj(sj(z.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},KM=function(a){if(!Q(a.D,
N.m.Db))return{};var b=T(a,R.A.Da),c=b.prefix+"_ga",d=CM(a,b);ns(function(){var e;if(wn("analytics_storage"))e={};else{var f={_up:"1"},g;g=Eu(a,N.m.Qb);e=(f[c]=g,f[d]=FM(a),f)}return e},1);return!wn("analytics_storage")&&JM()?HM(c,d):{}},MM=function(a){var b=GM(a)||{},c=T(a,R.A.Da),d=c.prefix+"_ga",e=CM(a,c),f={};ps(b[N.m.rf],!!b[N.m.oa])&&(f=IM(a,d,e),f.clientId&&f.pb&&(LM=!0));b[N.m.oa]&&ms(function(){var g={},h=vM(a);h&&(g[d]=h);var m=EM(a);m&&(g[e]=m);var n=tr("FPLC",void 0,void 0,N.m.ka);n.length&&
(g._fplc=n[0]);return g},b[N.m.oa],b[N.m.kd],!!b[N.m.Hc]);return f},LM=!1;var NM=function(a){if(!T(a,R.A.Bd)&&dk(a.D)){var b=GM(a)||{},c=(ps(b[N.m.rf],!!b[N.m.oa])?fs(!0)._fplc:void 0)||(tr("FPLC",void 0,void 0,N.m.ka).length>0?void 0:"0");W(a,"_fplc",c)}};function OM(a){(Rv(a)||fj())&&W(a,N.m.sl,vm()||um());!Rv(a)&&fj()&&W(a,N.m.yi,"::")}function PM(a){if(fj()&&!Rv(a)&&(ym()||W(a,N.m.Uk,!0),G(78))){Pu(a);Qu(a,lo.Gf.Jn,Rm(Q(a.D,N.m.Wa)));var b=lo.Gf.Kn;var c=Q(a.D,N.m.zc);Qu(a,b,c===!0?1:c===!1?0:void 0);Qu(a,lo.Gf.In,Rm(Q(a.D,N.m.Bb)));Qu(a,lo.Gf.Gn,Kr(Qm(Q(a.D,N.m.ub)),Qm(Q(a.D,N.m.Rb))))}};var RM=function(a,b){Tn("grl",function(){return QM()})(b)||(P(35),a.isAborted=!0)},QM=function(){var a=Gb(),b=a+864E5,c=20,d=5E3;return function(e){var f=Gb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Qp=d,e.Dp=c);return g}};
var SM=function(a){var b=Eu(a,N.m.Ya);return mj(sj(b),"host",!0)},TM=function(a){if(Q(a.D,N.m.qf)!==void 0)a.copyToHitData(N.m.qf);else{var b=Q(a.D,N.m.hi),c,d;a:{if(LM){var e=GM(a)||{};if(e&&e[N.m.oa])for(var f=SM(a),g=e[N.m.oa],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=SM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(W(a,N.m.qf,"1"),
mM(H.P.hm))}};
var UM=function(a,b){Dq()&&(a.gcs=Eq(),T(b,R.A.bh)&&(a.gcu="1"));a.gcd=Iq(b.D);a.npa=T(b,R.A.Vc)?"0":"1";Nq()&&(a._ng="1")},VM=function(a){if(T(a,R.A.Bd))return{url:ek("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=ak(dk(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=Sv(a),d=Q(a.D,N.m.Pb),e=c&&!wm()&&d!==!1&&wK()&&wn(N.m.aa)&&wn(N.m.ka)?17:16;return{url:Gw(e),endpoint:e}},WM={};WM[N.m.Qb]="cid";WM[N.m.Th]="gcut";WM[N.m.dd]="are";WM[N.m.Gg]="pscdl";
WM[N.m.di]="_fid";WM[N.m.Qk]="_geo";WM[N.m.Cc]="gdid";WM[N.m.je]="_ng";WM[N.m.Ec]="frm";WM[N.m.qf]="ir";WM[N.m.Uk]="fp";WM[N.m.xb]="ul";WM[N.m.Rg]="ni";WM[N.m.Ho]="pae";WM[N.m.Sg]="_rdi";WM[N.m.Ic]="sr";WM[N.m.Vg]="tid";WM[N.m.mi]="tt";WM[N.m.Eb]="ec_mode";WM[N.m.Xl]="gtm_up";WM[N.m.xf]="uaa";WM[N.m.yf]="uab";WM[N.m.zf]="uafvl";WM[N.m.Af]="uamb";WM[N.m.Bf]="uam";WM[N.m.Cf]=
"uap";WM[N.m.Df]="uapv";WM[N.m.Ef]="uaw";WM[N.m.sl]="ur";WM[N.m.yi]="_uip";WM[N.m.Go]="_prs";WM[N.m.jd]="lps";WM[N.m.Zd]="gclgs";WM[N.m.be]="gclst";WM[N.m.ae]="gcllp";var XM={};XM[N.m.Ue]="cc";XM[N.m.Ve]="ci";XM[N.m.We]="cm";XM[N.m.Xe]="cn";XM[N.m.Ze]="cs";XM[N.m.af]="ck";XM[N.m.lb]=
"cu";XM[N.m.pf]="_tu";XM[N.m.za]="dl";XM[N.m.Ya]="dr";XM[N.m.Cb]="dt";XM[N.m.Tg]="seg";XM[N.m.Tb]="sid";XM[N.m.Ug]="sct";XM[N.m.Na]="uid";G(145)&&(XM[N.m.uf]="dp");var YM={};YM[N.m.Jg]="_et";YM[N.m.Ac]="edid";G(94)&&(YM._eu="_eu");var ZM={};ZM[N.m.Ue]="cc";ZM[N.m.Ve]="ci";ZM[N.m.We]="cm";ZM[N.m.Xe]="cn";ZM[N.m.Ze]="cs";ZM[N.m.af]="ck";var $M={},aN=($M[N.m.yb]=1,
$M),bN=function(a,b,c){function d(F,M){if(M!==void 0&&!Bm.hasOwnProperty(F)){M===null&&(M="");var U;var ha=M;F!==N.m.Kg?U=!1:T(a,R.A.pe)||Rv(a)?(e.ecid=ha,U=!0):U=void 0;if(!U&&F!==N.m.bi){var S=M;M===!0&&(S="1");M===!1&&(S="0");S=String(S);var aa;if(WM[F])aa=WM[F],e[aa]=S;else if(XM[F])aa=XM[F],g[aa]=S;else if(YM[F])aa=YM[F],f[aa]=S;else if(F.charAt(0)==="_")e[F]=S;else{var ta;ZM[F]?ta=!0:F!==N.m.Ye?ta=!1:(typeof M!=="object"&&v(F,M),ta=!0);ta||v(F,M)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;
e.gtm=Rq({Pa:T(a,R.A.ab)});e._p=G(159)?$i:pM();if(c&&(c.jb||c.lj)&&(G(125)||(e.em=c.fc),c.Ib)){var h=c.Ib.Be;h&&!G(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}T(a,R.A.Td)&&(e._gaz=1);UM(e,a);Lq()&&(e.dma_cps=Jq());e.dma=Kq();gq(oq())&&(e.tcfd=Mq());var m=jo(a);m&&(g.tag_exp=m);Xj()&&(e.ptag_exp=Xj());var n=Eu(a,N.m.Cc);n&&(e.gdid=n);f.en=String(a.eventName);if(T(a,R.A.Kf)){var p=T(a,R.A.Vl);f._fv=p?2:1}T(a,R.A.fh)&&(f._nsi=1);if(T(a,R.A.te)){var q=T(a,R.A.Yl);f._ss=q?2:1}T(a,R.A.Jf)&&(f._c=1);T(a,R.A.yd)&&
(f._ee=1);if(T(a,R.A.Ul)){var r=Eu(a,N.m.ya)||Q(a.D,N.m.ya);if(Array.isArray(r))for(var u=0;u<r.length&&u<200;u++)f["pr"+(u+1)]=yg(r[u])}var t=Eu(a,N.m.Ac);t&&(f.edid=t);Kx(a,f);for(var v=function(F,M){if(typeof M!=="object"||!aN[F]){var U="ep."+F,ha="epn."+F;F=tb(M)?ha:U;var S=tb(M)?U:ha;f.hasOwnProperty(S)&&delete f[S];f[F]=String(M)}},x=l(Object.keys(a.C)),y=x.next();!y.done;y=x.next()){var A=y.value;d(A,Eu(a,A))}(function(F){Rv(a)&&typeof F==="object"&&zb(F||{},function(M,U){typeof U!=="object"&&
(e["sst."+M]=String(U))})})(Eu(a,N.m.Ni));Jx(e,Eu(a,N.m.vd));var D=Eu(a,N.m.Ub)||{};Q(a.D,N.m.Pb,void 0,4)===!1&&(e.ngs="1");zb(D,function(F,M){M!==void 0&&((M===null&&(M=""),F!==N.m.Na||g.uid)?b[F]!==M&&(f[(tb(M)?"upn.":"up.")+String(F)]=String(M),b[F]=M):g.uid=String(M))});if(fj()&&!ym()){var E=T(a,R.A.Lf);E?e._gsid=E:e.njid="1"}var J=VM(a);Kg.call(this,{ra:e,Pd:g,fj:f},J.url,J.endpoint,Rv(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};ya(bN,Kg);
var cN=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},dN=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e;return b},eN=function(a,b,c,d,e){var f=0,g=new w.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;Cy(c,
m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},gN=function(a,b,c){var d;return d=Ey(new Dy(function(e,f){var g=cN(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");bl(a,g,void 0,Gy(d,f),h)}),function(e,f){var g=cN(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&
(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?fN(a,g,void 0,d,h,Gy(d,f)):cl(a,g,void 0,h,void 0,Gy(d,f))})},hN=function(a,b,c,d,e){Wk(a,2,b);var f=gN(a,d,e);fN(a,b,c,f)},fN=function(a,b,c,d,e,f){cd()?By(a,b,c,d,e,void 0,f):eN(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},iN=function(a,b,c){var d=sj(b),e=dN(d),f=Iy(d);!G(132)||Ec("; wv")||Ec("FBAN")||Ec("FBAV")||Gc()?hN(a,f,c,e):ow(f,c,e,function(g){hN(a,f,c,
e,g)})};var jN={AW:Il.X.xn,G:Il.X.Ro,DC:Il.X.No};function kN(a){var b=ex(a);return""+Nh(b.map(function(c){return c.value}).join("!"))}function lN(a){var b=oo(a);return b&&jN[b.prefix]}function mN(a,b){var c=a[b];c&&(c.clearTimerId&&w.clearTimeout(c.clearTimerId),c.clearTimerId=w.setTimeout(function(){delete a[b]},36E5))};
var nN=function(a,b,c,d){var e=a+"?"+b;d?al(c,e,d):$k(c,e)},pN=function(a,b,c,d,e){var f=b,g=fd();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;oN&&(d=!Lb(h,Fw())&&!Lb(h,Ew()));if(d&&!iM)iN(e,h,c);else{var m=b;cd()?cl(e,a+"?"+m,c,{Ch:!0})||nN(a,m,e,c):nN(a,m,e,c)}},qN=function(a,b){function c(y){r.push(y+"="+encodeURIComponent(""+a.ra[y]))}var d=b.er,e=b.ir,f=b.hr,g=b.gr,h=b.hq,m=b.zq,n=b.yq,p=b.Xp,q=b.qr;if(d||e||f||g){var r=[];a.ra._ng&&c("_ng");c("tid");c("cid");c("gtm");r.push("aip=1");
a.Pd.uid&&!n&&r.push("uid="+encodeURIComponent(""+a.Pd.uid));c("dma");a.ra.dma_cps!=null&&c("dma_cps");a.ra.gcs!=null&&c("gcs");c("gcd");a.ra.npa!=null&&c("npa");a.ra.frm!=null&&c("frm");d&&(q&&r.push("tag_exp="+q),Xj()&&r.push("ptag_exp="+Xj()),nN("https://stats.g.doubleclick.net/g/collect","v=2&"+r.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),hn({targetId:String(a.ra.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+r.join("&"),
parameterEncoding:2,endpoint:19},ib:b.ib}));if(e&&(q&&r.push("tag_exp="+q),Xj()&&r.push("ptag_exp="+Xj()),r.push("z="+wb()),!m)){var u=h&&Lb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(u){var t=u+r.join("&");bl({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);hn({targetId:String(a.ra.tid),request:{url:t,parameterEncoding:2,endpoint:47},ib:b.ib})}}if(f){var v="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");r=[];c("_gsid");c("gtm");a.ra._geo&&c("_geo");nN(v,r.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});hn({targetId:String(a.ra.tid),request:{url:v+"?"+r.join("&"),parameterEncoding:2,endpoint:18},ib:b.ib})}if(g){r=[];r.push("v=2");c("_gsid");c("gtm");a.ra._geo&&c("_geo");var x="https://{ga4CollectionSubdomain.}google-analytics.com/g/s/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");nN(x,r.join("&"),{destinationId:a.destinationId||
"",endpoint:62,eventId:a.eventId,priorityId:a.priorityId});hn({targetId:String(a.ra.tid),request:{url:x+"?"+r.join("&"),parameterEncoding:2,endpoint:62},ib:b.ib})}}},oN=!1;var rN=function(){this.M=1;this.R={};this.H=-1;this.C=new Dg};k=rN.prototype;k.Lb=function(a,b){var c=this,d=new bN(a,this.R,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=jM(a),g,
h;f&&this.C.U(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=w,p=n.setTimeout,q;Rv(a)?sN?(sN=!1,q=tN):q=uN:q=5E3;this.H=p.call(n,function(){c.flush()},q)}}else{var r=Gg(d,this.M++),u=r.params,t=r.body;g=u;h=t;pN(d.baseUrl,u,t,d.M,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var v=T(a,R.A.wg),x=T(a,R.A.Td),y=T(a,R.A.yg),A=T(a,R.A.xg),D=Q(a.D,N.m.Rh)!==!1,E=Cq(a.D),J={er:v,ir:x,hr:y,gr:A,hq:zm(),ws:D,vs:E,zq:wm(),yq:T(a,R.A.pe),
ib:e,D:a.D,Xp:ym(),qr:jo(a)};qN(d,J)}qy(a.D.eventId);jn(function(){if(m){var F=Gg(d),M=F.body;g=F.params;h=M}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},ib:e,isBatched:!1}})};k.add=function(a){if(G(100)){var b=T(a,R.A.Ph);if(b){W(a,N.m.Eb,T(a,R.A.Fm));W(a,N.m.Rg,"1");this.Lb(a,b);return}}var c=Qv(a);if(G(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=lN(e);if(h){var m=kN(g);f=(Ml(h)||{})[m]}else f=void 0;var n=f;
d=n?n.sentTo[e]:void 0;if(d&&d+6E4>Gb())c=void 0,W(a,N.m.Eb);else{var p=c,q=a.target.destinationId,r=lN(q);if(r){var u=kN(p),t=Ml(r)||{},v=t[u];if(v)v.timestamp=Gb(),v.sentTo=v.sentTo||{},v.sentTo[q]=Gb(),v.pending=!0;else{var x={};t[u]={pending:!0,timestamp:Gb(),sentTo:(x[q]=Gb(),x)}}mN(t,u);Ll(r,t)}}}!c||iM||G(125)&&!G(93)?this.Lb(a):this.jr(a)};k.flush=function(){if(this.C.events.length){var a=Ig(this.C,this.M++);pN(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",
endpoint:this.C.endpoint,eventId:this.C.ja,priorityId:this.C.ma});this.C=new Dg;this.H>=0&&(w.clearTimeout(this.H),this.H=-1)}};k.Mm=function(a,b){var c=Eu(a,N.m.Eb);W(a,N.m.Eb);b.then(function(d){var e={},f=(e[R.A.Ph]=d,e[R.A.Fm]=c,e),g=YA(a.target.destinationId,N.m.Yd,a.D.C);$A(g,a.D.eventId,{eventMetadata:f})})};k.jr=function(a){var b=this,c=Qv(a);if(Cx(c)){var d=rx(c,G(93));d?G(100)?(this.Mm(a,d),this.Lb(a)):d.then(function(g){b.Lb(a,g)},function(){b.Lb(a)}):this.Lb(a)}else{var e=Bx(c);if(G(93)){var f=
nx(e);f?G(100)?(this.Mm(a,f),this.Lb(a)):f.then(function(g){b.Lb(a,g)},function(){b.Lb(a,e)}):this.Lb(a,e)}else this.Lb(a,e)}};var tN=Ai(Ji(24),500),uN=Ai(Ji(56),5E3),sN=!0;
var vN=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;vN(a+"."+f,b[f],c)}else c[a]=b;return c},wN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!wn(e)}return b},yN=function(a,b){var c=xN.filter(function(e){return!wn(e)});if(c.length){var d=wN(c);xn(c,function(){for(var e=wN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){V(b,R.A.bh,!0);var n=f.map(function(p){return Lm[p]}).join(".");n&&Ov(b,"gcut",n);a(b)}})}},zN=function(a){Rv(a)&&Ov(a,"navt",gd())},AN=function(a){Rv(a)&&Ov(a,"lpc",at())},BN=function(a){if(Rv(a)){var b=Q(a.D,N.m.Sb),c;b===!0&&(c="1");b===!1&&(c="0");c&&Ov(a,"rdp",c)}},CN=function(a){G(147)&&Rv(a)&&Q(a.D,N.m.Te,!0)===!1&&W(a,N.m.Te,0)},DN=function(a,b){if(Rv(b)){var c=T(b,R.A.Jf);(b.eventName==="page_view"||c)&&yN(a,b)}},EN=function(a){if(Rv(a)&&a.eventName===N.m.Yd&&T(a,R.A.bh)){var b=
Eu(a,N.m.Th);b&&(Ov(a,"gcut",b),Ov(a,"syn",1))}},FN=function(a){Rv(a)&&V(a,R.A.Aa,!1)},GN=function(a){Rv(a)&&(T(a,R.A.Aa)&&Ov(a,"sp",1),T(a,R.A.Vo)&&Ov(a,"syn",1),T(a,R.A.Ne)&&(Ov(a,"em_event",1),Ov(a,"sp",1)))},HN=function(a){if(Rv(a)){var b=$i;b&&Ov(a,"tft",Number(b))}},IN=function(a){function b(e){var f=vN(N.m.yb,e);zb(f,function(g,h){W(a,g,h)})}if(Rv(a)){var c=Uu(a,"ccd_add_1p_data",!1)?1:0;Ov(a,"ude",c);var d=Q(a.D,N.m.yb);d!==void 0?(b(d),W(a,N.m.Eb,"c")):b(T(a,R.A.Sa));V(a,R.A.Sa)}},JN=function(a){if(Rv(a)){var b=
IJ();b&&Ov(a,"us_privacy",b);var c=vq();c&&Ov(a,"gdpr",c);var d=uq();d&&Ov(a,"gdpr_consent",d);var e=HC.gppString;e&&Ov(a,"gpp",e);var f=HC.C;f&&Ov(a,"gpp_sid",f)}},KN=function(a){Rv(a)&&tl()&&Q(a.D,N.m.Ha)&&Ov(a,"adr",1)},LN=function(a){if(Rv(a)){var b=G(90)?ym():"";b&&Ov(a,"gcsub",b)}},MN=function(a){if(Rv(a)){Q(a.D,N.m.Pb,void 0,4)===!1&&Ov(a,"ngs",1);wm()&&Ov(a,"ga_rd",1);wK()||Ov(a,"ngst",1);var b=zm();b&&Ov(a,"etld",b)}},NN=function(a){},ON=function(a){Rv(a)&&tl()&&Ov(a,"rnd",nv())},xN=[N.m.aa,N.m.W];
var PN=function(a,b){var c;a:{var d=FM(a);if(d){if(DM(d,a)){c=d;break a}P(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:uM(a,b),pb:e}},QN=function(a,b,c,d,e){var f=Qm(Q(a.D,N.m.Qb));if(Q(a.D,N.m.Gc)&&Q(a.D,N.m.Fc))f?sM(a,f,1):(P(127),a.isAborted=!0);else{var g=f?1:8;V(a,R.A.fh,!1);f||(f=vM(a),g=3);f||(f=b,g=5);if(!f){var h=wn(N.m.ka),m=oM();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=Mr(),g=7,V(a,R.A.Kf,!0),V(a,R.A.fh,!0));sM(a,f,g)}var n=T(a,R.A.fb),p=Math.floor(n/1E3),q=void 0;T(a,R.A.fh)||
(q=EM(a)||c);var r=Bb(Q(a.D,N.m.vf,30));r=Math.min(475,r);r=Math.max(5,r);var u=Bb(Q(a.D,N.m.ji,1E4)),t=xM(q);V(a,R.A.Kf,!1);V(a,R.A.te,!1);V(a,R.A.Mf,0);t&&t.j&&V(a,R.A.Mf,Math.max(0,t.j-Math.max(0,p-t.t)));var v=!1;if(!t){V(a,R.A.Kf,!0);v=!0;var x={};t=(x.s=String(p),x.o=1,x.g=!1,x.t=p,x.l=!1,x.h=void 0,x)}p>t.t+r*60&&(v=!0,t.s=String(p),t.o++,t.g=!1,t.h=void 0);if(v)V(a,R.A.te,!0),d.nq(a);else if(d.fq()>u||a.eventName===N.m.Yc)t.g=!0;T(a,R.A.pe)?Q(a.D,N.m.Na)?t.l=!0:(t.l&&!G(9)&&(t.h=void 0),t.l=
!1):t.l=!1;var y=t.h;if(T(a,R.A.pe)||Rv(a)){var A=Q(a.D,N.m.Kg),D=A?1:8;A||(A=y,D=4);A||(A=Lr(),D=7);var E=A.toString(),J=D,F=T(a,R.A.sk);if(F===void 0||J<=F)W(a,N.m.Kg,E),V(a,R.A.sk,J)}e?(a.copyToHitData(N.m.Tb,t.s),a.copyToHitData(N.m.Ug,t.o),a.copyToHitData(N.m.Tg,t.g?1:0)):(W(a,N.m.Tb,t.s),W(a,N.m.Ug,t.o),W(a,N.m.Tg,t.g?1:0));V(a,N.m.bi,t.l?1:0);fj()&&V(a,R.A.Lf,t.d||Ub())};var SN=function(a){for(var b={},c=String(RN.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].trim();if(f&&a(f)){var g=e.slice(1).join("=").trim();g&&(g=decodeURIComponent(g));var h=void 0,m=void 0;((h=b)[m=f]||(h[m]=[])).push(g)}}return b};var TN=window,RN=document,UN=function(a){var b=TN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||RN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&TN["ga-disable-"+a]===!0)return!0;try{var c=TN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=SN(function(f){return f==="AMP_TOKEN"}).AMP_TOKEN||[],e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return RN.getElementById("__gaOptOutExtension")?!0:!1};var VN="gclid dclid gclsrc wbraid gbraid gad_source gad_campaignid utm_source utm_medium utm_campaign utm_term utm_content utm_id".split(" ");function WN(){var a=z.location,b,c=a==null?void 0:(b=a.search)==null?void 0:b.replace("?",""),d;if(c){for(var e=[],f=kj(c,!0),g=l(VN),h=g.next();!h.done;h=g.next()){var m=h.value,n=f[m];if(n)for(var p=0;p<n.length;p++){var q=n[p];q!==void 0&&e.push({name:m,value:q})}}d=e}else d=[];return d};
var YN=function(a){return!a||XN.test(a)||Dm.hasOwnProperty(a)},ZN=function(a){var b=N.m.Ic,c;c||(c=function(){});Eu(a,b)!==void 0&&W(a,b,c(Eu(a,b)))},$N=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=lj(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},aO=function(a){Q(a.D,N.m.Db)&&(wn(N.m.ka)||Q(a.D,N.m.Qb)||W(a,N.m.Xl,!0));var b;var c;c=c===void 0?3:c;var d=w.location.href;if(d){var e=sj(d).search.replace("?",""),f=jj(e,"_gl",!1,!0)||"";b=f?gs(f,c)!==void 0:!1}else b=!1;b&&Rv(a)&&
Ov(a,"glv",1);if(a.eventName!==N.m.na)return{};Q(a.D,N.m.Db)&&au(["aw","dc"]);cu(["aw","dc"]);var g=MM(a),h=KM(a);return Object.keys(g).length?g:h},bO={Vp:Ji(31)},cO={},dO=(cO[N.m.Ue]=1,cO[N.m.Ve]=1,cO[N.m.We]=1,cO[N.m.Xe]=1,cO[N.m.Ze]=1,cO[N.m.af]=1,cO),XN=/^(_|ga_|google_|gtag\.|firebase_).*$/,eO=[Tu,Ru,JJ,Vu,sJ,FJ],fO=function(a){this.M=a;this.C=this.pb=this.clientId=void 0;this.Ja=this.U=!1;this.Za=0;this.R=!1;this.ja={oj:!1};this.ma=new rN;this.H=new lM};k=fO.prototype;k.Rq=function(a,b,c){var d=
this,e=oo(this.M);if(e)if(c.eventMetadata[R.A.yd]&&a.charAt(0)==="_")c.onFailure();else{a!==N.m.na&&a!==N.m.nc&&YN(a)&&P(58);gO(c.C);var f=new LF(e,a,c);V(f,R.A.fb,b);var g=[N.m.ka],h=Rv(f);V(f,R.A.gh,h);if(Uu(f,N.m.ke,Q(f.D,N.m.ke))||h)g.push(N.m.aa),g.push(N.m.W);AJ(function(){Kn(function(){d.Sq(f)},g)});G(88)&&a===N.m.na&&Uu(f,"ga4_ads_linked",!1)&&Fl(Hl(hl.Z.Ga),function(){d.Pq(a,c,f)})}else c.onFailure()};k.Pq=function(a,b,c){function d(){for(var h=l(eO),m=h.next();!m.done;m=h.next()){var n=
m.value;n(f);if(f.isAborted)break}T(f,R.A.Aa)||f.isAborted||Qx(f)}var e=oo(this.M),f=new LF(e,a,b);V(f,R.A.ba,O.N.Oa);V(f,R.A.Aa,!0);V(f,R.A.gh,T(c,R.A.gh));var g=[N.m.aa,N.m.W];Kn(function(){d();wn(g)||Jn(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;V(f,R.A.ia,!0);V(f,R.A.Ke,m);V(f,R.A.Le,n);d()},g)},g)};k.Sq=function(a){var b=this;try{Tu(a);if(a.isAborted){lb();return}G(165)||(this.C=a);hO(a);iO(a);jO(a);kO(a);G(138)&&(a.isAborted=!0);Ku(a);var c={};RM(a,c);if(a.isAborted){a.D.onFailure();
lb();return}G(165)&&(this.C=a);var d=c.Dp;c.Qp===0&&mM(H.P.nk);d===0&&mM(H.P.Uj);Vu(a);V(a,R.A.Rf,hl.Z.xc);lO(a);mO(a);this.Yo(a);this.H.xr(a);nO(a);VJ(a,G(60));oO(a);pO(a);this.jn(aO(a));var e=a.eventName===N.m.na;e&&(this.R=!0);qO(a);e&&!a.isAborted&&this.Za++>0&&mM(H.P.bm);sJ(a);rO(a);QN(a,this.clientId,this.pb,this.H,!this.Ja);sO(a);tO(a);uO(a);vO(a);wO(a,this.ja);xO(a);yO(a);zO(a);AO(a);BO(a);CO(a);NM(a);TM(a);ON(a);NN(a);MN(a);LN(a);KN(a);JN(a);HN(a);GN(a);EN(a);CN(a);BN(a);AN(a);zN(a);OM(a);
PM(a);Q(a.D,N.m.Sg)&&!Rv(a)||DJ(a);DO(a);EO(a);Mu(a);Lu(a);Su(a);KJ(a,!1);FO(a);HJ(a);GO(a);IN(a);FN(a);HO(a);!this.R&&T(a,R.A.Ne)&&mM(H.P.rk);nM(a);if(T(a,R.A.Aa)||a.isAborted){a.D.onFailure();lb();return}this.jn(PN(a,this.clientId));this.Ja=!0;this.ur(a);IO(a);DN(function(f){b.Gm(f)},a);this.H.Fj();JO(a);NJ(a);Wu(a);if(a.isAborted){a.D.onFailure();lb();return}this.Gm(a);a.D.onSuccess()}catch(f){a.D.onFailure()}lb()};k.Gm=function(a){this.ma.add(a)};k.jn=function(a){var b=a.clientId,c=a.pb;b&&c&&
(this.clientId=b,this.pb=c)};k.flush=function(){this.ma.flush()};k.ur=function(a){var b=this;if(!this.U){var c=wn(N.m.W),d=wn(N.m.ka);xn([N.m.W,N.m.ka,N.m.aa],function(){var e=wn(N.m.W),f=wn(N.m.ka),g=!1,h={},m={};if(d!==f&&b.C&&b.pb&&b.clientId){var n=b.clientId,p;var q=xM(b.pb);p=q?q.h:void 0;if(f){var r=vM(b.C);if(r){b.clientId=r;var u=EM(b.C);u&&(b.pb=BM(u,b.pb,b.C))}else tM(b.clientId,b.C),qM(b.clientId,!0);DM(b.pb,b.C);g=!0;h[N.m.Pk]=n;G(69)&&p&&(h[N.m.Bo]=p)}else b.pb=void 0,b.clientId=void 0,
w.gaGlobal={}}e&&!c&&(g=!0,m[R.A.bh]=!0,h[N.m.Th]=Lm[N.m.W]);if(g){var t=YA(b.M,N.m.Yd,h);$A(t,a.D.eventId,{eventMetadata:m})}d=f;c=e;b.ja.oj=!0});this.U=!0}};k.Yo=function(a){a.eventName!==N.m.nc&&this.H.Xo(a)};var jO=function(a){var b=z.location.protocol;b!=="http:"&&b!=="https:"&&(P(29),a.isAborted=!0)},kO=function(a){zc&&zc.loadPurpose==="preview"&&(P(30),a.isAborted=!0)},lO=function(a){var b={prefix:String(Q(a.D,N.m.Wa,"")),path:String(Q(a.D,N.m.Rb,"/")),flags:String(Q(a.D,N.m.Bb,"")),domain:String(Q(a.D,
N.m.ub,"auto")),Rc:Number(Q(a.D,N.m.wb,63072E3))};V(a,R.A.Da,b)},nO=function(a){T(a,R.A.Bd)?V(a,R.A.pe,!1):Uu(a,"ccd_add_ec_stitching",!1)&&V(a,R.A.pe,!0)},oO=function(a){if(G(91)&&!G(88)&&Uu(a,"ga4_ads_linked",!1)&&a.eventName===N.m.na){var b=Q(a.D,N.m.kb)!==!1;if(b){var c=Bu(a);c.Rc&&(c.Rc=Math.min(c.Rc,7776E3));Cu({Ae:b,Fe:Om(Q(a.D,N.m.Xa)),Je:!!Q(a.D,N.m.Db),Oc:c})}}},pO=function(a){var b=Cq(a.D);Q(a.D,N.m.Sb)===!0&&(b=!1);V(a,R.A.Vc,b)},qO=function(a){a.eventName===N.m.na&&(Q(a.D,N.m.od,!0)?
(a.D.C[N.m.Ea]&&(a.D.H[N.m.Ea]=a.D.C[N.m.Ea],a.D.C[N.m.Ea]=void 0,W(a,N.m.Ea)),a.eventName=N.m.Yc):a.isAborted=!0)},mO=function(a){function b(c,d){Bm[c]||d===void 0||W(a,c,d)}zb(a.D.H,b);zb(a.D.C,b)},sO=function(a){var b=Mo(a.D),c=function(d,e){dO[d]&&W(a,d,e)};sd(b[N.m.Ye])?zb(b[N.m.Ye],function(d,e){c((N.m.Ye+"_"+d).toLowerCase(),e)}):zb(b,c)},IO=function(a){if(G(132)&&Rv(a)&&!(Ec("; wv")||Ec("FBAN")||Ec("FBAV")||Gc())&&wn(N.m.ka)){V(a,R.A.Zl,!0);Rv(a)&&Ov(a,"sw_exp",1);a:{
if(!G(132)||!Rv(a))break a;var b=ak(dk(a.D),"/_/service_worker");lw(b);}}},tO=function(a){if(!Q(a.D,N.m.Fc)||!Q(a.D,N.m.Gc)){var b=a.copyToHitData,c=N.m.za,d="",e=z.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Rb(n[1])))}d=e.protocol+"//"+e.hostname+
f+g}b.call(a,c,d,$N);var p=a.copyToHitData,q=N.m.Ya,r;a:{var u=tr("_opt_expid",void 0,void 0,N.m.ka)[0];if(u){var t=lj(u);if(t){var v=t.split("$");if(v.length===3){r=v[2];break a}}}var x=Sn.ga4_referrer_override;if(x!==void 0)r=x;else{var y=cp("gtm.gtagReferrer."+a.target.destinationId),A=z.referrer;r=y?""+y:A}}p.call(a,q,r||void 0,$N);a.copyToHitData(N.m.Cb,z.title);a.copyToHitData(N.m.xb,(zc.language||"").toLowerCase());var D=eG();a.copyToHitData(N.m.Ic,D.width+"x"+D.height);G(145)&&a.copyToHitData(N.m.uf,
void 0,$N);G(87)&&Mv()&&a.copyToHitData(N.m.jd,"1")}},uO=function(a){var b=G(266),c=G(267);if(b||c){var d=Eu(a,N.m.za);if(d&&d.indexOf("?")===-1){var e=WN();if(e.length!==0&&(b&&mM(H.P.Il),c)){mM(H.P.Hl);var f=e.map(function(g){return g.name+"="+g.value}).join("&");W(a,N.m.za,d+"?"+f)}}}},wO=function(a,b){b.oj&&(V(a,R.A.ia,!0),b.oj=!1,fj()&&V(a,R.A.Lf,Ub()))},xO=function(a){var b=T(a,R.A.Mf);b=b||0;var c=!!T(a,R.A.ia),d=b===0||c;V(a,R.A.Di,d);d&&V(a,R.A.Mf,60)},yO=function(a){V(a,R.A.wg,!1);V(a,R.A.Td,
!1);if(!Rv(a)&&!T(a,R.A.Bd)&&Q(a.D,N.m.Pb)!==!1&&wK()&&wn([N.m.aa,N.m.ka])){var b=Sv(a);(T(a,R.A.te)||Q(a.D,N.m.Pk))&&V(a,R.A.wg,!!b);b&&T(a,R.A.Di)&&T(a,R.A.Wl)&&V(a,R.A.Td,!0)}},zO=function(a){V(a,R.A.xg,!1);V(a,R.A.yg,!1);if(!ym()&&fj()&&!Rv(a)&&!T(a,R.A.Bd)&&T(a,R.A.Di)){var b=T(a,R.A.Td);T(a,R.A.Lf)&&(b?V(a,R.A.yg,!0):V(a,R.A.xg,!0))}},CO=function(a){a.copyToHitData(N.m.mi);for(var b=Q(a.D,N.m.fi)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(N.m.mi,d.traffic_type);mM(H.P.Bm);
break}}},JO=function(a){a.copyToHitData(N.m.Qk);Q(a.D,N.m.Sg)&&(W(a,N.m.Sg,!0),Rv(a)||ZN(a))},FO=function(a){a.copyToHitData(N.m.Na);a.copyToHitData(N.m.Ub)},vO=function(a){Uu(a,"google_ng")&&!wm()?a.copyToHitData(N.m.je,1):Nu(a)},HO=function(a){var b=Q(a.D,N.m.Gc);b&&mM(H.P.tm);T(a,R.A.Ne)&&mM(H.P.Wj);var c=Jj(Kj());(b||Uj(c)||c&&c.parent&&c.context&&c.context.source===5)&&mM(H.P.am)},hO=function(a){if(UN(a.target.destinationId))P(28),a.isAborted=!0;else if(G(144)){var b=Ij();if(b&&Array.isArray(b.destinations))for(var c=
0;c<b.destinations.length;c++)if(UN(b.destinations[c])){P(125);a.isAborted=!0;break}}},DO=function(a){qw()&&W(a,N.m.dd,"1")},iO=function(a){if(bO.Vp.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=Pv(a);b&&b.blacklisted&&(a.isAborted=!0)}},AO=function(a){var b=function(c){return!!c&&c.conversion};V(a,R.A.Jf,b(Pv(a)));T(a,R.A.Kf)&&V(a,R.A.Vl,b(Pv(a,"first_visit")));T(a,R.A.te)&&V(a,R.A.Yl,b(Pv(a,"session_start")))},BO=function(a){Fm.hasOwnProperty(a.eventName)&&(V(a,
R.A.Ul,!0),a.copyToHitData(N.m.ya),a.copyToHitData(N.m.lb))},GO=function(a){if(!Rv(a)&&T(a,R.A.Jf)&&wn(N.m.aa)&&Uu(a,"ga4_ads_linked",!1)){var b=Bu(a),c=qt(b.prefix),d=ev(c);W(a,N.m.Zd,d.Yf);W(a,N.m.be,d.th);W(a,N.m.ae,d.sh)}},EO=function(a){if(G(122)){var b=ym();b&&V(a,R.A.Qo,b)}},rO=function(a){V(a,R.A.Wl,Sv(a)&&Q(a.D,N.m.Pb)!==!1&&wK()&&!wm())};
function gO(a){zb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[N.m.Ub]||{};zb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var LO=function(a){if(!KO(a)){var b=!1,c=function(){!b&&KO(a)&&(b=!0,Rc(z,"visibilitychange",c),G(5)&&Rc(z,"prerenderingchange",c),P(55))};Qc(z,"visibilitychange",c);G(5)&&Qc(z,"prerenderingchange",c);P(54)}},KO=function(a){if(G(5)&&"prerendering"in z?z.prerendering:z.visibilityState==="prerender")return!1;a();return!0};function MO(a,b){LO(function(){var c=oo(a);if(c){var d=NO(c,b);Cp(a,d,hl.Z.xc)}});}function NO(a,b){var c=function(){};var d=new fO(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[R.A.Bd]=!0);d.Rq(g,h,m)};OO(a,d,b);return c}
function OO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[R.A.Tj]=!0,e),deferrable:!0};d.Vq(function(){iM=!0;Dp.flush();d.uh()>=1E3&&zc.sendBeacon!==void 0&&Ep(N.m.Yd,{},a.id,f);b.flush();d.kn(function(){iM=!1;d.kn()})});}var PO=H.P.pk,QO=H.P.qk;var RO=NO;function SO(a,b){if(G(240)){var c=Ej();c&&c.indexOf(b)>-1&&(a[R.A.Pl]=!0)}}function UO(a,b,c){var d=this;}UO.K="internal.gtagConfig";
function WO(a,b){}
WO.publicName="gtagSet";function XO(){var a={};return a};function YO(a){}YO.K="internal.initializeServiceWorker";function ZO(a,b){}ZO.publicName="injectHiddenIframe";var $O=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function aP(a,b,c,d,e){}aP.K="internal.injectHtml";var eP={};
function gP(a,b,c,d){}var hP={dl:1,id:1},iP={};
function jP(a,b,c,d){}G(160)?jP.publicName="injectScript":gP.publicName="injectScript";jP.K="internal.injectScript";function kP(){var a=!1;a=!!om["5"];return a}kP.K="internal.isAutoPiiEligible";function lP(a){var b=!0;return b}lP.publicName="isConsentGranted";function mP(a){var b=!1;return b}mP.K="internal.isDebugMode";function nP(){return xm()}nP.K="internal.isDmaRegion";function oP(a){var b=!1;return b}oP.K="internal.isEntityInfrastructure";function pP(a){var b=!1;if(!th(a))throw I(this.getName(),["number"],[a]);b=G(a);return b}pP.K="internal.isFeatureEnabled";function qP(){var a=!1;return a}qP.K="internal.isFpfe";function rP(){var a=!1;return a}rP.K="internal.isGcpConversion";function sP(){var a=!1;return a}sP.K="internal.isLandingPage";function tP(){var a=!1;return a}tP.K="internal.isOgt";function uP(){var a;return a}uP.K="internal.isSafariPcmEligibleBrowser";function vP(){var a=Th(function(b){pD(this).log("error",b)});a.publicName="JSON";return a};function wP(a){var b=void 0;return Id(b)}wP.K="internal.legacyParseUrl";function xP(){return!1}
var yP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function zP(){}zP.publicName="logToConsole";function AP(a,b){}AP.K="internal.mergeRemoteConfig";function BP(a,b,c){c=c===void 0?!0:c;var d=[];return Id(d)}BP.K="internal.parseCookieValuesFromString";function CP(a){var b=void 0;if(typeof a!=="string")return;a&&Lb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(x){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Id({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=sj(a)}catch(x){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var u=q[r].split("="),t=u[0],v=lj(u.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(t)?typeof p[t]==="string"?p[t]=[p[t],v]:p[t].push(v):p[t]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Id(n);
return b}CP.publicName="parseUrl";function DP(a){}DP.K="internal.processAsNewEvent";function EP(a,b,c){var d;return d}EP.K="internal.pushToDataLayer";function FP(a){var b=Fa.apply(1,arguments),c=!1;if(!K(a))throw I(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(B(f.value,this.J,1));try{L.apply(null,d),c=!0}catch(g){return!1}return c}FP.publicName="queryPermission";function GP(a){var b=this;}GP.K="internal.queueAdsTransmission";function HP(a){var b=void 0;return b}HP.publicName="readAnalyticsStorage";function IP(){var a="";return a}IP.publicName="readCharacterSet";function JP(){return Fi(19)}JP.K="internal.readDataLayerName";function KP(){var a="";return a}KP.publicName="readTitle";function LP(a,b){var c=this;if(!K(a)||!lh(b))throw I(this.getName(),["string","function"],arguments);GJ(a,function(d){b.invoke(c.J,Id(d,c.J,1))});}LP.K="internal.registerCcdCallback";function MP(a,b){return!0}MP.K="internal.registerDestination";var NP=["config","event","get","set"];function OP(a,b,c){}OP.K="internal.registerGtagCommandListener";function PP(a,b){var c=!1;return c}PP.K="internal.removeDataLayerEventListener";function QP(a,b){}
QP.K="internal.removeFormData";function RP(){}RP.publicName="resetDataLayer";function SP(a,b,c){var d=void 0;return d}SP.K="internal.scrubUrlParams";function TP(a){}TP.K="internal.sendAdsHit";function UP(a,b,c,d){if(arguments.length<2||!jh(d)||!jh(c))throw I(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?B(c):{},f=B(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?B(d):{},m=pD(this);h.originatingEntity=eE(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};td(e,q);var r={};td(h,r);var u=YA(p,b,q);$A(u,h.eventId||m.eventId,r)}}}UP.K="internal.sendGtagEvent";function VP(a,b,c){}VP.publicName="sendPixel";function WP(a,b){}WP.K="internal.setAnchorHref";function XP(a){}XP.K="internal.setContainerConsentDefaults";function YP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}YP.publicName="setCookie";function ZP(a){}ZP.K="internal.setCorePlatformServices";function $P(a,b){}$P.K="internal.setDataLayerValue";function aQ(a){}aQ.publicName="setDefaultConsentState";function bQ(a,b){}bQ.K="internal.setDelegatedConsentType";function cQ(a,b){}cQ.K="internal.setFormAction";function dQ(a,b,c){c=c===void 0?!1:c;}dQ.K="internal.setInCrossContainerData";function eQ(a,b,c){return!1}eQ.publicName="setInWindow";function fQ(a,b,c){}fQ.K="internal.setProductSettingsParameter";function gQ(a,b,c){if(!K(a)||!K(b)||arguments.length!==3)throw I(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Gp(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!sd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=B(c,this.J,1);}gQ.K="internal.setRemoteConfigParameter";function hQ(a,b){}hQ.K="internal.setTransmissionMode";function iQ(a,b,c,d){var e=this;}iQ.publicName="sha256";function jQ(a,b,c){}
jQ.K="internal.sortRemoteConfigParameters";function kQ(a){}kQ.K="internal.storeAdsBraidLabels";function lQ(a,b){var c=void 0;return c}lQ.K="internal.subscribeToCrossContainerData";function mQ(a){}mQ.K="internal.taskSendAdsHits";var nQ={},oQ={};nQ.getItem=function(a){var b=null;L(this,"access_template_storage");var c=pD(this).Jb();oQ[c]&&(b=oQ[c].hasOwnProperty("gtm."+a)?oQ[c]["gtm."+a]:null);return b};nQ.setItem=function(a,b){L(this,"access_template_storage");var c=pD(this).Jb();oQ[c]=oQ[c]||{};oQ[c]["gtm."+a]=b;};
nQ.removeItem=function(a){L(this,"access_template_storage");var b=pD(this).Jb();if(!oQ[b]||!oQ[b].hasOwnProperty("gtm."+a))return;delete oQ[b]["gtm."+a];};nQ.clear=function(){L(this,"access_template_storage"),delete oQ[pD(this).Jb()];};nQ.publicName="templateStorage";function pQ(a,b){var c=!1;return c}pQ.K="internal.testRegex";function qQ(a){var b;return b};function rQ(a,b){if(!K(a)||!th(b))throw I(this.getName(),["string","number"],arguments);kb(a,b);}rQ.K="internal.trackUsage";function sQ(a,b){var c;return c}sQ.K="internal.unsubscribeFromCrossContainerData";function tQ(a){}tQ.publicName="updateConsentState";function uQ(a){var b=!1;return b}uQ.K="internal.userDataNeedsEncryption";var vQ;function wQ(a,b,c){vQ=vQ||new di;vQ.add(a,b,c)}function xQ(a,b){var c=vQ=vQ||new di;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=qb(b)?wh(a,b):xh(a,b)}
function yQ(){return function(a){var b;var c=vQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.ob();if(e){var f=!1,g=e.Jb();if(g){Dh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function zQ(){var a=function(c){return void xQ(c.K,c)},b=function(c){return void wQ(c.publicName,c)};b(jD);b(qD);b(EE);b(GE);b(HE);b(OE);b(QE);b(SF);b(vP());b(UF);b(AL);b(BL);b(YL);b(ZL);b($L);b(fM);b(WO);b(ZO);b(lP);b(zP);b(CP);b(FP);b(IP);b(KP);b(VP);b(YP);b(aQ);b(eQ);b(iQ);b(nQ);b(tQ);wQ("Math",Bh());wQ("Object",bi);wQ("TestHelper",fi());wQ("assertApi",yh);wQ("assertThat",zh);wQ("decodeUri",Eh);wQ("decodeUriComponent",Fh);wQ("encodeUri",Gh);wQ("encodeUriComponent",Hh);wQ("fail",Mh);wQ("generateRandom",
Qh);wQ("getTimestamp",Rh);wQ("getTimestampMillis",Rh);wQ("getType",Sh);wQ("makeInteger",Uh);wQ("makeNumber",Vh);wQ("makeString",Wh);wQ("makeTableMap",Xh);wQ("mock",$h);wQ("mockObject",ai);wQ("fromBase64",tL,!("atob"in w));wQ("localStorage",yP,!xP());wQ("toBase64",qQ,!("btoa"in w));a(iD);a(mD);a(GD);a(SD);a(ZD);a(dE);a(tE);a(CE);a(FE);a(IE);a(JE);a(KE);a(LE);a(ME);a(NE);a(PE);a(RE);a(RF);a(TF);a(VF);a(WF);a(XF);a(YF);a(ZF);a(GH);a(LH);a(TH);a(UH);a(eI);a(jI);a(oI);a(xI);a(CI);a(PI);a(RI);a(eJ);a(fJ);
a(hJ);a(rL);a(sL);a(uL);a(vL);a(wL);a(xL);a(yL);a(DL);a(EL);a(FL);a(GL);a(HL);a(IL);a(JL);a(KL);a(LL);a(ML);a(NL);a(OL);a(PL);a(RL);a(SL);a(TL);a(UL);a(VL);a(WL);a(XL);a(aM);a(bM);a(cM);a(dM);a(eM);a(hM);a(UO);a(YO);a(aP);a(jP);a(kP);a(mP);a(nP);a(oP);a(pP);a(qP);a(rP);a(sP);a(tP);a(uP);a(wP);a(rE);a(AP);a(BP);a(DP);a(EP);a(GP);a(JP);a(LP);a(MP);a(OP);a(PP);a(QP);a(SP);a(TP);a(UP);a(WP);a(XP);a(ZP);a($P);a(bQ);a(cQ);a(dQ);a(fQ);a(gQ);a(hQ);a(jQ);a(kQ);a(lQ);a(mQ);a(pQ);a(rQ);a(sQ);a(uQ);xQ("internal.IframingStateSchema",
XO());xQ("internal.quickHash",Ph);
G(104)&&a(CL);G(160)?b(jP):b(gP);G(177)&&b(HP);return yQ()};var gD;
function AQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;gD=new bf;BQ();Jf=fD();var e=gD,f=zQ(),g=new Bd("require",f);g.Ta();e.C.C.set("require",g);Ya.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&dg(n,d[m]);try{gD.execute(n),G(120)&&jk&&n[0]===50&&h.push(n[1])}catch(r){}}G(120)&&(Wf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");cj[q]=["sandboxedScripts"]}CQ(b)}function BQ(){gD.Tc(function(a,b,c){Sn.SANDBOXED_JS_SEMAPHORE=Sn.SANDBOXED_JS_SEMAPHORE||0;Sn.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Sn.SANDBOXED_JS_SEMAPHORE--}})}function CQ(a){a&&zb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");cj[e]=cj[e]||[];cj[e].push(b)}})};function DQ(a){$A(VA("developer_id."+a,!0),0,{})};var EQ=Array.isArray;function FQ(a,b){return td(a,b||null)}function X(a){return window.encodeURIComponent(a)}function GQ(a,b,c){Pc(a,b,c)}
function HQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=mj(sj(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function IQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function JQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=IQ(b,"parameter","parameterValue");e&&(c=FQ(e,c))}return c}function KQ(a,b,c){return a===void 0||a===c?b:a}function LQ(){try{if(!G(243))return null;var a=[],b;a:{try{b=!!$F('script[data-requiremodule^="mage/"]');break a}catch(g){}b=!1}b&&a.push("ac");var c;a:{try{c=!!$F('script[src^="//assets.squarespace.com/"]');break a}catch(g){}c=!1}c&&a.push("sqs");var d;a:{try{d=!!$F('script[id="d-js-core"]');break a}catch(g){}d=!1}d&&a.push("dud");var e;a:{try{e=!!$F('script[src*="woocommerce"],link[href*="woocommerce"],[class|="woocommerce"]');break a}catch(g){}e=!1}e&&a.push("woo");var f;a:{try{f=!!$F('meta[content*="fourthwall"],script[src*="fourthwall"],link[href*="fourthwall"]');
break a}catch(g){}f=!1}f&&a.push("fw");if(a.length>0)return{plf:a.join(".")}}catch(g){}return null};function MQ(a,b,c){return Lc(a,b,c,void 0)}function NQ(a,b){return cp(a,b||2)}function OQ(a,b){w[a]=b}function PQ(a,b,c){var d=w;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var QQ={};var Y={securityGroups:{}};
Y.securityGroups.access_template_storage=["google"],Y.__access_template_storage=function(){return{assert:function(){},V:function(){return{}}}},Y.__access_template_storage.F="access_template_storage",Y.__access_template_storage.isVendorTemplate=!0,Y.__access_template_storage.priorityOverride=0,Y.__access_template_storage.isInfrastructure=!1,Y.__access_template_storage["5"]=!1;

Y.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Y.__read_event_data=b;Y.__read_event_data.F="read_event_data";Y.__read_event_data.isVendorTemplate=!0;Y.__read_event_data.priorityOverride=0;Y.__read_event_data.isInfrastructure=!1;Y.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!sb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Ng(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},V:a}})}();


Y.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Y.__detect_youtube_activity_events=b;Y.__detect_youtube_activity_events.F="detect_youtube_activity_events";Y.__detect_youtube_activity_events.isVendorTemplate=!0;Y.__detect_youtube_activity_events.priorityOverride=0;Y.__detect_youtube_activity_events.isInfrastructure=!1;Y.__detect_youtube_activity_events["5"]=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},V:a}})}();


Y.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Y.__detect_history_change_events=b;Y.__detect_history_change_events.F="detect_history_change_events";Y.__detect_history_change_events.isVendorTemplate=!0;Y.__detect_history_change_events.priorityOverride=0;Y.__detect_history_change_events.isInfrastructure=!1;Y.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();


Y.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Y.__detect_link_click_events=b;Y.__detect_link_click_events.F="detect_link_click_events";Y.__detect_link_click_events.isVendorTemplate=!0;Y.__detect_link_click_events.priorityOverride=0;Y.__detect_link_click_events.isInfrastructure=!1;Y.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},V:a}})}();
Y.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Y.__detect_form_submit_events=b;Y.__detect_form_submit_events.F="detect_form_submit_events";Y.__detect_form_submit_events.isVendorTemplate=!0;Y.__detect_form_submit_events.priorityOverride=0;Y.__detect_form_submit_events.isInfrastructure=!1;Y.__detect_form_submit_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&
f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},V:a}})}();
Y.securityGroups.read_container_data=["google"],Y.__read_container_data=function(){return{assert:function(){},V:function(){return{}}}},Y.__read_container_data.F="read_container_data",Y.__read_container_data.isVendorTemplate=!0,Y.__read_container_data.priorityOverride=0,Y.__read_container_data.isInfrastructure=!1,Y.__read_container_data["5"]=!1;
Y.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Y.__listen_data_layer=b;Y.__listen_data_layer.F="listen_data_layer";Y.__listen_data_layer.isVendorTemplate=!0;Y.__listen_data_layer.priorityOverride=0;Y.__listen_data_layer.isInfrastructure=!1;Y.__listen_data_layer["5"]=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!sb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},V:a}})}();
Y.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Y.__detect_user_provided_data=b;Y.__detect_user_provided_data.F="detect_user_provided_data";Y.__detect_user_provided_data.isVendorTemplate=!0;Y.__detect_user_provided_data.priorityOverride=0;Y.__detect_user_provided_data.isInfrastructure=!1;Y.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},V:a}})}();



Y.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Y.__get_url=b;Y.__get_url.F="get_url";Y.__get_url.isVendorTemplate=!0;Y.__get_url.priorityOverride=0;Y.__get_url.isInfrastructure=!1;Y.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!sb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!sb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},V:a}})}();





Y.securityGroups.read_dom_elements=["google"],function(){function a(b,c,d){return{type:c,value:d}}(function(b){Y.__read_dom_elements=b;Y.__read_dom_elements.F="read_dom_elements";Y.__read_dom_elements.isVendorTemplate=!0;Y.__read_dom_elements.priorityOverride=0;Y.__read_dom_elements.isInfrastructure=!1;Y.__read_dom_elements["5"]=!1})(function(b){var c=b.vtp_allowedElementIds||"none",d=b.vtp_allowedCssSelectors||"none",e=b.vtp_elementIds||[],f=b.vtp_cssSelectors||[],g=b.vtp_createPermissionError;return{assert:function(h,
m,n){switch(m){case "id":if(c==="none")break;if(c==="any"||e.indexOf(n)>-1)return;break;case "css":if(d==="none")break;if(d==="any"||f.indexOf(n)>-1)return;break;default:throw g(h,{},"Unknown selector type "+m+".");}throw g(h,{},"Prohibited selector value "+n+" for selector type "+m+".");},V:a}})}();
Y.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Y.__gct=b;Y.__gct.F="gct";Y.__gct.isVendorTemplate=!0;Y.__gct.priorityOverride=0;Y.__gct.isInfrastructure=!1;Y.__gct["5"]=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[N.m.vf]=d);c[N.m.Lg]=b.vtp_eventSettings;c[N.m.Dk]=b.vtp_dynamicEventSettings;c[N.m.ke]=b.vtp_googleSignals===1;c[N.m.Rk]=b.vtp_foreignTld;c[N.m.Ok]=b.vtp_restrictDomain===
1;c[N.m.fi]=b.vtp_internalTrafficResults;var e=N.m.Xa,f=b.vtp_linker;f&&f[N.m.oa]&&(f[N.m.oa]=a(f[N.m.oa]));c[e]=f;var g=N.m.hi,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Ip(b.vtp_trackingId,c);MO(b.vtp_trackingId,b.vtp_gtmEventId);Sc(b.vtp_gtmOnSuccess)})}();



Y.securityGroups.get=["google"],Y.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=YA(String(b.streamId),d,c);$A(f,e.eventId,e);a.vtp_gtmOnSuccess()},Y.__get.F="get",Y.__get.isVendorTemplate=!0,Y.__get.priorityOverride=0,Y.__get.isInfrastructure=!1,Y.__get["5"]=!1;
Y.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Y.__detect_scroll_events=b;Y.__detect_scroll_events.F="detect_scroll_events";Y.__detect_scroll_events.isVendorTemplate=!0;Y.__detect_scroll_events.priorityOverride=0;Y.__detect_scroll_events.isInfrastructure=!1;Y.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();



Y.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){Y.__detect_form_interaction_events=b;Y.__detect_form_interaction_events.F="detect_form_interaction_events";Y.__detect_form_interaction_events.isVendorTemplate=!0;Y.__detect_form_interaction_events.priorityOverride=0;Y.__detect_form_interaction_events.isInfrastructure=!1;Y.__detect_form_interaction_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();
var Vn={dataLayer:dp,callback:function(a){bj.hasOwnProperty(a)&&qb(bj[a])&&bj[a]();delete bj[a]},bootstrap:0};
function RQ(){Un();Nj();Bz();Jb(cj,Y.securityGroups);var a=Jj(Kj()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;fn(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||P(142);Vf={Jp:jg}}var SQ=!1;G(218)&&(SQ=Di(47,SQ));
function rm(){try{if(SQ||!Vj()){Qi();G(218)&&(Hi.C=Di(50,Hi.C));
Hi.Za=Ji(4);Hi.Ja=Ji(5);Hi.ma=Ji(11);Hi.ma=Ji(10);G(218)&&(Hi.R=Di(51,Hi.R));if(G(109)){}Wa[7]=!0;var a=Tn("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*
Math.random()))});nn(a);Rn();WC();pq();Zn();if(Oj()){Fi(5);oE();rA().removeExternalRestrictions(Fj());}else{CJ();ko();Tf();Pf=Y;Qf=EC;Tv();AQ();RQ();CC();pm||(om=tm(),om["0"]&&Nl(Il.X.se,JSON.stringify(om)));Nn();OB();Ji(6);Ji(7);Ji(35);TA();vB=!1;z.readyState==="complete"?xB():Qc(w,"load",xB);NA();jk&&(lp(yp),w.setInterval(xp,864E5),lp(XC),lp(eA),lp(Xx),lp(Bp),lp(cD),lp(pA),G(120)&&(lp(jA),lp(kA),lp(lA)),ZC={},lp($C),Li(),G(261)&&lp(YC));lk&&(dm(),Co(),QB(),ZB(),XB(),Wl("bt",String(Hi.H?2:Hi.C?1:0)),Wl("ct",String(Hi.H?0:Hi.C?1:3)),TB(),WB());tC();nm(1);pE();aj=Gb();Vn.bootstrap=aj;Hi.R&&NB();G(109)&&
sy();G(134)&&(typeof w.name==="string"&&Lb(w.name,"web-pixel-sandbox-CUSTOM")&&id()?DQ("dMDg0Yz"):w.Shopify&&(DQ("dN2ZkMj"),id()&&DQ("dNTU0Yz")))}}}catch(b){nm(4),up()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Tm(n)&&(m=h.zl)}function c(){m&&Cc?g(m):a()}if(!w[Fi(37)]){var d=!1;if(z.referrer){var e=sj(z.referrer);d=oj(e,"host")===Fi(38)}if(!d){var f=tr(Fi(39));d=!(!f.length||!f[0].length)}d&&(w[Fi(37)]=!0,Lc(Fi(40)))}var g=function(t){var v="GTM",x="GTM";Xi&&(v="OGT",x="GTAG");var y=Fi(23),A=w[y];A||(A=[],w[y]=A,Lc("https://"+Fi(3)+"/debug/bootstrap?id="+Fi(5)+"&src="+x+"&cond="+String(t)+"&gtm="+Rq()));var D={messageType:"CONTAINER_STARTING",
data:{scriptSource:Cc,containerProduct:v,debug:!1,id:Fi(5),targetRef:{ctid:Fi(5),isDestination:Dj(),canonicalId:Fi(6)},aliases:Gj(),destinations:Ej()}};D.data.resume=function(){a()};Ei(2)&&(D.data.initialPublish=!0);A.push(D)},h={Uo:1,Ol:2,im:3,jk:4,zl:5};h[h.Uo]="GTM_DEBUG_LEGACY_PARAM";h[h.Ol]="GTM_DEBUG_PARAM";h[h.im]="REFERRER";h[h.jk]="COOKIE";h[h.zl]="EXTENSION_PARAM";var m=void 0,n=void 0,p=mj(w.location,"query",!1,void 0,"gtm_debug");Tm(p)&&(m=h.Ol);if(!m&&z.referrer){var q=sj(z.referrer);
oj(q,"host")===Fi(24)&&(m=h.im)}if(!m){var r=tr("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.jk)}m||b();if(!m&&Sm(n)){var u=!1;Qc(z,"TADebugSignal",function(){u||(u=!0,b(),c())},!1);w.setTimeout(function(){u||(u=!0,b(),c())},200)}else c()})(function(){!SQ||tm()["0"]?rm():qm()});

})()

