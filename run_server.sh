#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo ""
echo "========================================"
echo "    🚀 سيرفر FoulaBook Scraper"
echo "========================================"
echo ""

echo "📦 جاري التحقق من المكتبات المطلوبة..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ فشل في تثبيت المكتبات المطلوبة"
    echo "يرجى التأكد من تثبيت Python بشكل صحيح"
    exit 1
fi

echo ""
echo "✅ تم تثبيت جميع المكتبات بنجاح"
echo ""

echo "🌐 بدء تشغيل السيرفر..."
echo "📍 الرابط: http://localhost:5000"
echo "🔄 اضغط Ctrl+C للإيقاف"
echo ""

python3 server.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ حدث خطأ في تشغيل السيرفر"
    echo "يرجى التحقق من ملف server.py"
    exit 1
fi
