#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سيرفر FoulaBook Scraper
"""

import requests
import json
import time
import sys

def test_server_connection(base_url="http://localhost:5000"):
    """اختبار الاتصال بالسيرفر"""
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ السيرفر يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ السيرفر يرد بكود خطأ: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالسيرفر")
        print("💡 تأكد من تشغيل السيرفر أولاً")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_api_endpoints(base_url="http://localhost:5000"):
    """اختبار نقاط API المختلفة"""
    endpoints = [
        ("/api/articles", "GET"),
        ("/api/stats", "GET"),
    ]
    
    print("\n🔍 اختبار نقاط API...")
    
    for endpoint, method in endpoints:
        try:
            url = base_url + endpoint
            if method == "GET":
                response = requests.get(url, timeout=5)
            else:
                response = requests.post(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - يعمل")
            else:
                print(f"❌ {endpoint} - خطأ {response.status_code}")
                
        except Exception as e:
            print(f"❌ {endpoint} - خطأ: {e}")

def test_scraping_functionality(base_url="http://localhost:5000"):
    """اختبار وظيفة السحب"""
    print("\n📚 اختبار وظيفة السحب...")
    
    test_url = "https://foulabook.com/ar/book/test-book"
    
    try:
        response = requests.post(
            f"{base_url}/api/scrape",
            json={"url": test_url},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'book_data' in data:
                print("✅ وظيفة السحب تعمل")
                print(f"📖 عنوان الكتاب: {data['book_data'].get('title', 'غير محدد')}")
            else:
                print("⚠️ السحب يعمل لكن البيانات غير مكتملة")
        else:
            print(f"❌ فشل في السحب - كود الخطأ: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار السحب: {e}")

def test_file_operations(base_url="http://localhost:5000"):
    """اختبار عمليات الملفات"""
    print("\n📁 اختبار عمليات الملفات...")
    
    try:
        # اختبار الحصول على قائمة المقالات
        response = requests.get(f"{base_url}/api/articles", timeout=5)
        if response.status_code == 200:
            articles = response.json()
            print(f"✅ تم العثور على {len(articles.get('scraped', []))} مقالة مسحوبة")
            print(f"✅ تم العثور على {len(articles.get('published', []))} مقالة منشورة")
        else:
            print("❌ فشل في الحصول على قائمة المقالات")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الملفات: {e}")

def test_statistics(base_url="http://localhost:5000"):
    """اختبار الإحصائيات"""
    print("\n📊 اختبار الإحصائيات...")
    
    try:
        response = requests.get(f"{base_url}/api/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ إجمالي المقالات: {stats.get('total_count', 0)}")
            print(f"✅ حجم البيانات: {stats.get('total_size', 0)} بايت")
        else:
            print("❌ فشل في الحصول على الإحصائيات")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإحصائيات: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار سيرفر FoulaBook Scraper")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # اختبار الاتصال الأساسي
    if not test_server_connection(base_url):
        print("\n❌ فشل في الاتصال بالسيرفر")
        print("💡 تأكد من تشغيل السيرفر باستخدام:")
        print("   - run_server.bat (Windows)")
        print("   - ./run_server.sh (Linux/Mac)")
        print("   - python server.py")
        sys.exit(1)
    
    # اختبار باقي الوظائف
    test_api_endpoints(base_url)
    test_file_operations(base_url)
    test_statistics(base_url)
    
    # اختبار السحب (اختياري)
    print("\n❓ هل تريد اختبار وظيفة السحب؟ (قد يستغرق وقتاً)")
    choice = input("اكتب 'y' للموافقة أو أي شيء آخر للتخطي: ")
    if choice.lower() == 'y':
        test_scraping_functionality(base_url)
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
    print("💡 إذا كانت جميع الاختبارات ناجحة، فالسيرفر يعمل بشكل صحيح")

if __name__ == "__main__":
    main()
