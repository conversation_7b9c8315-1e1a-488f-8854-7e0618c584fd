.fixed-header-on header.header{
    background-color: #FFF !important; 
}

.transparent-header .header-top {
    z-index: 103;
    position: relative
}

.transparent-header header.header.transparent-header-on {
    z-index: 1006;
    position: absolute;
    width: 100%; 
    background-color:rgba(255, 255, 255, 0.64);
    border-bottom: 1px solid rgba(0,0,0,.07);
    border-top-color: #a0a0a0
}


/*Floating Header ************************************************************/
.floating-header header.header.floating-header-on {
    z-index: 1006;
    position: absolute;
    width: 100%; 
    top:50px; 
}
.floating-header header{
    background: transparent;
    box-shadow:none;
    /*-moz-transition: all 0.4s;
    -o-transition: all 0.4s;
    -webkit-transition: all 0.4s;
    transition: all 0.4s;*/
}
.floating-header header > .container{
      background: #FFF;
      border-radius:2px;
}
.floating-header header.header.floating-header-on nav ul.nav-main > li > a{
    text-transform: uppercase;
    font-weight: 600;
}

/*End Floating Header ************************************************************/




.transparent-header.full-transparent-header header.header.transparent-header-on {
    background-color: rgba(255, 255, 255, 0);
    border-bottom: 1px solid rgba(0,0,0,.07);
    border-top-color: #a0a0a0
}

.transparent-header.full-transparent-header header.header.transparent-header-on div.search a,
.transparent-header.full-transparent-header header.header.transparent-header-on nav ul.nav-main i.fa-caret-down,
.transparent-header.full-transparent-header header.header.transparent-header-on nav ul.nav-main > li > a{
    color:rgba(255,255,255,1);
}

.transparent-header.full-transparent-header.fixed-header-on header.header div.search a,
.transparent-header.full-transparent-header.fixed-header-on header.header nav ul.nav-main i.fa-caret-down,
.transparent-header.full-transparent-header.fixed-header-on header.header nav ul.nav-main > li > a{
    color:#404040;
}

.transparent-header .header-top.colored+header.header.transparent-header-on {
    border-top-color: transparent
}

.transparent-header header.header.dark.transparent-header-on {
    background-color: rgba(57,66,69,.8);
    border-top-color: rgba(37,42,44,.5);
}


.fixed-header-on .header.fixed,
.fixed-header-on header.header.transparent-header-on,
.fixed-header-on header.header.floating-header-on {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1006;
}

.fixed-header-on .header.fixed .nav > li > a {
        padding-top: 21px!important;
    padding-bottom: 21px!important;
}

.fixed-header-on .header.fixed div.search a{
        padding: 20px 5px;
}

.fixed-header-on header div.logo { 
    height: 60px;
}
.fixed-header-on header div.logo img{
    height:35px;
}

.fixed-header-on .header.fixed.animated {
    -webkit-animation-duration: .4s;
    animation-duration: .4s;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}


/* Header */
header {
    clear: both;
    background: #FFF; 
    top: 0;
    width: 100%;
        /*border-bottom: 1px solid #e1e1e1;*/
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.10);
    position:relative;
    z-index:1001;
}
.transparent-header header{
    box-shadow:none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.19)!important;
}

.v-header-shadow {
    background: url(../img/base/shadow-top.png) no-repeat;
    position: absolute;
    -moz-background-size: 100% 100%;
    -o-background-size: 100% 100%;
    background-size: 100% 100%;
    right: 0;
    left: 0;
    height: 30px;
    width: 80%;
    /*bottom: -40px;*/
    margin-right: auto;
    opacity: 0.6;
    margin-left: auto;
}

.shadow-right {
    position: absolute;
    pointer-events: none;
    background-image: url(../img/base/shadow-bottom.png);
    background-repeat: no-repeat;
    background-position: bottom center;
    height: 32px;
    width: 100%;
    bottom: 0;
    z-index: 99; 
    border-bottom: 1px solid #d2d3d4;
}

body.boxed-layout .header-container {
    margin: 0 auto;  
    box-shadow: 0 0 7px rgba(0,0,0,0.15);
}

nav.nav-main.one-page-menu .fa {
    margin-left: 6px;
    font-size: 14px;
}

header nav ul.nav-main ul.dropdown-menu i.fa {
    width: 20px;
    font-size: 15px;
    opacity: .9;
    margin-left: 4px;
}

.menu-logo-wrap {
    position: absolute;
    left: 25px;
    bottom: 25px;
    opacity: 0.1;
}

nav.std-menu .menu {
    height: auto;
    margin: 0;
    position: relative;
    padding: 0;
}

nav.std-menu .menu li {
    position: relative;
    display: inline-block;
    float: right;
    font-size: 16px;
    margin: 8px 0;
}

nav.std-menu .menu li:first-child {
    background: none;
}

nav.std-menu .menu li > a {
    text-decoration: none;
    padding: 2px 0;
    margin: 0 10px;
    display: block;
    white-space: nowrap;
    background: transparent;
}

.v-menu-item-info {
    background: #c10841;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    color: #fff; 
    margin: 2px 7px 0 0;
    padding: 2px 5px;
    display: inline-block;
    font-size: 11px;
    line-height: 13px;
}

.bg-warning {
    background: #f89406 !important;
}

.bg-success {
    background: #738d00 !important;
}

.bg-info {
    background: #9c9c9c !important;
}
 


.promo-block {
    background-color: rgb(250, 250, 250);
    border-right: 1px solid rgb(236, 236, 236) !important;
    padding: 0 0 !important;
}

.promo-block .promo-block-inner {
    padding: 33px 0px;
}

.promo-block .promo-block-inner img {
    margin-right: auto;
    margin-left: auto;
    display: -webkit-box;
}

body.no-page-top {
    background: #EDEFF2;
}
 

body .shopping-cart-widget,
body .search,
body nav ul.nav-main {
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
}

body .form-control.search {
    margin-top: 1px !important;
    height: 40px !important;
    border: 1px solid #EBEBEB;
}

header > .container {
    position: relative;
}

/* Header Top */
.header-container .header-top {
    background: #ffffff;
    border-bottom: 1px solid #EBEBEB;
    width: 100%;
    z-index: 1002;
    margin: 0;
    min-height: 40px;
    -moz-transition: all 0.2s;
    -o-transition: all 0.2s;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
    position: initial;
}

.header-container .header-top p {
    float: right;
    font-size: 0.9em;
    line-height: 35px;
    margin: 0;
    padding-top: 3px;
}

.header-container .header-top ul.social-icons {
    float: left;
    list-style: none;
    margin: 0 25px 0 0;
    padding: 0;
    position: relative;
    top: 0px;
    left: 10px;
}

.header-container .header-top ul.social-icons li a {
    width: 33px;
}

.header-container .header-top ul.social-icons li:not(.sf-love) a {
    font-size: 14px;
}

.header-container .header-top ul.social-icons li:first-child {
    border-right: 1px solid rgb(233, 233, 233);
}

.header-container .header-top ul.social-icons li:last-child {
    border-left: 1px solid rgb(233, 233, 233);
}

.header-container .header-top i.fa {
    position: relative;
    top: 1px;
}

.header-container .header-top nav i.fa {
    top: 0;
}

.header-container .header-top nav {
    float: right;
}

.header-container .header-top nav ul.nav-top li a,
.header-container .header-top nav ul.nav-top li span {
    color: #999;
    font-size: 0.9em;
    line-height: 50px;
    padding: 0 6px;
}

.header-container .header-top nav ul.nav-top li a:before {
    display: none;
}

.header-container .header-top.color nav ul.nav-top li a,
.header-container .header-top.color nav ul.nav-top li span,
.header-container .header-top.color ul.social-icons li a {
    color: #FFF;
}

.dropdown-menu {
    min-width: 200px;
}

a.current-menu-item {
    background-color: rgb(243, 243, 243) !important;
}

a.current {
    background-color: rgb(243, 243, 243) !important;
}

/* Responsive Nav Button */
button.btn-responsive-nav {
    display: none;
}

/* Logo */
header div.logo {
    display: table;
    position: absolute;
    height: 90px;
}

header div.logo a {
    display: table-cell;
    vertical-align: middle;
}

header div.logo img {
    margin-right: auto;
    margin-left: auto;
}
.fixed-header-on header.transparent-header-on div.logo img{
    content: url('../img/logo.png');
}

/* Search */
header div.search {
    float: left;
    position: relative;
    display: block;
    /*height: 90px;*/
    z-index: 9999999;
}

header div.search .btn-primary:hover,
header div.search .btn-primary { 
    border-radius: 2px;
}

header div.search .btn-primary .fa {
    line-height: 26px;
    padding: 0;
}

header div.search a {
    width: 25px;
    text-align: center;
    line-height: 20px;
    color: #2D343E;
    position: relative;
    z-index: 9999;
    display: block;
    font-size: 14px;
    padding: 35px 5px;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
}

header div.search-input {
    position: absolute;
    left: -10px; 
    background: #fff;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    padding: 15px;
    width: 300px;
    z-index: 9998;
    filter: alpha(opacity=0);
    opacity: 0;
    display: none;
    -moz-transition: all 0.2s;
    -o-transition: all 0.2s;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
    -moz-box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.2);
    border: 1px solid #e1e1e1;
}

header div.search input[type=text] {
    box-shadow: none;
}

.v-arrow-wrap {
    width: 20px;
    height: 20px;
    position: absolute;
    top: -20px;
    left: 10px;
    margin-right: -10px;
    overflow: hidden;
}

.v-arrow-inner {
    top: 15px;
    background-color: #ffffff;
    color: #808080;
    height: 10px;
    width: 10px;
    position: absolute;
    right: 50%;
    margin-right: -5px;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
    border: 1px solid #e1e1e1;
}

header div.search-input.active {
    filter: alpha(opacity=100);
    opacity: 100;
}

header div.search-input input {
    border: 0;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
}

header div.search-input .input-group-btn:last-child > .btn {
    margin-top: 1px;
    margin-bottom: 0px;
    margin-right: 0px;
    margin-left:0px;
    padding: 6px 12px;
    border-radius:0px;
    box-shadow:none;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
header div.search-input .input-group-btn:last-child > .btn:hover {
    margin-top: 1px;
    margin-bottom: 0px;
    box-shadow:none;
}
header div.search-input .input-group-btn:last-child > .btn:active {
    top: 0px!important;
}
header div.search-input .input-group-btn:last-child > .btn i {
     margin-left: 0px !important;
     font-size: 14px; 
}

html.webkit header div.search-input .btn {
    margin-top: 0;
    margin-bottom: 0px;
}

/* Navigation */
header nav.nav-main {
    float: left;
}

header .nav > li > a {
    padding: 35px 15px;
}

header .nav-pills > li {
    margin-left: 3px;
}

header div.nav-main-collapse,
header div.nav-main-collapse.in {
    overflow-y: visible;
}

header div.nav-main-collapse.in {
    overflow-y: visible;
    float: none;
    margin: 0;
}

header nav ul.nav-main i.fa-caret-down {
    background: transparent;
    border-radius: 100%;
    color: #888;
    display: inline-block;
    font-size: 16px;
    height: 14px;
    line-height: 15px;
    position: relative;
    text-align: center;
    top: -1px;
    width: 10px;
}

header nav ul.nav-main > li + li {
    margin-right: -8px;
}

header nav ul.nav-main .dropdown-submenu {
    position: relative;
}

header nav ul.nav-main .dropdown-submenu > .dropdown-menu {
    top: 0;
    right: 100%;
    margin-top: -6px;
    margin-right: -1px;
    -webkit-border-radius: 6px 0 6px 6px;
    -moz-border-radius: 6px 0 6px 6px;
    border-radius: 6px 0 6px 6px;
}

header nav ul.nav-main .dropdown-submenu:hover > .dropdown-menu {
    display: block;
    transition: all .2s ease-in-out;
}

header nav ul.nav-main .dropdown-submenu > a:after {

    float: left;
    content: "\f105";
    font-family: fontAwesome;
    font-size:14px;
    margin-left: -6px;

    /*Option 2*/
    /*display: block;
    content: " ";
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 4px 0 4px 4px;
    border-left-color: #838383;
    margin-top: 7px;
    margin-right: -8px;*/
}

header nav ul.nav-main .dropdown-submenu.pull-left {
    float: none;
}

header nav ul.nav-main .dropdown-submenu.pull-left > .dropdown-menu {
    right: -100%;
    margin-right: 10px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px 6px;
    border-radius: 0 6px 6px 6px;
}

header nav ul.nav-main li.dropdown.open a.dropdown-toggle {
    -moz-border-radius: 5px 5px 0px 0px;
    -webkit-border-radius: 5px 5px 0px 0px;
    border-radius: 5px 5px 0px 0px;
}

header nav ul.nav-main ul.dropdown-menu,
header nav ul.nav-main li.dropdown.open a.dropdown-toggle,
header nav ul.nav-main li.active a {
    background-color: transparent;
}

header nav ul.nav-main ul.dropdown-menu {
    -moz-border-radius: 2px;
    -moz-box-shadow: 0 0 0;
    -webkit-border-radius: 2px;
    -webkit-box-shadow: 0 0 0;
    border: 0;
    border-radius: 2px;
    margin: 0 3px 0 0;
    padding: 0px 0px;
    top: auto;
    background: #FFFFFF;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
    border-left-color: rgb(235, 235, 235);

    border-top-width:2px;
    border-top-style:solid;  
}

header nav ul.nav-main ul.dropdown-menu ul.dropdown-menu {
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    margin-right: 0px !important;
    margin-top: -10px;
}

header nav ul.nav-main ul.dropdown-menu li:hover > a {
    filter: none;
    background: #F5F5F5;
        transition: all .2s ease-in-out;
}

header nav ul.nav-main ul.dropdown-menu > li > a {
    color: #444;
    font-weight: 400;
    text-transform: none;
    position: relative;
    padding: 6px 19px 6px 18px;
    font-size: 12px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid #efefef; 
    transition: all .2s ease-in-out;
}

header nav ul.nav-main ul.dropdown-menu li:last-child > a {
    border-bottom: 0;
}

header nav ul.nav-main li a {
    font-style: normal;
    line-height: 20px;
    position: relative;
    background: none;
    text-transform: none;
    display: block;
    color: #444;
    /*font-size: 13px;
    font-weight: 500; */
    letter-spacing: 0px;
    text-shadow: 0 0px 0px rgba(0, 0, 0, 0.0);
    font-size: 13px;
    font-weight: 500; 
    letter-spacing: 0px;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
        font-family: Raleway !important;
}
 

header nav ul.nav-main i.fa-caret-down.fa-caret-down:before {
    content: "\f107";
    font-size: 12px;
    vertical-align: -1px;
    margin-right: 3px;
}

header nav ul.nav-pills > li > a:before {
    display: none;
}

header ul.nav-pills > li > a:hover {
    color: #fff; 
    background: none !important;
}

header nav ul.nav-pills > li > a,
header nav ul.nav-pills > li > a:hover,
header nav ul.nav-pills > li > a:focus,
header nav ul.nav-pills > li.active > a,
header nav ul.nav-pills > li.active > a:hover,
header nav ul.nav-pills > li.active > a:focus {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    border: 0;
    background-color:transparent;
}

.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
    background-color:transparent;
}

header nav ul.nav-main li ul a {
    text-transform: none;
    font-weight: normal;
    font-size: 0.9em;
}
 

header nav ul.nav-main li.dropdown:hover > a i.fa-caret-down {
    -moz-transition: all 0.2s;
    -o-transition: all 0.2s;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
}

header nav ul.nav-main li.dropdown:hover > a:after {
    /*display: block;
                content: " ";
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-bottom: 10px solid #1e73be;
                position: absolute;
                bottom: -3px;
                left: 40%;*/
    display: none;
}

header nav ul.nav-main li.dropdown:hover > ul {
    display: block;
}

.header-container nav ul.nav-main li.dropdown:hover > ul {
    display: block;
}

/*header Top Menu*/
.header-top-menu {
    float: left !important;
    line-height: 1.428571429;
    margin-left: -10px;
}

nav.std-menu.header-top-menu .menu li {
    margin: 0px 0;
    line-height: 35px !important;
}

.header-container .header-top nav ul.nav-main li a {
    line-height: 23px !important;
    font-size: 11px;
}

.header-container .header-top ul.social-icons li {
    height: 39px;
}

.header-container .header-top ul.social-icons li:not(.sf-love) a {
    height: 39px;
    padding-top: 13px;
}

nav.std-menu.header-top-menu .menu li.m-item {
    border-right: 1px solid #EBEBEB;
}
nav.std-menu.header-top-menu ul.nav-main ul.dropdown-menu li > a {
    margin: 0;
}

.header-container nav.header-top-menu ul.nav-main ul.dropdown-menu {
    margin: 0 0px 0 0;
    padding: 10px 0;
    padding-top:7px;
}
.header-container nav.header-top-menu ul.nav-main ul.dropdown-menu li {
    float:none;
    display:block;
}
.header-container nav.header-top-menu ul.nav-main ul.dropdown-menu li a {
    padding: 4px 15px 4px 10px;
}

.header-container nav.header-top-menu ul.nav-main ul.dropdown-menu {
    min-width: 120px;
        z-index: 1002;
}

nav.std-menu.header-top-menu .menu li > a {
    padding: 8px 0px;
    color: #777;
}

.header-container nav.header-top-menu ul.nav-main li.dropdown:hover > a {
    padding-bottom: 8px;
}

.header-top-menu a {
    font-size: 12px;
}

.header-top-info ul {
    margin: 0px;
    float: right;
}

.header-top-info li {
    list-style: none;
    display: inline-block;
    line-height: 39px;
    border-right: 1px solid #EBEBEB;
    padding: 0px 10px;
    font-size: 12px;
    color: #777;
}

.header-top-info li:last-child {
    border-left: 1px solid #EBEBEB;
}

.header-top-info li i.fa {
    margin-left: 5px;
    vertical-align: 1px;
}

/* Mega Menu */
@media (min-width: 992px) {
    header nav ul.nav-main li.dropdown > ul.dropdown-menu {
        top: -9999px;
        right: -9999px;
        display: none;
        /*display: block;*/
        -webkit-transition: opacity ease .3s;
        -moz-transition: opacity ease .3s;
        -o-transition: opacity ease .3s;
        transition: opacity ease .3s;
        opacity: 0;
    }
    
    header nav ul.nav-main li.dropdown:hover > ul.dropdown-menu {
        top: auto;
        right: auto;
        opacity: 1;
    }
    
    .mega-menu-content,
    .mega-menu-content [class*="col"] {
        border-right: 1px dotted #e7e7e7;
    }
    
    .mega-menu-content,
    .mega-menu-content [class*="col"]:first-child {
        border-right: 0px dotted #e7e7e7;
    }
    
    nav.mega-menu .nav,
    nav.mega-menu .dropup,
    nav.mega-menu .dropdown,
    nav.mega-menu .collapse {
        position: static;
    }
    
    header nav.mega-menu ul.nav-main .mega-menu-content {
        text-shadow: none;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item ul.dropdown-menu {
        background: #FFFFFF !important; 
        color: #3F3F3F;
        padding: 0px 15px;
        padding-right: 15px;
        margin: 0 15px;
        -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
        box-shadow: 0 6px 8px rgba(0,0,0,.175);
        border-top-width:2px;
        border-top-style:solid; 
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-fullwidth ul.dropdown-menu {
        -moz-border-radius: 3px;
        -webkit-border-radius: 3px;
        border-radius: 3px;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item .mega-menu-sub-title {
        color: #444;
        display: block;
        font-size: 13px;
        font-weight: 400 !important;
        padding-bottom: 5px;
        text-transform: uppercase;
        font-family: source_sans_probold, Arial, Helvetica, Tahoma, sans-serif;
        letter-spacing: 1.5px;
        cursor: default;
        padding-right: 25px;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item ul.sub-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item .no-smx ul.sub-menu a .fa {
        color: #333;
        width: 20px;
        font-size: 15px;
        opacity: .9;
        margin-left: 4px;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item ul.sub-menu a {
        color: #444;
        text-shadow: none;
        margin: 0;
        padding: 3px 12px;
        padding-left:16px;
        text-transform: none;
        font-size: 12px;
        display: block;
        font-weight: normal;
        -moz-border-radius: 2px;
        -webkit-border-radius: 2px;
        border-radius: 2px;
        -moz-transition: all 0.2s;
        -o-transition: all 0.2s;
        -webkit-transition: all 0.2s;
        transition: all 0.2s;
        padding-right: 30px; 
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item ul.sub-menu a:before {
        display: inline-block;
        font-family: FontAwesome;
        font-style: normal;
        font-weight: normal;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        content: "\f0da";
        padding: 4px 0 4px 8px;
        color: #464646;
        font-size: 11px;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item .no-smx ul.sub-menu a:before {
        display: none;
    }
    
    header nav.mega-menu ul.nav-main li.mega-menu-item:hover ul.sub-menu li:hover a:hover {
        background: #F5F5F5 !important;
        text-decoration: none;
    }
    
    nav.mega-menu .navbar-inner,
    nav.mega-menu .container {
        position: relative;
    }
    
    nav.mega-menu .dropdown-menu {
        right: auto;
    }
    
    nav.mega-menu .dropdown-menu > li {
        display: block;
        /*border-bottom: 1px solid rgb(247, 247, 247);*/
    }
    
    nav.mega-menu .dropdown-menu > li:last-child {
        border-bottom: 0 solid rgb(247, 247, 247);
    }
    
    nav.mega-menu .nav.pull-right .dropdown-menu {
        left: 0;
    }
    
    nav.mega-menu .mega-menu-content [class*="col"] {
        padding: 30px 0;
    }
    
    nav.mega-menu .mega-menu-content:before,
    nav.mega-menu .mega-menu-content:after {
        display: table;
        content: "";
        line-height: 0;
    }
    
    nav.mega-menu .mega-menu-content:after {
        clear: both;
    }
    
    nav.mega-menu.navbar .nav > li > .dropdown-menu:after,
    nav.mega-menu.navbar .nav > li > .dropdown-menu:before {
        display: none;
    }
    
    nav.mega-menu .dropdown.mega-menu-fullwidth .dropdown-menu {
        width: 95%;
        right: 2%;
        left: 2% !important;
    }
    
    nav.mega-menu ul.nav-main li ul.dropdown-menu ul.dropdown-menu {
        padding-top: 0px;
        padding-bottom: 0px;
        padding-right: 0;
        padding-left: 0;
        margin-top: -1px;
        border-radius: 2px;
    }
    
    nav.mega-menu ul.sub-menu li.dropdown-submenu ul.dropdown-menu {
        right: 100%;
    }
    
    nav.mega-menu ul.sub-menu li.dropdown-submenu ul.dropdown-menu li a {
        padding: 6px 8px;
        padding-right: 19px;
    }
    
    nav.mega-menu .dropdown.mega-menu-fullwidth .dropdown-menu.two-columns {
        width: 50%;
        right: 26%;
    }
}


@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}
