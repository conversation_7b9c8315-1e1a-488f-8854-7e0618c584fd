# 🚀 سيرفر FoulaBook Scraper المحلي

## 🎯 نظرة عامة

سيرفر محلي متقدم لسحب الكتب من موقع foulabook.com وتحويلها إلى مقالات متوافقة مع قالب اقرأ كتاب، مع واجهة ويب تفاعلية وشريط تقدم في الوقت الفعلي.

## ✨ المميزات الرئيسية

### 🔄 سحب البيانات في الوقت الفعلي
- شريط تقدم مباشر أثناء عملية السحب
- تحديثات فورية عبر WebSocket
- معالجة أخطاء متقدمة

### 📊 إدارة المقالات
- قسم للمقالات المسحوبة
- قسم للمقالات المنشورة
- عرض تفاصيل كل مقالة
- إمكانية النشر والحذف

### 🎨 واجهة مستخدم متقدمة
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- إشعارات فورية
- نوافذ منبثقة للمعاينة

### 🔒 حماية حقوق الطبع والنشر
- خيار تفعيل الحماية لكل كتاب
- رسائل تنبيه مخصصة
- بدائل للحصول على الكتب المحمية

## 🚀 التشغيل السريع

### Windows
```bash
# تشغيل سريع (مستحسن)
start_server.bat

# تشغيل كامل مع فحص المتطلبات
run_server.bat

# أو يدوياً
pip install -r requirements.txt
python server.py
```

### Linux/Mac
```bash
# إعطاء صلاحيات التشغيل
chmod +x run_server.sh

# تشغيل السيرفر
./run_server.sh

# أو يدوياً
pip3 install -r requirements.txt
python3 server.py
```

### ✨ ملفات التشغيل المتاحة:
- **`start_server.bat`** - تشغيل سريع للسيرفر (Windows)
- **`run_server.bat`** - تشغيل كامل مع فحص المتطلبات (Windows)
- **`run_server.sh`** - تشغيل للأنظمة الأخرى (Linux/Mac)

## 🌐 الوصول للسيرفر

بعد تشغيل السيرفر، افتح المتصفح وانتقل إلى:
```
http://localhost:5000
```

## 📁 هيكل المشروع

```
📦 FoulaBook Scraper Server
├── 🐍 server.py                 # السيرفر الرئيسي
├── 📁 templates/
│   └── 🌐 index.html           # واجهة الويب
├── 📁 scraped_articles/         # المقالات المسحوبة
├── 📁 published_articles/       # المقالات المنشورة
├── 📁 logs/                     # ملفات السجلات
├── 📄 requirements.txt          # المكتبات المطلوبة
├── 🚀 run_server.bat           # تشغيل Windows
├── 🚀 run_server.sh            # تشغيل Linux/Mac
└── 📖 README_SERVER.md         # هذا الملف
```

## 🔧 API المتاحة

### سحب البيانات
```http
POST /api/scrape
Content-Type: application/json

{
  "url": "https://foulabook.com/ar/book/example"
}
```

### توليد المقالة
```http
POST /api/generate
Content-Type: application/json

{
  "book_data": {...},
  "copyright_protected": false
}
```

### الحصول على المقالات
```http
GET /api/articles
```

### عرض مقالة محددة
```http
GET /api/article/{filename}
```

### نشر مقالة
```http
POST /api/publish
Content-Type: application/json

{
  "filename": "article.json"
}
```

## 🔌 WebSocket Events

### الأحداث الواردة
- `scrape_progress` - تحديث شريط التقدم
- `scrape_complete` - انتهاء السحب بنجاح
- `scrape_error` - خطأ في السحب

### الأحداث الصادرة
- `connect` - الاتصال بالسيرفر
- `disconnect` - قطع الاتصال

## 📊 مراقبة النظام

### السجلات
- `logs/server.log` - سجل السيرفر الرئيسي
- `logs/scraper.log` - سجل عمليات السحب

### الملفات المحفوظة
- `scraped_articles/` - البيانات المسحوبة والمقالات المولدة
- `published_articles/` - المقالات المنشورة

## 🎮 كيفية الاستخدام

### 1. سحب كتاب جديد
1. أدخل رابط الكتاب من foulabook.com
2. اضغط "سحب البيانات"
3. راقب شريط التقدم
4. عدّل البيانات إذا لزم الأمر
5. اضغط "توليد المقالة"

### 2. إدارة المقالات
1. انتقل إلى قسم "إدارة المقالات"
2. اختر بين "المسحوبة" و "المنشورة"
3. استخدم أزرار العرض والنشر والحذف

### 3. معاينة المقالات
1. اضغط "عرض" على أي مقالة
2. ستفتح نافذة معاينة
3. يمكنك نسخ كود المقالة مباشرة

## ⚙️ الإعدادات المتقدمة

### ملف التكوين (`config.py`)
يمكنك تخصيص إعدادات السيرفر من خلال ملف `config.py`:

```python
# إعدادات السيرفر
SERVER_CONFIG = {
    'host': '0.0.0.0',      # عنوان IP للسيرفر
    'port': 5000,           # رقم المنفذ
    'debug': True,          # وضع التطوير
    'threaded': True        # دعم المعالجة المتوازية
}

# إعدادات المجلدات
DIRECTORIES = {
    'scraped_articles': 'scraped_articles',
    'published_articles': 'published_articles',
    'logs': 'logs',
    'templates': 'templates'
}

# إعدادات السحب
SCRAPING_CONFIG = {
    'timeout': 30,                    # مهلة الاتصال (ثانية)
    'max_retries': 3,                 # عدد المحاولات
    'delay_between_requests': 1,      # التأخير بين الطلبات
    'user_agent': 'Mozilla/5.0...'   # وكيل المستخدم
}
```

### تخصيص المنافذ
```bash
# تغيير المنفذ في config.py
SERVER_CONFIG['port'] = 8080
```

### تخصيص المجلدات
```bash
# تغيير مسارات المجلدات في config.py
DIRECTORIES['scraped_articles'] = 'my_scraped_books'
DIRECTORIES['published_articles'] = 'my_published_books'
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في تثبيت المكتبات
```bash
# حل للـ Windows
pip install --upgrade pip
pip install -r requirements.txt

# حل للـ Linux/Mac
pip3 install --upgrade pip
pip3 install -r requirements.txt
```

#### 2. خطأ في الاتصال
- تأكد من أن المنفذ 5000 غير مستخدم
- جرب منفذ آخر في ملف server.py

#### 3. مشاكل الترميز
- تأكد من حفظ الملفات بترميز UTF-8
- في Windows، استخدم `chcp 65001` قبل التشغيل

### فحص السجلات
```bash
# عرض آخر 50 سطر من السجل
tail -n 50 logs/server.log

# متابعة السجل في الوقت الفعلي
tail -f logs/server.log
```

## 🔒 الأمان

### نصائح الأمان
- لا تشغل السيرفر على شبكة عامة
- استخدم localhost فقط للتطوير
- احم ملفات الإعدادات من الوصول غير المصرح

### النسخ الاحتياطية
```bash
# نسخ احتياطي للمقالات
cp -r scraped_articles/ backup_scraped_$(date +%Y%m%d)/
cp -r published_articles/ backup_published_$(date +%Y%m%d)/
```

## 🚀 التطوير والتحسين

### إضافة ميزات جديدة
1. عدّل ملف `server.py` لإضافة API جديدة
2. حدّث `templates/index.html` لإضافة واجهة المستخدم
3. اختبر الميزات الجديدة محلياً

### تحسين الأداء
- استخدم قاعدة بيانات للمقالات الكبيرة
- أضف تخزين مؤقت للبيانات المسحوبة
- استخدم معالجة متوازية للسحب المتعدد

## 📞 الدعم والمساعدة

### في حالة مواجهة مشاكل:
1. راجع ملفات السجلات في مجلد `logs/`
2. تأكد من تحديث جميع المكتبات
3. جرب إعادة تشغيل السيرفر
4. تحقق من اتصال الإنترنت

### معلومات النظام
```bash
# معلومات Python
python --version
pip --version

# معلومات المكتبات
pip list | grep -E "(Flask|requests|beautifulsoup4)"
```

---

## 🎉 مثال عملي

### سيناريو كامل للاستخدام:

1. **تشغيل السيرفر**
   ```bash
   run_server.bat  # Windows
   ./run_server.sh # Linux/Mac
   ```

2. **فتح المتصفح**
   - انتقل إلى `http://localhost:5000`

3. **سحب كتاب**
   - أدخل: `https://foulabook.com/ar/book/example-book-pdf`
   - اضغط "سحب البيانات"
   - راقب شريط التقدم

4. **توليد المقالة**
   - عدّل البيانات إذا لزم الأمر
   - فعّل "محمي بحقوق الطبع والنشر" إذا لزم
   - اضغط "توليد المقالة"

5. **إدارة المقالة**
   - انتقل إلى قسم "المقالات المسحوبة"
   - اضغط "عرض" لمعاينة المقالة
   - اضغط "نسخ كود المقالة" لنسخ الكود
   - اضغط "نشر" لنقل المقالة إلى قسم المنشورة

---

**ملاحظة:** هذا السيرفر مصمم للاستخدام المحلي والتطوير. يرجى احترام حقوق الطبع والنشر واستخدام النظام بمسؤولية.
