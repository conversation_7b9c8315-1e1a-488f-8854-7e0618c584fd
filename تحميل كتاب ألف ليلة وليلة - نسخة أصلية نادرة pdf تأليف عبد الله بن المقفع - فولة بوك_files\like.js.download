$(document).ready(function(){

    var bookId = 0;
    // Mark as likess AJAX
      $('.mark-as-likes').on('click', function() {
        var bookId = $(this).attr('data-src');
        //var likesElem = $(this).('.like-count');
        var likesElem = document.getElementById("likes"+bookId).innerText;
       
         $.ajax({
        method: 'POST',
        url: urlLike,
        data: {bookId: bookId, _token: token}
        })
        .done(function(result) {

            //event.target.innerText = isLike ? event.target.innerText == 'Like' ? 'You like this post' : 'Like' : event.target.innerText == 'Dislike' ? 'You don\'t like this post' : 'Dislike';
            if(result.status === 1)
                {
                  // change likes image
                  if(result.currentValue === 'like')
                  {
                    /*toastr.options.rtl = true;
                    toastr.success("تم الإعجاب");*/
                    
                    document.getElementById("likes"+bookId).innerHTML = (parseInt(likesElem)) + 1;
                    
                    $(".mark-as-likes[data-src=" + bookId + "]").find('i').attr('data-original-title','إلغاء الإعجاب');
                    $(".mark-as-likes[data-src=" + bookId + "]").find('i').removeClass('fa-heart-o').addClass('fa-heart');
                  } else {
                    
                    /*toastr.options.rtl = true;
                    toastr.info("تم إلغاء الإعجاب");*/

                    document.getElementById("likes"+bookId).innerHTML = (parseInt(likesElem)) - 1;
                    
                    $(".mark-as-likes[data-src=" + bookId + "]").find('i').attr('data-original-title','إعجاب');
                    $(".mark-as-likes[data-src=" + bookId + "]").find('i').removeClass('fa-heart').addClass('fa-heart-o');
                  }
                }
        })
        .fail(function(xhr, status, error)
        {
            toastr.options.rtl = true;
            toastr.error("آسف, أنت بحاجة إلى تسجيل الدخول أولا");
        });
      });
});
