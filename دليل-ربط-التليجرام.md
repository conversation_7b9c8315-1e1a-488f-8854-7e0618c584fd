# 🤖 دليل ربط التليجرام بنظام سحب الكتب

## 📋 المتطلبات الأساسية

### 1. إنشاء بوت تليجرام
1. افتح تطبيق التليجرام
2. ابحث عن `@BotFather`
3. ابدأ محادثة معه بإرسال `/start`
4. أرسل `/newbot` لإنشاء بوت جديد
5. اختر اسماً للبوت (مثل: `IqraaKitab Bot`)
6. اختر معرفاً للبوت (يجب أن ينتهي بـ `bot` مثل: `iqraakitab_bot`)
7. ستحصل على **رمز البوت (Token)** - احتفظ به بأمان

### 2. إنشاء قناة أو مجموعة
1. أنشئ قناة جديدة في التليجرام
2. اجعل البوت مديراً في القناة مع صلاحيات النشر
3. احصل على **معرف القناة**:
   - إذا كانت القناة عامة: استخدم `@channel_username`
   - إذا كانت القناة خاصة: استخدم الرقم السالب مثل `-1001234567890`

## 🔧 طرق الحصول على معرف القناة

### الطريقة الأولى: للقنوات العامة
- إذا كان معرف القناة `@iqraakitab` فاستخدم `@iqraakitab`

### الطريقة الثانية: للقنوات الخاصة
1. أضف البوت `@userinfobot` إلى القناة
2. أرسل أي رسالة في القناة
3. سيرد البوت برقم معرف القناة
4. استخدم الرقم مع إشارة السالب

### الطريقة الثالثة: باستخدام API
1. أرسل رسالة في القناة
2. افتح الرابط: `https://api.telegram.org/bot[TOKEN]/getUpdates`
3. ابحث عن `"chat":{"id":-1001234567890}`

## ⚙️ تكوين النظام

### 1. في واجهة السيرفر
1. شغل السيرفر: `python server.py`
2. افتح المتصفح على `http://localhost:5000`
3. اضغط على أيقونة الإعدادات ⚙️ في الأعلى
4. في قسم "إعدادات التليجرام":
   - **رمز البوت**: ضع الرمز الذي حصلت عليه من BotFather
   - **معرف القناة**: ضع معرف القناة أو المجموعة
   - فعّل "تفعيل رفع التليجرام"
5. اضغط "اختبار اتصال التليجرام" للتأكد
6. احفظ الإعدادات

### 2. تكوين يدوي (اختياري)
يمكنك تعديل ملف `server_settings.json` مباشرة:

```json
{
  "telegram_token": "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz",
  "telegram_channel": "@iqraakitab",
  "telegram_enabled": true
}
```

## 🚀 كيفية الاستخدام

### 1. رفع كتاب على التليجرام
1. اسحب بيانات كتاب من foulabook.com
2. اضغط "تحميل الملف" لتحميل الكتاب محلياً
3. اضغط "رفع للتليجرام" 📤
4. سيتم رفع الكتاب مع معلوماته المنسقة

### 2. تنسيق الرسالة
سيتم إرسال الكتاب بالتنسيق التالي:

```
📚 تحميل كتاب ألف ليلة وليلة

👤 المؤلف: عبد الله بن المقفع
📖 السلسلة: لا توجد
🌐 اللغة: العربية
📄 الصفحات: 1342 صفحة
📝 الطبعة: غير محدد
🏢 دار النشر: غير محدد
📅 سنة النشر: 1860م
⭐ التقييم: 5/5
📁 الصيغة: PDF
💾 حجم الملف: 121.76 Mo
🏷️ التصنيف: الأدب العالمي

📋 الوصف:
نسخة أصلية نادرة من كتاب ألف ليلة وليلة...

🔗 موقعنا: https://ikitab.iqraatech.net
📱 قناتنا: @IqraaKitab

#كتب #قراءة #الأدب_العالمي #PDF
```

## 🔍 استكشاف الأخطاء

### خطأ "رمز البوت غير صحيح"
- تأكد من نسخ الرمز كاملاً من BotFather
- تأكد من عدم وجود مسافات إضافية

### خطأ "القناة غير موجودة"
- تأكد من إضافة البوت كمدير في القناة
- تأكد من صحة معرف القناة
- للقنوات الخاصة استخدم الرقم السالب

### خطأ "ليس لديك صلاحية"
- تأكد من أن البوت مدير في القناة
- أعطِ البوت صلاحيات النشر والإرسال

### خطأ "الملف كبير جداً"
- التليجرام يدعم ملفات حتى 50 ميجابايت للبوتات
- للملفات الأكبر، استخدم خدمات التخزين السحابي

## 📝 ملاحظات مهمة

1. **الأمان**: لا تشارك رمز البوت مع أحد
2. **الحدود**: التليجرام له حدود على عدد الرسائل (30 رسالة/ثانية)
3. **الملفات**: الحد الأقصى 50 ميجابايت للملف الواحد
4. **التنسيق**: يدعم HTML و Markdown في الرسائل

## 🆘 الدعم

إذا واجهت مشاكل:
1. تأكد من تشغيل السيرفر
2. تحقق من سجلات الأخطاء في وحدة التحكم
3. اختبر الاتصال من إعدادات السيرفر
4. تأكد من اتصال الإنترنت

## 🔄 التحديثات المستقبلية

- دعم رفع ملفات متعددة
- جدولة النشر
- إحصائيات التفاعل
- دعم المجموعات المتعددة
- نظام القوالب المخصصة
