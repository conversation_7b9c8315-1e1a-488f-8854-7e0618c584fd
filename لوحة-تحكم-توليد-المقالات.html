<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم توليد مقالات الكتب - قالب اقرأ كتاب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }
        
        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            transform: scale(1.2);
        }
        
        .copyright-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #dc3545;
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
            display: none;
        }
        
        .copyright-warning.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .copyright-warning h4 {
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .copyright-warning p {
            color: #856404;
            margin: 0;
            font-size: 14px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn.generate {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .btn.generate:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
        }
        
        .btn.clear {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            margin-top: 10px;
        }
        
        .btn.clear:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
        }
        
        .preview-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .preview-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #27ae60;
            padding-bottom: 10px;
        }
        
        .generated-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .copy-btn {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }
        
        .status-message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status-message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status-message.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .header p {
                font-size: 1em;
            }
        }
        
        .url-examples {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .url-examples h4 {
            color: #0c5460;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .url-examples ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .url-examples li {
            color: #0c5460;
            font-size: 12px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 لوحة تحكم توليد مقالات الكتب</h1>
            <p>أداة متقدمة لسحب محتوى الكتب وتوليد مقالات متوافقة مع قالب اقرأ كتاب</p>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h2>📝 بيانات الكتاب</h2>
                
                <div class="form-group">
                    <label for="sourceUrl">🔗 رابط المصدر:</label>
                    <input type="url" id="sourceUrl" placeholder="https://example.com/book-page">
                    <div class="url-examples">
                        <h4>أمثلة على المواقع المدعومة:</h4>
                        <ul>
                            <li>مكتبة نور - noor-book.com</li>
                            <li>كتب PDF - kutubpdf.net</li>
                            <li>مكتبة الكتب - maktabat-alkutub.com</li>
                            <li>أي موقع كتب آخر</li>
                        </ul>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="bookTitle">📚 عنوان الكتاب:</label>
                    <input type="text" id="bookTitle" placeholder="مثال: رياض الصالحين">
                </div>
                
                <div class="form-group">
                    <label for="bookAuthor">✍️ المؤلف:</label>
                    <input type="text" id="bookAuthor" placeholder="مثال: الإمام النووي">
                </div>
                
                <div class="form-group">
                    <label for="bookImage">🖼️ رابط صورة الكتاب:</label>
                    <input type="url" id="bookImage" placeholder="https://example.com/book-cover.jpg">
                </div>
                
                <div class="form-group">
                    <label for="bookSeries">📖 السلسلة:</label>
                    <input type="text" id="bookSeries" placeholder="مثال: سلسلة الكتب الإسلامية">
                </div>
                
                <div class="form-group">
                    <label for="bookLanguage">🌐 اللغة:</label>
                    <select id="bookLanguage">
                        <option value="العربية">العربية</option>
                        <option value="الإنجليزية">الإنجليزية</option>
                        <option value="الفرنسية">الفرنسية</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="bookPages">📄 عدد الصفحات:</label>
                    <input type="number" id="bookPages" placeholder="مثال: 500">
                </div>
                
                <div class="form-group">
                    <label for="bookEdition">🔢 الطبعة:</label>
                    <input type="text" id="bookEdition" placeholder="مثال: الطبعة الأولى">
                </div>
                
                <div class="form-group">
                    <label for="bookPublisher">🏢 دار النشر:</label>
                    <input type="text" id="bookPublisher" placeholder="مثال: دار المعرفة">
                </div>
                
                <div class="form-group">
                    <label for="bookYear">📅 سنة النشر:</label>
                    <input type="number" id="bookYear" placeholder="مثال: 2023" min="1900" max="2024">
                </div>
                
                <div class="form-group">
                    <label for="bookRating">⭐ التقييم:</label>
                    <select id="bookRating">
                        <option value="5/5">5/5 - ممتاز</option>
                        <option value="4.5/5">4.5/5 - جيد جداً</option>
                        <option value="4/5">4/5 - جيد</option>
                        <option value="3.5/5">3.5/5 - مقبول</option>
                        <option value="3/5">3/5 - متوسط</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="bookFormat">📁 صيغة الملف:</label>
                    <select id="bookFormat">
                        <option value="pdf">PDF</option>
                        <option value="epub">EPUB</option>
                        <option value="doc">DOC</option>
                        <option value="txt">TXT</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="bookSize">💾 حجم الملف:</label>
                    <input type="text" id="bookSize" placeholder="مثال: 10 ميجابايت">
                </div>
                
                <div class="form-group">
                    <label for="bookDescription">📋 نبذة عن الكتاب:</label>
                    <textarea id="bookDescription" placeholder="اكتب نبذة مختصرة عن محتوى الكتاب وأهميته..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="authorInfo">👤 معلومات عن المؤلف:</label>
                    <textarea id="authorInfo" placeholder="اكتب معلومات عن المؤلف ومسيرته العلمية..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="bookQuotes">💭 اقتباسات من الكتاب:</label>
                    <textarea id="bookQuotes" placeholder="اكتب اقتباسات مهمة من الكتاب، كل اقتباس في سطر منفصل..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="downloadLink">⬇️ رابط التحميل:</label>
                    <input type="url" id="downloadLink" placeholder="https://example.com/download-link">
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="copyrightProtected">
                    <label for="copyrightProtected">🔒 الكتاب محمي بحقوق الطبع والنشر</label>
                </div>
                
                <div class="copyright-warning" id="copyrightWarning">
                    <h4>⚠️ تنبيه حقوق الطبع والنشر</h4>
                    <p>عند تفعيل هذا الخيار، سيتم إنشاء المقالة مع رسالة تنبيه بدلاً من أزرار التحميل، وستظهر أيقونة تحذيرية على صورة الكتاب.</p>
                </div>
                
                <button class="btn" onclick="fetchBookData()">🔍 سحب البيانات من الرابط</button>
                <button class="btn generate" onclick="generateArticle()">✨ توليد المقالة</button>
                <button class="btn clear" onclick="clearForm()">🗑️ مسح البيانات</button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري معالجة البيانات...</p>
                </div>
                
                <div class="status-message" id="statusMessage"></div>
            </div>
            
            <div class="preview-section">
                <h2>👁️ معاينة المقالة المولدة</h2>
                <div class="generated-content" id="generatedContent">
                    سيتم عرض المقالة المولدة هنا بعد الضغط على زر "توليد المقالة"...
                </div>
                <button class="copy-btn" onclick="copyToClipboard()">📋 نسخ الكود</button>
            </div>
        </div>
    </div>

    <script>
        // إظهار/إخفاء تحذير حقوق الطبع والنشر
        document.getElementById('copyrightProtected').addEventListener('change', function() {
            const warning = document.getElementById('copyrightWarning');
            if (this.checked) {
                warning.classList.add('show');
            } else {
                warning.classList.remove('show');
            }
        });

        // وظيفة سحب البيانات من الرابط
        function fetchBookData() {
            const url = document.getElementById('sourceUrl').value;
            if (!url) {
                showStatus('يرجى إدخال رابط المصدر أولاً', 'error');
                return;
            }

            showLoading(true);
            showStatus('جاري سحب البيانات من الرابط...', 'success');

            // محاكاة عملية سحب البيانات
            setTimeout(() => {
                // هنا يمكن إضافة كود حقيقي لسحب البيانات من المواقع
                showLoading(false);
                showStatus('تم سحب البيانات بنجاح! يرجى مراجعة البيانات وتعديلها حسب الحاجة.', 'success');
                
                // ملء البيانات التجريبية
                document.getElementById('bookTitle').value = 'كتاب تجريبي';
                document.getElementById('bookAuthor').value = 'مؤلف تجريبي';
                document.getElementById('bookPages').value = '300';
                document.getElementById('bookYear').value = '2023';
                document.getElementById('bookDescription').value = 'هذا كتاب تجريبي تم سحب بياناته من الرابط المحدد.';
            }, 2000);
        }

        // وظيفة توليد المقالة
        function generateArticle() {
            const data = collectFormData();
            if (!validateData(data)) {
                return;
            }

            showLoading(true);
            showStatus('جاري توليد المقالة...', 'success');

            setTimeout(() => {
                const article = generateArticleHTML(data);
                document.getElementById('generatedContent').textContent = article;
                showLoading(false);
                showStatus('تم توليد المقالة بنجاح!', 'success');
            }, 1500);
        }

        // جمع بيانات النموذج
        function collectFormData() {
            return {
                title: document.getElementById('bookTitle').value,
                author: document.getElementById('bookAuthor').value,
                image: document.getElementById('bookImage').value,
                series: document.getElementById('bookSeries').value,
                language: document.getElementById('bookLanguage').value,
                pages: document.getElementById('bookPages').value,
                edition: document.getElementById('bookEdition').value,
                publisher: document.getElementById('bookPublisher').value,
                year: document.getElementById('bookYear').value,
                rating: document.getElementById('bookRating').value,
                format: document.getElementById('bookFormat').value,
                size: document.getElementById('bookSize').value,
                description: document.getElementById('bookDescription').value,
                authorInfo: document.getElementById('authorInfo').value,
                quotes: document.getElementById('bookQuotes').value,
                downloadLink: document.getElementById('downloadLink').value,
                copyrightProtected: document.getElementById('copyrightProtected').checked
            };
        }

        // التحقق من صحة البيانات
        function validateData(data) {
            if (!data.title) {
                showStatus('يرجى إدخال عنوان الكتاب', 'error');
                return false;
            }
            if (!data.author) {
                showStatus('يرجى إدخال اسم المؤلف', 'error');
                return false;
            }
            return true;
        }

        // توليد HTML المقالة
        function generateArticleHTML(data) {
            const copyrightAttr = data.copyrightProtected ? ' data-copyright="protected"' : '';
            const imageUrl = data.image || 'https://via.placeholder.com/300x400/007bff/ffffff?text=' + encodeURIComponent(data.title);
            
            let article = `<div class="ibookinfobox">
    <div class="iBICover"${copyrightAttr}><img alt="${data.title}" border="0" data-original-height="800" data-original-width="512" height="100%" src="${imageUrl}" title="${data.title}" width="100%" /></div>
  <div class="iBIDetails">
    <div class="iBItable iBName"><span class="ititle"><b>الكتاب:</b></span> ${data.title}<br /></div>
    <div class="iBItable iBAuthor"><p><span class="ititle"><b>المؤلف:</b> ${data.author}</span></p></div>
    <div class="iBItable iBSeries"><span class="ititle"><b>السلسلة:</b></span> ${data.series || 'لا توجد'}</div>
    <div class="iBItable iBLang"><span class="ititle"><b>اللغة:</b></span> ${data.language}</div>
    <div class="iBItable iBPages"><span class="ititle"><b>الصفحات:</b></span> ${data.pages} صفحة<br /></div>
    <div class="iBItable iBEdition"><span class="ititle"><b>الطبعة:</b></span> ${data.edition || 'غير محدد'}</div>
    <div class="iBItable iBPublisher"><span class="ititle"><b>دار النشر:</b></span> ${data.publisher || 'غير محدد'}</div>
    <div class="iBItable iBYear"><span class="ititle"><b>سنة النشر:</b></span> ${data.year}</div>
    <div class="iBItable iBRate"><p><span class="ititle"><b>تقييم جود ريدز:</b> ${data.rating}</span></p></div>
    <div class="iBItable iBType"><span class="ititle"><b>الصيغة:</b></span> ${data.format}</div>
    <div class="iBItable iBSize"><span class="ititle"><b>حجم الملف:</b></span> ${data.size || 'غير محدد'}</div>
  </div>
</div>
<br />
<span><!--more--></span>`;

            // إضافة نبذة عن الكتاب
            if (data.description) {
                article += `
<div class="iqraa-message info imtitle iAboutB">
  <h2 style="text-align: right;">نبذة عن الكتاب</h2>
  <p>${data.description}</p>
</div>
<br />`;
            }

            // إضافة فهرس تجريبي
            article += `
<button class="iqraacoll iBIndex" type="button"><svg class="iBIndexbefore" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13.744 8s1.522-8-3.335-8h-8.409v24h20v-13c0-3.419-5.247-3.745-8.256-3zm4.256 11h-12v-1h12v1zm0-3h-12v-1h12v1zm0-3h-12v-1h12v1zm-3.432-12.925c2.202 1.174 5.938 4.883 7.432 6.881-1.286-.9-4.044-1.657-6.091-1.179.222-1.468-.185-4.534-1.341-5.702z"></path></svg><h2>فهرس الكتاب</h2><svg class="iBIndexafter" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"></path></svg></button><div class="collcontent iBIndexdetail">
  <p>فهرس الكتاب:<br /></p>
  <ol style="text-align: right;"><li>المقدمة</li><li>الفصل الأول</li><li>الفصل الثاني</li><li>الفصل الثالث</li><li>الخاتمة</li></ol>
</div>
<br />`;

            // إضافة معلومات المؤلف
            if (data.authorInfo) {
                article += `
<div class="iqraa-message info imtitle bookauthor">
  <h2 style="text-align: right;">معلومات عن الكاتب</h2>
  <div class="iALAvatarImage"><img class="iALAvatar" alt="صورة مؤلف الكتاب" src="https://lh3.googleusercontent.com/--NHUHORGgKI/Y8sDAvqyVhI/AAAAAAAAAwg/NE5pqiucNjEtjYgWDVlI9rarSKL-XdFHACNcBGAsYHQ/s16000-rw-e360/iAuthorAvatar-1.webp" title="صورة مؤلف الكتاب" /></div>
  <p>${data.authorInfo}</p>
  <a class="bookauthorBTN button" href="#">الكتب الأخرى للكاتب</a>
</div>
<br />`;
            }

            // إضافة الاقتباسات
            if (data.quotes) {
                article += `
<h2 style="text-align: right;">أفكار واقتباسات من الكتاب</h2>
<p>هذه مجموعة من الأفكار والمعلومات التي تم اقتباسها من الكتاب:</p><p></p>`;
                
                const quotes = data.quotes.split('\n').filter(q => q.trim());
                quotes.forEach(quote => {
                    article += `<blockquote>${quote.trim()}</blockquote><p></p>`;
                });
            }

            // إضافة أزرار التحميل أو رسالة حقوق الطبع والنشر
            if (data.copyrightProtected) {
                article += `
<!-- رسالة حماية حقوق الطبع والنشر -->
<div class="iqraa-message warning copyright-notice" style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); border: 2px solid #dc3545; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
    <h3 style="color: #dc3545; margin-bottom: 15px; font-size: 22px; font-weight: bold;">⚠️ تنبيه حقوق الطبع والنشر</h3>
    <p style="color: #856404; margin-bottom: 15px; line-height: 1.8; font-size: 16px;">
        <strong>نعتذر، هذا الكتاب غير متاح حاليًا للتحميل أو القراءة لأن المؤلف أو الناشر لا يسمح بذلك في الوقت الحالي.</strong>
    </p>
    <p style="color: #856404; margin-bottom: 15px; line-height: 1.6; font-size: 14px;">
        نحن نحترم حقوق الملكية الفكرية ونلتزم بقوانين حقوق الطبع والنشر.
    </p>
    <div style="background: rgba(220, 53, 69, 0.1); padding: 15px; border-radius: 8px; margin-top: 15px;">
        <h4 style="color: #dc3545; margin-bottom: 10px; font-size: 16px;">📚 يمكنك الحصول على هذا الكتاب من خلال:</h4>
        <ul style="color: #856404; text-align: right; margin: 0; padding-right: 20px; line-height: 1.8;">
            <li>شراء نسخة ورقية من المكتبات</li>
            <li>المكتبات العامة والجامعية</li>
            <li>التواصل مع الناشر مباشرة</li>
            <li>المتاجر الإلكترونية المعتمدة</li>
        </ul>
    </div>
</div>`;
            } else {
                const downloadUrl = data.downloadLink || 'https://ikitab.iqraatech.net';
                article += `
<div class="isdbtn button download" id="isdbtn">تحميل الكتاب</div>
<div class="idownloadmodal" id="idownloadmodal">
  <div class="idownloadmodalcontainer">
    <div class="idownloadmodaltop"><div class="idownloadmodalCloseBTN" id="CloseBTN"></div><div class="idownloadmodaltitle">تحميل الكتاب</div></div>
    <div id="ipdAd"></div>
    <div class="inowdownloadinner">
      <a class="button download" href="${downloadUrl}" id="inowdownload" target="_blank">أضغط لتحميل الكتاب</a>
    </div>
  </div>
</div>`;
            }

            article += `
<br />
<br />
<div class="iBLikeMessage"><svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="m12 5.72c-2.624-4.517-10-3.198-10 2.461 0 3.725 4.345 7.727 9.303 *************.446.283.697.283s.503-.094.697-.283c4.977-4.831 9.303-8.814 9.303-12.54 0-5.678-7.396-6.944-10-2.461z"></path></svg><p>إذا كان الكتاب قد نال إعجابكم فلا تترددوا في الضغط على زر القلب الموجود في الأسفل</p><svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="m12 5.72c-2.624-4.517-10-3.198-10 2.461 0 3.725 4.345 7.727 9.303 *************.446.283.697.283s.503-.094.697-.283c4.977-4.831 9.303-8.814 9.303-12.54 0-5.678-7.396-6.944-10-2.461z"></path></svg></div>`;

            // إضافة السكريبت إذا لم يكن محمي بحقوق الطبع والنشر
            if (!data.copyrightProtected) {
                article += `

<!--[ أكواد خاصة بأداة التحميل العائم يجب عدم التلاعب بها لضمان عمل الأداة بشكل سليم ]-->
<script>/*<![CDATA[*/var _0x53b5e1=_0x568f;function _0x568f(t,n){var r=_0x34a4();return _0x568f=function(n,e){var a=r[n-=422];if(void 0===_0x568f.pKfuWp){var f=function(t){for(var n,r,e="",a="",i=e+f,o=0,c=0;r=t.charAt(c++);~r&&(n=o%4?64*n+r:r,o++%4)?e+=i.charCodeAt(c+10)-10!=0?String.fromCharCode(255&n>>(-2*o&6)):o:0)r="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=".indexOf(r);for(var u=0,s=e.length;u<s;u++)a+="%"+("00"+e.charCodeAt(u).toString(16)).slice(-2);return decodeURIComponent(a)};_0x568f.NFBMpM=f,t=arguments,_0x568f.pKfuWp=!0}var i=r[0],o=n+i,c=t[o];if(c)a=c;else{var u=function(t){this.YoeVLP=t,this.IVrfrZ=[1,0,0],this.SFXOoi=function(){return"newState"},this.MOsbRF="\\w+ *\\(\\) *{\\w+ *",this.CWpKlI="['|\"].+['|\"];? *}"};u.prototype.pSVDLb=function(){var t=new RegExp(this.MOsbRF+this.CWpKlI).test(this.SFXOoi.toString())?--this.IVrfrZ[1]:--this.IVrfrZ[0];return this.gWqApy(t)},u.prototype.gWqApy=function(t){return Boolean(~t)?this.wvAjFF(this.YoeVLP):t},u.prototype.wvAjFF=function(t){for(var n=0,r=this.IVrfrZ.length;n<r;n++)this.IVrfrZ.push(Math.round(Math.random())),r=this.IVrfrZ.length;return t(this.IVrfrZ[0])},new u(_0x568f).pSVDLb(),a=_0x568f.NFBMpM(a),t[o]=a}return a},_0x568f(t,n)}!function(t,n){for(var r=_0x568f,e=_0x34a4();;)try{if(457417===-parseInt(r(427))/1+-parseInt(r(433))/2+-parseInt(r(443))/3+-parseInt(r(449))/4*(parseInt(r(441))/5)+-parseInt(r(425))/6+parseInt(r(445))/7+parseInt(r(438))/8)break;e.push(e.shift())}catch(t){e.push(e.shift())}}();var _0x2cf5c2=function(){var t=!0;return function(n,r){var e=t?function(){var t=_0x568f;if(r){var e=r[t(436)](n,arguments);return r=null,e}}:function(){};return t=!1,e}}(),_0x1fc1ec=_0x2cf5c2(this,(function(){var t=_0x568f;return _0x1fc1ec[t(446)]()[t(426)](t(437))[t(446)]()[t(431)](_0x1fc1ec).search("(((.+)+)+)+$")}));_0x1fc1ec();var lazyiPDBtn=!1;function _0x34a4(){var t=["B25JBgLJAW","mta2odK5nhH3BuzXAq","Aw5Uzxjive1m","yM9KEq","yxbWBhK","kcGOlISPkYKRksSK","mJiXnJK4mZjqyLLmAxy","zg9JDw1LBNrfBgvTzw50","mtzWEa","nwnoDgPcEa","AxnKyNrU","mtCYodKXmKLhshbpwa","icdyQ9IN2yBzITIPlI4UlIa","nZu2mtqWDKHfyKLe","Dg9tDhjPBMC","2lpzITIQ2yuG2kFzHnII2yyG2kxyUDIV2kFyRYdyP9Me2yxzHnMb","y3jLyxrLrwXLBwvUDa","mZaZmZe4oen4Bw1Asq","CgfYzw50tM9Kzq","ywrKrxzLBNrmAxn0zw5LCG","C3r5Bgu","C2nYB2XSvg9W","z2v0rwXLBwvUDej5swq","C2nYB2XS","C3bHBG","mJeWmde2ogTMve5PsW","C2vHCMnO","mJaYnZa2BK5tA0f1","2yxzHIdzGDI22ytzGYdyP9Mg2kRyUnIXica","CMvWBgfJzunOAwXK","zM9UDa","y29UC3rYDwn0B3i"];return(_0x34a4=function(){return t})()}window[_0x53b5e1(451)](_0x53b5e1(423),(function(){var t,n,r,e,a=_0x53b5e1;(0!=document[a(439)][a(453)]&&!1===lazyiPDBtn||0!=document[a(435)][a(453)]&&!1===lazyiPDBtn)&&(n=document[a(422)]("inowdownload"),r=ipdseconds,(e=document[a(448)](a(424)))[a(434)]=a(447),e[a(452)][a(430)]=a(440),n[a(450)][a(429)](e,n),document.getElementById(a(442))[a(432)]=function(){t=setInterval((function(){var a=_0x568f;--r<0?(e[a(450)][a(429)](n,e),clearInterval(t)):e[a(434)]=a(428)+r.toString()+a(444)}),1e3)},lazyiPDBtn=!0)}),!0);/*]]>*/</script>`;
            }

            return article;
        }

        // مسح النموذج
        function clearForm() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                document.querySelectorAll('input, textarea, select').forEach(element => {
                    if (element.type === 'checkbox') {
                        element.checked = false;
                    } else {
                        element.value = '';
                    }
                });
                document.getElementById('generatedContent').textContent = 'سيتم عرض المقالة المولدة هنا بعد الضغط على زر "توليد المقالة"...';
                document.getElementById('copyrightWarning').classList.remove('show');
                showStatus('تم مسح جميع البيانات', 'success');
            }
        }

        // نسخ المحتوى إلى الحافظة
        function copyToClipboard() {
            const content = document.getElementById('generatedContent').textContent;
            if (content && content !== 'سيتم عرض المقالة المولدة هنا بعد الضغط على زر "توليد المقالة"...') {
                navigator.clipboard.writeText(content).then(() => {
                    showStatus('تم نسخ الكود بنجاح!', 'success');
                }).catch(() => {
                    // طريقة بديلة للنسخ
                    const textArea = document.createElement('textarea');
                    textArea.value = content;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showStatus('تم نسخ الكود بنجاح!', 'success');
                });
            } else {
                showStatus('لا يوجد محتوى للنسخ', 'error');
            }
        }

        // إظهار/إخفاء حالة التحميل
        function showLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }

        // إظهار رسالة الحالة
        function showStatus(message, type) {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = message;
            statusElement.className = `status-message ${type} show`;
            
            setTimeout(() => {
                statusElement.classList.remove('show');
            }, 5000);
        }

        // تهيئة القيم الافتراضية
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('bookLanguage').value = 'العربية';
            document.getElementById('bookRating').value = '5/5';
            document.getElementById('bookFormat').value = 'pdf';
            document.getElementById('bookYear').value = new Date().getFullYear();
        });
    </script>
</body>
</html>
