/**
 * نظام حماية حقوق الطبع والنشر لقالب اقرأ كتاب
 * Copyright Protection System for Iqraa Kitab Template
 * 
 * هذا الملف يحتوي على جميع وظائف حماية حقوق الطبع والنشر
 * This file contains all copyright protection functions
 */

// إعدادات النظام
const CopyrightProtection = {
    // رسائل التنبيه
    messages: {
        title: '⚠️ تنبيه حقوق الطبع والنشر',
        mainMessage: 'نعتذر، هذا الكتاب غير متاح حاليًا للتحميل أو القراءة لأن المؤلف أو الناشر لا يسمح بذلك في الوقت الحالي.',
        subMessage: 'نحن نحترم حقوق الملكية الفكرية ونلتزم بقوانين حقوق الطبع والنشر.',
        buttonTooltip: 'هذا الكتاب محمي بحقوق الطبع والنشر'
    },
    
    // أيقونة حقوق الطبع والنشر (SVG)
    copyrightIcon: `
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
        </svg>
    `,
    
    // محددات العناصر
    selectors: {
        protectedBooks: '[data-copyright="protected"]',
        downloadButtons: '.isdbtn, .iqraarbbtn, .button.download, .button.read, .idownloadmodal',
        bookLinks: 'a[href*="book"], a[href*="كتاب"], .iPostThumbLink',
        modalClass: 'icopyright-modal',
        iconClass: 'icopyright-icon'
    },

    // تهيئة النظام
    init: function() {
        this.createModal();
        this.addCopyrightIcons();
        this.protectDownloadButtons();
        this.addEventListeners();
        this.addKeyboardSupport();
        console.log('Copyright Protection System initialized');
    },

    // إنشاء النافذة المنبثقة
    createModal: function() {
        if (document.querySelector('.' + this.selectors.modalClass)) {
            return; // النافذة موجودة بالفعل
        }

        const modal = document.createElement('div');
        modal.className = this.selectors.modalClass;
        modal.innerHTML = `
            <div class="icopyright-content">
                <button class="icopyright-close" onclick="CopyrightProtection.closeModal()" aria-label="إغلاق">&times;</button>
                <h3>${this.messages.title}</h3>
                <p>${this.messages.mainMessage}</p>
                <p>${this.messages.subMessage}</p>
            </div>
        `;
        document.body.appendChild(modal);
    },

    // إضافة أيقونات حقوق الطبع والنشر
    addCopyrightIcons: function() {
        const protectedElements = document.querySelectorAll(this.selectors.protectedBooks);
        
        protectedElements.forEach((element) => {
            // تجنب إضافة أيقونة مكررة
            if (element.querySelector('.' + this.selectors.iconClass)) {
                return;
            }

            const icon = document.createElement('div');
            icon.className = this.selectors.iconClass + ' show';
            icon.innerHTML = this.copyrightIcon;
            icon.title = this.messages.buttonTooltip;
            icon.setAttribute('aria-label', this.messages.buttonTooltip);
            icon.setAttribute('role', 'button');
            icon.setAttribute('tabindex', '0');

            // تحديد موضع الأيقونة
            this.positionIcon(element, icon);

            // إضافة حدث النقر
            icon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showModal();
            });

            // إضافة دعم لوحة المفاتيح
            icon.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.showModal();
                }
            });

            element.appendChild(icon);
        });
    },

    // تحديد موضع الأيقونة
    positionIcon: function(element, icon) {
        const elementStyle = window.getComputedStyle(element);
        if (elementStyle.position === 'static') {
            element.style.position = 'relative';
        }
    },

    // حماية أزرار التحميل
    protectDownloadButtons: function() {
        const buttons = document.querySelectorAll(this.selectors.downloadButtons);
        
        buttons.forEach((button) => {
            // التحقق من وجود كتاب محمي في الصفحة
            const isProtectedPage = document.querySelector(this.selectors.protectedBooks);
            
            if (isProtectedPage) {
                this.disableButton(button);
            }

            // إضافة حدث النقر
            button.addEventListener('click', (e) => {
                if (isProtectedPage) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showModal();
                    return false;
                }
            });
        });
    },

    // تعطيل الزر
    disableButton: function(button) {
        button.style.opacity = '0.6';
        button.style.cursor = 'not-allowed';
        button.title = this.messages.buttonTooltip;
        button.setAttribute('data-protected', 'true');
        button.setAttribute('aria-disabled', 'true');
    },

    // إضافة مستمعي الأحداث
    addEventListeners: function() {
        // إغلاق النافذة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const modal = document.querySelector('.' + this.selectors.modalClass);
            if (modal && e.target === modal) {
                this.closeModal();
            }
        });

        // حماية الروابط للكتب المحمية
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link) {
                const protectedElement = link.closest(this.selectors.protectedBooks);
                if (protectedElement) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showModal();
                }
            }
        });
    },

    // إضافة دعم لوحة المفاتيح
    addKeyboardSupport: function() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    },

    // عرض النافذة المنبثقة
    showModal: function() {
        const modal = document.querySelector('.' + this.selectors.modalClass);
        if (modal) {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
            
            // التركيز على زر الإغلاق للوصولية
            const closeButton = modal.querySelector('.icopyright-close');
            if (closeButton) {
                closeButton.focus();
            }

            // إضافة تأثير صوتي (اختياري)
            this.playNotificationSound();
        }
    },

    // إغلاق النافذة المنبثقة
    closeModal: function() {
        const modal = document.querySelector('.' + this.selectors.modalClass);
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = 'auto';
        }
    },

    // تشغيل صوت تنبيه (اختياري)
    playNotificationSound: function() {
        // يمكن إضافة صوت تنبيه هنا إذا رغبت
        // const audio = new Audio('notification-sound.mp3');
        // audio.play().catch(() => {}); // تجاهل الأخطاء
    },

    // إضافة شارة "محمي" للكتب
    addProtectedBadge: function(element) {
        if (element.querySelector('.copyright-badge')) {
            return; // الشارة موجودة بالفعل
        }

        const badge = document.createElement('div');
        badge.className = 'copyright-badge';
        badge.textContent = 'محمي';
        badge.title = this.messages.buttonTooltip;
        element.appendChild(badge);
    },

    // فحص الكتب المحمية ديناميكياً
    checkForProtectedBooks: function() {
        const protectedBooks = document.querySelectorAll(this.selectors.protectedBooks);
        
        protectedBooks.forEach((book) => {
            // إضافة الأيقونة إذا لم تكن موجودة
            if (!book.querySelector('.' + this.selectors.iconClass)) {
                this.addCopyrightIcons();
            }
            
            // إضافة الشارة
            this.addProtectedBadge(book);
        });
    },

    // تحديث النظام (للاستخدام مع AJAX)
    refresh: function() {
        this.checkForProtectedBooks();
        this.protectDownloadButtons();
        console.log('Copyright Protection System refreshed');
    },

    // إزالة الحماية (للاختبار فقط)
    removeProtection: function() {
        const icons = document.querySelectorAll('.' + this.selectors.iconClass);
        const badges = document.querySelectorAll('.copyright-badge');
        const modal = document.querySelector('.' + this.selectors.modalClass);
        
        icons.forEach(icon => icon.remove());
        badges.forEach(badge => badge.remove());
        if (modal) modal.remove();
        
        console.log('Copyright Protection removed');
    },

    // إحصائيات الحماية
    getStats: function() {
        const protectedBooks = document.querySelectorAll(this.selectors.protectedBooks).length;
        const protectedButtons = document.querySelectorAll('[data-protected="true"]').length;
        
        return {
            protectedBooks: protectedBooks,
            protectedButtons: protectedButtons,
            isActive: protectedBooks > 0
        };
    }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تفعيل الميزة
    const isEnabled = document.body.getAttribute('data-copyright-protection') === 'enabled' ||
                     document.querySelector('[data-copyright="protected"]');
    
    if (isEnabled) {
        CopyrightProtection.init();
    }
});

// إعادة تهيئة النظام عند تحديث المحتوى (للمواقع التي تستخدم AJAX)
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        let shouldRefresh = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const addedNodes = Array.from(mutation.addedNodes);
                const hasProtectedContent = addedNodes.some(node => 
                    node.nodeType === 1 && 
                    (node.matches && node.matches('[data-copyright="protected"]') ||
                     node.querySelector && node.querySelector('[data-copyright="protected"]'))
                );
                
                if (hasProtectedContent) {
                    shouldRefresh = true;
                }
            }
        });
        
        if (shouldRefresh) {
            setTimeout(() => CopyrightProtection.refresh(), 100);
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// تصدير النظام للاستخدام العام
window.CopyrightProtection = CopyrightProtection;
